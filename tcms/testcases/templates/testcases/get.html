{% extends "base.html" %}
{% load i18n %}
{% load static %}
{% load comments %}
{% load extra_filters %}

{% block title %}TC-{{ object.pk }}: {{ object.summary }}{% endblock %}
{% block page_id %}page-testcases-get{% endblock %}
{% block body_class %}cards-pf{% endblock %}

{% block contents %}
<div class="container-cards-pf">
    <!-- Important:  if you need to nest additional .row within a .row.row-cards-pf, do *not* use .row-cards-pf on the nested .row  -->
    <h1 class="col-md-12 kiwi-margin-top-0">
        <span id="test_case_pk"
            data-pk="{{ object.pk }}"
            data-perm-remove-tag="{{ perms.testcases.delete_testcasetag }}"
            data-perm-remove-component="{{ perms.testcases.delete_testcasecomponent }}"
            data-perm-remove-plan="{{ perms.testcases.delete_testcaseplan }}"
            data-perm-remove-bug="{{ perms.testcases.delete_bug }}"
        >TC-{{ object.pk }}:</span> {{ object.summary }}
    </h1>

    <div class="row row-cards-pf">
        <div class="col-xs-12 col-sm-12 col-md-3">
            <div class="card-pf card-pf-accented card-pf-aggregate-status">
                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa pficon-user"></span>{% trans 'Author' %}:
                    <a href="{% url "tcms-profile" object.author.pk %}">{{ object.author.username }}</a>
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-search"></span>{% trans 'Default tester' %}:
                    {% if object.default_tester  %}
                    <a href="{% url "tcms-profile" object.default_tester.pk %}">{{ object.default_tester.username }}</a>
                    {% else %}
                    -
                    {% endif %}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span id="product_pk"
                        data-pk="{{ object.category.product.pk }}"
                        class="fa fa-shopping-cart"></span>{% trans 'Product' %}:
                    {{ object.category.product }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-tag"></span>{% trans 'Category' %}:
                    {{ object.category }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa
                        {% if object.case_status.is_confirmed %}
                            fa-check-square
                        {% else %}
                            fa-times
                        {% endif %}"></span>{% trans 'Status' %}:
                    {{ object.case_status }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-hourglass"></span>{% trans 'Priority' %}:
                    {{ object.priority }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-calendar"></span>{{ object.create_date }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-clock-o"></span>{% trans 'Setup duration' %}:
                    {{ object.setup_duration|default:"-" }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-clock-o"></span>{% trans 'Testing duration' %}:
                    {{ object.testing_duration|default:"-" }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-clock-o"></span>{% trans 'Expected duration' %}:
                    {{ object.expected_duration }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa
                        {% if object.is_automated %}
                            fa-cog
                        {% else %}
                            fa-hand-paper-o
                        {% endif %}"></span>{% trans 'Automated' %}:
                    {{ object.is_automated }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-terminal"></span>{% trans 'Script' %}:
                    {{ object.script|default:'-' }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-terminal"></span>{% trans 'Arguments' %}:
                    {{ object.arguments|default:'-' }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-file-text"></span>{% trans 'Requirements' %}:
                    {{ object.requirement|default:'-' }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-link"></span>{% trans 'Reference link' %}:
                    {% if object.extra_link %}
                        <a href="{{ object.extra_link }}">{{ object.extra_link }}</a>
                    {% else %}
                        -
                    {% endif %}
                </h2>

                <div class="card-pf-body"></div>
            </div>
        </div>

        <div class="col-xs-12 col-sm-12 col-md-6">
            <div class="card-pf card-pf-accented">
                <div class="card-pf-body">
                    <div class="markdown-text">
                        {{ object.text|markdown2html }}
                    </div>

                    <p>
                        <strong>{% trans 'Notes' %}:</strong>
                        {{ object.notes }}
                    </p>
                </div>
            </div>
        </div>

        <div class="col-xs-12 col-sm-12 col-md-3">
            {% include "include/properties_card.html" %}
        </div>
    </div> <!-- row -->

    <div class="row row-cards-pf">
        <div class="col-xs-12 col-sm-12 col-md-12">
            {% include 'include/tc_executions.html' with show_bugs=True %}
        </div>
    </div>

    <div class="row row-cards-pf">
        {% trans "Bugs" as bugs_heading %}
        {% include "include/bugs_table.html" with heading=bugs_heading class="bugs" %}

        <div class="col-xs-12 col-sm-6 col-md-8">
            <div class="card-pf card-pf-accented">
                <h2 class="card-pf-title">
                    <span class="fa pficon-topology"></span>
                    {% trans 'Test plans' %}
                </h2>

                <div class="card-pf-body">
                    <table class="table" id="plans">
                        <thead>
                            <tr>
                                <th>{% trans 'ID' %}</th>
                                <th>{% trans 'Name' %}</th>
                                <th>{% trans 'Author' %}</th>
                                <th>{% trans 'Type' %}</th>
                                <th>{% trans 'Product' %}</th>
                                <th></th>
                            </tr>
                        </thead>

                        {% if perms.testcases.add_testcaseplan %}
                        <tfoot>
                            <tr>
                                <th colspan='5'>
                                    <div class="input-group input-group-sm">
                                        <input type="text" class="form-control typeahead" id="input-add-plan">
                                    </div>
                                </th>
                                <th>
                                    <a href="#plans" id="btn-add-plan" title="{% trans 'Add' %}">
                                        <span class="fa fa-plus"></span>
                                    </a>
                                </th>
                            </tr>
                        </tfoot>
                        {% endif %}

                    </table>
                </div>
            </div>
        </div>

    </div>

    <div class="row row-cards-pf">
        <div class="col-xs-12 col-sm-6 col-md-3">
            {% include 'include/tags_card.html' with add_perm=perms.testcases.add_testcasetag %}
        </div>

        <div class="col-xs-12 col-sm-6 col-md-3">
            <div class="card-pf card-pf-accented">
                <h2 class="card-pf-title">
                    <span class="fa pficon-build"></span>
                    {% trans 'Components' %}
                </h2>

                <div class="card-pf-body">
                    <table class="table" id="components">
                        <thead>
                            <tr>
                                <th>{% trans 'Name' %}</th>
                                <th></th>
                            </tr>
                        </thead>

                        {% if perms.testcases.add_testcasecomponent %}
                        <tfoot>
                            <tr>
                                <th>
                                    <div class="input-group input-group-sm mb-3">
                                        <input type="text" class="form-control typeahead" id="id_components">
                                    </div>
                                </th>
                                <th>
                                    <a href="#components" id="add-component" title="{% trans 'Add' %}">
                                        <span class="fa fa-plus"></span>
                                    </a>
                                </th>
                            </tr>
                        </tfoot>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>

        <div class="col-xs-12 col-sm-6 col-md-6">
            {% include 'include/attachments.html' %}
        </div>

    </div><!-- /row -->
</div>
{% endblock %}
