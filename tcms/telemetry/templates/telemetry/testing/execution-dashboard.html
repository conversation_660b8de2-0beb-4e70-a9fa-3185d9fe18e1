{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Execution Dashboard" %}{% endblock %}
{% block page_id %}page-telemetry-execution-dashboard{% endblock %}

{% block contents %}
    <div class="container-fluid container-cards-pf">
        {% include "telemetry/include/filters.html" %}

        <form class="form-horizontal">
            {% csrf_token %}
            <div class="form-group">
                <label class="col-md-1 col-lg-1" for="id_include_child_tps">{% trans "Child TPs" %}</label>
                <div class="col-md-3 col-lg-3">
                    <input class="bootstrap-switch" id="id_include_child_tps" type="checkbox"
                        data-on-text="{% trans 'Yes' %}" data-off-text="{% trans 'No' %}">
                </div>
            </div>
        </form>

        <table class="table table-striped table-bordered table-hover" id="resultsTable">
            <thead>
                <tr>
                    <th>{% trans "ID" %}</th>
                    <th>{% trans "Summary" %}</th>
                    <th>{% trans "Status" %}</th>
                    <th>{% trans "Product" %}</th>
                    <th>{% trans "Build" %}</th>
                    <th>{% trans "Components" %}</th>

                    <th>{% trans "Default tester" %}/{% trans "TC" %}</th>
                    <th>{% trans "Default tester" %}/{% trans "TR" %}</th>

                    <th>{% trans "Assignee" %}</th>
                    <th style="white-space: nowrap">{% trans "Tested by" %}</th>
                    <th>{% trans "Started at" %}</th>
                    <th>{% trans "Finished at" %}</th>
                </tr>
            </thead>
        </table>
    </div>
{% endblock %}
