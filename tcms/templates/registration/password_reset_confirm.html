{% extends "base.html" %}
{% load i18n %}
{% block html_class %}login-pf{% endblock %}
{% block title %}{{ title }}{% endblock %}

{% block contents %}
<div class="container">
  <div class="row">
    <div class="col-sm-7 col-md-6 col-lg-5 login">
      <form class="form-horizontal" role="form" method="POST">
        {% csrf_token %}

        <div class="form-group">
          {{ form.new_password1.errors }}
          <label for="id_new_password1" class="col-sm-2 col-md-2 control-label">{% trans "Password" %}</label>
          <div class="col-sm-10 col-md-10">
            <input type="password" class="form-control" id="id_new_password1" name="new_password1" placeholder="" tabindex="1">
          </div>
        </div>
        <div class="form-group">
          {{ form.new_password2.errors }}
          <label for="id_new_password2" class="col-sm-2 col-md-2 control-label">{% trans "Confirm" %}</label>
          <div class="col-sm-10 col-md-10">
            <input type="password" class="form-control" id="id_new_password2" name="new_password2" placeholder="" tabindex="2">
          </div>
        </div>
        <div class="form-group">
          <div class="col-xs-12 col-sm-offset-2 col-sm-10 col-md-offset-2 col-md-10 submit">
            <button type="submit" class="btn btn-primary btn-lg" tabindex="4">{% trans "Change password" %}</button>
          </div>
        </div>
      </form>
    </div><!--/.col-*-->
    <div class="col-sm-5 col-md-6 col-lg-7 details">
      <p>
        <strong>
        {{ title }}
        </strong>
      </p>

      <p>
        {% if validlink %}
            {% trans "Please enter your new password twice so we can verify you typed it in correctly" %}.
        {% else %}
            {% trans "The password reset link was invalid, possibly because it has already been used.
            Please" %} <a href="{% url "tcms-password_reset" %}">{% trans "request a new password reset" %}</a>.
        {% endif %}
      </p>
    </div><!--/.col-*-->
  </div><!--/.row-->
</div><!--/.container-->
{% endblock %}
