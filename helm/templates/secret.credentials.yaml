{{- $secret_name := include "kiwi.credentials" . -}}

{{- if not (lookup "v1" "Secret" .Release.Namespace $secret_name) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secret_name }}
  labels:
    {{- include "kiwi.labels" . | nindent 4 }}
  annotations:
    "helm.sh/resource-policy": "keep"
type: Opaque
stringData:
  secret_key: {{ randAlphaNum 64 }}
  mariadb-username: {{ .Values.database.username }}
  mariadb-root-password: {{ .Values.database.password }}
  mariadb-replication-password: {{ .Values.database.password }}
  mariadb-password: {{ .Values.database.password }}
{{- end }}
