{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block head %}
    {{ form.media }}
{% endblock %}
{% block title %}
    {{ page_title }}
{% endblock %}

{% block page_id %}page-bugs-mutable{% endblock %}

{% block contents %}
    <div class="container-fluid container-cards-pf">
        <form class="form-horizontal" action="{{ form_post_url }}" method="post">
            {% csrf_token %}
            <input type="hidden" name="reporter" value="{{ form.reporter.value }}">
            <div class="form-group">
                <label class="col-md-1 col-lg-1" for="id_summary">{% trans "Summary" %}</label>
                <div class="col-md-8 col-lg-8 {% if form.summary.errors %}has-error{% endif %}">
                    <input type="text" id="id_summary" name="summary" value="{{ form.summary.value|default:'' }}" class="form-control" required>
                    {{ form.summary.errors }}
                </div>

                <div class="col-md-1 col-lg-1">
                    <label for="id_severity">{% trans "Severity" %}</label>
                    <a href="{% url 'admin:bugs_severity_add' %}?_popup" id="add_id_severity" alt="{% trans 'add new Severity' %}" title="{% trans 'add new Severity' %}">+</a>
                </div>
                <div class="col-md-2 col-lg-2 {% if form.severity.errors %}has-error{% endif %}">
                    <select id="id_severity" name="severity" class="form-control selectpicker">
                        <option value="">----------</option>
                    {% for severity in form.severity.field.queryset %}
                        <option value="{{ severity.pk }}" {% if severity.pk|escape == form.severity.value|escape %}selected{% endif %}>
                            {{ severity.name }}
                        </option>
                    {% endfor %}
                    </select>
                    {{ form.severity.errors }}
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-1 col-lg-1">
                    <label for="id_product">{% trans "Product" %}</label>
                    <a href="{% url 'admin:management_product_add' %}?_popup" id="add_id_product" alt="{% trans 'add new Product' %}" title="{% trans 'add new Product' %}">+</a>
                </div>
                <div class="col-md-2 col-lg-2 {% if form.product.errors %}has-error{% endif %}">
                    <select id="id_product" name="product" class="form-control selectpicker">
                        {% for product in form.product.field.queryset %}
                            <option value="{{ product.pk }}" {% if product.pk|escape == form.product.value|escape %}selected{% endif %}>
                                {{ product.name }}
                            </option>
                        {% endfor %}
                    </select>
                    {{ form.product.errors }}
                </div>

                <div class="col-md-1 col-lg-1">
                    <label for="id_version">{% trans "Version" %}</label>
                    <a href="{% url 'admin:management_version_add' %}?_popup&product={{ form.product.value }}"
                       id="add_id_version" alt="{% trans 'add new Version' %}" title="{% trans 'add new Version' %}">
                        +
                    </a>
                </div>
                <div class="col-md-2 col-lg-2 {% if form.version.errors %}has-error{% endif %}">
                    <select id="id_version" name="version" class="form-control selectpicker">
                        {% for version in form.version.field.queryset %}
                            <option value="{{ version.pk }}" {% if version.pk|escape == form.version.value|escape %}selected{% endif %}>
                                {{ version.value }}
                            </option>
                        {% endfor %}
                    </select>
                    {{ form.version.errors }}
                </div>

                <div class="col-md-1 col-lg-1">
                    <label for="id_build">{% trans "Build" %}</label>
                    <a href="{% url 'admin:management_build_add' %}?_popup&version={{ form.version.value }}&product={{ form.product.value }}"
                        id="add_id_build"
                        alt="{% trans 'add new Build' %}"
                        title="{% trans 'add new Build' %}">+</a>
                </div>
                <div class="col-md-2 col-lg-2">
                    <select class="form-control selectpicker" id="id_build" name="build">
                    {% for option in form.build.field.queryset %}
                        <option value="{{ option.pk }}" {% if option.pk|escape == form.build.value|escape %}selected{% endif %}>{{ option.name }}</option>
                    {% endfor %}
                    </select>
                    {{ form.build.errors }}
                </div>

                <div class="col-md-1 col-lg-1">
                    <label for="id_assignee">{% trans "Assignee" %}</label>
                </div>
                <div class="col-md-2 col-lg-2 {% if form.assignee.errors %}has-error{% endif %}">
                    <input id="id_assignee" name="assignee" value="{{ form.assignee.value|default:'' }}"
                        type="text" class="form-control" placeholder="{% trans 'Username or email' %}">
                    {{ form.assignee.errors }}
                </div>
            </div>

        {% if not object %}
            <div class="form-group">
                <div class="col-lg-12 col-md-12">
                    <div>{{ form.text }}</div>
                    {{ form.text.errors }}
                </div>
            </div>
        {% endif %}

            <div class="form-group">
                <div class="col-md-1 col-lg-1">
                    <button type="submit" class="btn btn-default btn-lg">{% trans "Save" %}</button>
                </div>
            </div>
        </form>
    </div>
{% endblock %}
