# Generated by Django 4.1.3 on 2022-11-10 09:50

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("testcases", "0021_add_property_model"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="historicaltemplate",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical Template",
                "verbose_name_plural": "historical Templates",
            },
        ),
        migrations.AlterModelOptions(
            name="historicaltestcase",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical test case",
                "verbose_name_plural": "historical test cases",
            },
        ),
        migrations.AlterField(
            model_name="historicaltemplate",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="historicaltestcase",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
    ]
