{% load i18n %}
{% blocktrans with pk=test_run.pk run_url=test_run.get_full_url plan_url=test_run.plan.get_full_url summary=test_run.summary manager=test_run.manager default_tester=test_run.default_tester product=test_run.build.version.product version=test_run.build.version build=test_run.build notes=test_run.notes %}Test run {{ pk }} has been created or updated for you.

### Links ###
Test run: {{ run_url }}
Test plan: {{ plan_url }}

### Basic run information ###
Summary: {{ summary }}

Managed: {{ manager }}.
Default tester: {{ default_tester }}.

Product: {{ product }}
Product version: {{ version }}
Build: {{ build }}

Notes:
{{ notes }}{% endblocktrans %}
