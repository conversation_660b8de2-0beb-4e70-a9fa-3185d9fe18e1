name: docker

on:
  push:
    tags:
      - v*

permissions: read-all

jobs:
  build_and_push:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Build & push docker images for ${{ github.event.ref }}
        run: |
            VERSION=$(python -m tcms)

            make docker-image

            # tag so we can push to private container registry
            docker tag pub.kiwitcms.eu/kiwitcms/kiwi:latest kiwitcms/kiwi:latest
            docker tag pub.kiwitcms.eu/kiwitcms/kiwi:latest quay.io/kiwitcms/upstream:$VERSION-$(uname -m)

            echo "+++++ Docker images +++++"
            docker images

            echo "${{ secrets.DOCKER_PUSH_TOKEN }}" | docker login -u="${{ secrets.DOCKER_PUSH_USERNAME }}" --password-stdin
            docker push kiwitcms/kiwi:latest
            docker logout

            echo "${{ secrets.QUAY_PUSH_TOKEN }}" | docker login -u="${{ secrets.QUAY_PUSH_USERNAME }}" --password-stdin quay.io
            docker push quay.io/kiwitcms/upstream:$VERSION-$(uname -m)
            docker logout quay.io
