{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Clone TestCase" %}{% endblock %}
{% block page_id %}page-testcases-clone{% endblock %}

{% block contents %}
<div class="container-fluid container-cards-pf">
    <form class="form-horizontal" method="post" action="{% url "testcases-clone" %}">
        {% csrf_token %}

        {% if form.plan.field.queryset %}
        <div class="form-group">
            <label class="col-md-12 col-lg-12">{% trans 'Add new TC into TP' %}</label>
        </div>
        {% endif %}

        {% for plan in form.plan.field.queryset %}
        <div class="form-group">
            <div class="col-md-2 col-lg-2">
                <input class="bootstrap-switch" name="plan" type="checkbox" checked value="{{ plan.pk }}" data-on-text="{% trans 'Yes' %}" data-off-text="{% trans 'No' %}">
            </div>
            <label class="col-md-10 col-lg-10">TP-{{ plan.pk }}: {{ plan.name }}</label>
        </div>
        {% endfor %}


        <div class="form-group">
            <label class="col-md-12 col-lg-12">{% trans 'Selected TC' %}</label>
        </div>

        {% for case in form.case.field.queryset %}
        <div class="form-group">
            <div class="col-md-2 col-lg-2">
                <input class="bootstrap-switch" name="case" type="checkbox" checked value="{{ case.pk }}" data-on-text="{% trans 'Yes' %}" data-off-text="{% trans 'No' %}">
            </div>
            <label class="col-md-10 col-lg-10">TC-{{ case.pk }}: {{ case.summary }}</label>
        </div>
        {% endfor %}

        {{ form.errors }}
        <div class="form-group">
            <div class="col-md-1 col-lg-1">
                <button type="submit" class="btn btn-default btn-lg">{% trans "Clone" %}</button>
            </div>
        </div>
    </form>
</div><!-- /container -->
{% endblock %}
