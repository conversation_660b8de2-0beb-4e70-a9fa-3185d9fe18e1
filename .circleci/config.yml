# Use the latest 2.1 version of CircleCI pipeline process engine.
# See: https://circleci.com/docs/2.0/configuration-reference
version: 2.1

# Define a job to be invoked later in a workflow.
# See: https://circleci.com/docs/2.0/configuration-reference/#jobs
jobs:
  aarch64-test-docker-image:
    machine:
      # https://circleci.com/developer/machine/image/ubuntu-2204
      image: ubuntu-2204:2023.10.1
    resource_class: arm.medium
    # Add steps to the job
    # See: https://circleci.com/docs/2.0/configuration-reference/#steps
    steps:
      - checkout
      - run: |
            uname -a
            python3 --version
            # fail if not using Python 3
            python3 --version | grep "3\."

            sudo apt-get install firefox git make wrk
            # install beakerlib from source it doesn't ship DEB packages
            if [ ! -f "/usr/share/beakerlib/beakerlib.sh" ]; then
                git clone https://github.com/beakerlib/beakerlib.git
                sudo make -C beakerlib/ install
            fi

            pip3 install -r requirements/devel.txt
            make test-docker-image

  aarch64-build-and-push-docker-image:
    machine:
      # https://circleci.com/developer/machine/image/ubuntu-2204
      image: ubuntu-2204:2023.10.1
    resource_class: arm.medium
    # Add steps to the job
    # See: https://circleci.com/docs/2.0/configuration-reference/#steps
    steps:
      - checkout
      - run: |
            uname -a
            python3 --version
            # fail if not using Python 3
            python3 --version | grep "3\."

            VERSION=$(python3 -m tcms)

            make docker-image

            # tag so we can push to private container registry
            docker tag pub.kiwitcms.eu/kiwitcms/kiwi:latest quay.io/kiwitcms/upstream:$VERSION-$(uname -m)

            echo "+++++ Docker images +++++"
            docker images

            echo "$QUAY_PUSH_TOKEN" | docker login -u="$QUAY_PUSH_USERNAME" --password-stdin quay.io
            docker push quay.io/kiwitcms/upstream:$VERSION-$(uname -m)
            docker logout quay.io


# Invoke jobs via workflows
# See: https://circleci.com/docs/2.0/configuration-reference/#workflows
workflows:
  aarch64-docker-test:
    when:
      matches: { pattern: "^prepare/v.+$", value: << pipeline.git.branch >> }
    jobs:
      - aarch64-test-docker-image

  aarch64-docker-release:
    jobs:
      - aarch64-build-and-push-docker-image:
          filters:
            tags:
              only: /^v.+$/
            branches:
              ignore: /.*/
