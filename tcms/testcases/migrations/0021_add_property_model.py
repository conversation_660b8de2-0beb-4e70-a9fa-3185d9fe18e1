# Generated by Django 3.2.8 on 2021-11-16 14:03

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("testcases", "0020_add_template"),
    ]

    operations = [
        migrations.CreateModel(
            name="Property",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("value", models.CharField(max_length=255)),
                (
                    "case",
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        to="testcases.testcase",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
