{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Search Bugs" %}{% endblock %}
{% block page_id %}page-bugs-search{% endblock %}

{% block contents %}
<div class="container-fluid container-cards-pf">
    <form class="form-horizontal" method="get">
        {% csrf_token %}
        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_summary">{% trans "Summary" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_summary" type="text" class="form-control">
            </div>

            <label class="col-md-1 col-lg-1" for="id_product">{% trans "Severity" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_severity">
                    <option value="">----------</option>
                {% for option in form.severity.field.queryset %}
                    <option value="{{ option.pk }}" {% if form.severity.value|add:'0' == option.pk %}selected{% endif %}>{{ option.name }}</option>
                {% endfor %}
                </select>
            </div>

            <label for="id_before" class="col-md-1 col-lg-1">{% trans "Created" %}</label>
            <div class="col-md-3 col-lg-3">
                <div class="input-group date-time-picker-pf">
                    <span class="input-group-addon">{% trans "Before" %}</span>
                    <input type="text" class="form-control" id="id_before">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>

                    <span class="input-group-addon">{% trans "After" %}</span>
                    <input type="text" class="form-control" id="id_after">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_product">{% trans "Product" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_product">
                    <option value="">----------</option>
                {% for option in form.product.field.queryset %}
                    <option value="{{ option.pk }}" {% if form.product.value|add:'0' == option.pk %}selected{% endif %}>{{ option.name }}</option>
                {% endfor %}
                </select>
            </div>

            <label class="col-md-1 col-lg-1" for="id_version">{% trans "Version" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_version">
                    <option value="">----------</option>
                {% for option in form.version.field.queryset %}
                    <option value="{{ option.pk }}" {% if form.version.value|add:'0' == option.pk %}selected{% endif %}>{{ option.value }}</option>
                {% endfor %}
                </select>
            </div>

            <label class="col-md-1 col-lg-1" for="id_build">{% trans "Build" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_build">
                    <option value="">----------</option>
                {% for option in form.build.field.queryset %}
                    <option value="{{ option.pk }}" {% if form.build.value|add:'0' == option.pk %}selected{% endif %}>{{ option.name }}</option>
                {% endfor %}
                </select>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_reporter">{% trans "Reporter" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_reporter" type="text" class="form-control" placeholder="{% trans 'Username' %}">
            </div>

            <label class="col-md-1 col-lg-1" for="id_assignee">{% trans "Assignee" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_assignee" type="text" class="form-control" placeholder="{% trans 'Username' %}">
            </div>

            <label class="col-md-1 col-lg-1" for="id_status">{% trans "Open" %}</label>
            <div class="col-md-3 col-lg-3">
                <input class="bootstrap-switch" id="id_status" type="checkbox" checked data-on-text="{% trans 'Yes' %}" data-off-text="{% trans 'No' %}">
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-1 col-lg-1">
                <button id="btn_search" type="submit" class="btn btn-default btn-lg">{% trans "Search" %}</button>
            </div>
        </div>
    </form>

    <div class="panel panel-default">
        <!-- Table HTML -->
        <table class="table table-striped table-bordered table-hover" id="resultsTable">
            <thead>
                <tr>
                    <th>{% trans "ID" %}</th>
                    <th>{% trans "Severity" %}</th>
                    <th>{% trans "Summary" %}</th>
                    <th>{% trans "Created on" %}</th>
                    <th>{% trans "Product" %}</th>
                    <th>{% trans "Version" %}</th>
                    <th>{% trans "Build" %}</th>
                    <th>{% trans "Reporter" %}</th>
                    <th>{% trans "Assignee" %}</th>
                </tr>
            </thead>
        </table>

    </div>
</div><!-- /container -->
{% endblock %}
