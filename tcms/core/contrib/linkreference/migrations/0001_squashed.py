# Generated by Django 2.0.7 on 2018-07-25 14:20

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("testruns", "0004_squashed"),
    ]

    operations = [
        migrations.CreateModel(
            name="LinkReference",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "test_case_run",
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE, to="testruns.TestCaseRun"
                    ),
                ),
                ("name", models.CharField(blank=True, default="", max_length=64)),
                ("url", models.URLField()),
                ("created_on", models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
