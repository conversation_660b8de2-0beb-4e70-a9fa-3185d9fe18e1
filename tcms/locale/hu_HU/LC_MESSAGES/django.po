msgid ""
msgstr ""
"Project-Id-Version: kiwitcms\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-30 12:22+0000\n"
"PO-Revision-Date: 2025-07-30 13:13\n"
"Last-Translator: \n"
"Language-Team: Hungarian\n"
"Language: hu_HU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: kiwitcms\n"
"X-Crowdin-Project-ID: 295734\n"
"X-Crowdin-Language: hu\n"
"X-Crowdin-File: /master/tcms/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 19\n"

#: tcms/bugs/forms.py:33
msgid "Description of problem:\n\n\n"
"How often reproducible:\n\n\n"
"Steps to Reproduce:\n"
"1.\n"
"2.\n"
"3.\n\n"
"Actual results:\n\n\n"
"Expected results:\n\n\n"
"Additional info:"
msgstr "A probléma leírása:\n\n\n"
"Milyen gyakorisággal reprodukálható:\n\n\n"
"A reprodukálás lépései:\n"
"1.\n"
"2.\n"
"3.\n\n"
"Jelenlegi eredmények:\n\n\n"
"Elvárt eredmények:\n\n\n"
"További információ:"

#: tcms/bugs/models.py:24 tcms/bugs/templates/bugs/get.html:50
#: tcms/bugs/templates/bugs/mutable.html:27
#: tcms/bugs/templates/bugs/search.html:18
#: tcms/bugs/templates/bugs/search.html:108
msgid "Severity"
msgstr "Súlyosság"

#: tcms/bugs/templates/bugs/get.html:37 tcms/bugs/templates/bugs/search.html:89
#: tcms/issuetracker/kiwitcms.py:49 tcms/templates/include/bug_details.html:3
msgid "Open"
msgstr "Nyitott"

#: tcms/bugs/templates/bugs/get.html:39 tcms/issuetracker/kiwitcms.py:49
#: tcms/templates/include/bug_details.html:3
msgid "Closed"
msgstr "Lezárt"

#: tcms/bugs/templates/bugs/get.html:59 tcms/bugs/templates/bugs/search.html:79
#: tcms/bugs/templates/bugs/search.html:114
#: tcms/templates/include/bug_details.html:7
msgid "Reporter"
msgstr "Bejelentő"

#: tcms/bugs/templates/bugs/get.html:64
#: tcms/bugs/templates/bugs/mutable.html:94
#: tcms/bugs/templates/bugs/search.html:84
#: tcms/bugs/templates/bugs/search.html:115
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:36
#: tcms/templates/include/bug_details.html:10
#: tcms/testruns/templates/testruns/get.html:207
#: tcms/testruns/templates/testruns/get.html:267
msgid "Assignee"
msgstr "Felelős"

#: tcms/bugs/templates/bugs/get.html:75
#: tcms/bugs/templates/bugs/mutable.html:45
#: tcms/bugs/templates/bugs/search.html:47
#: tcms/bugs/templates/bugs/search.html:111
#: tcms/core/templates/dashboard.html:53 tcms/management/admin.py:107
#: tcms/telemetry/templates/telemetry/include/filters.html:8
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:29
#: tcms/templates/include/bug_details.html:13
#: tcms/testcases/templates/testcases/get.html:44
#: tcms/testcases/templates/testcases/get.html:170
#: tcms/testcases/templates/testcases/mutable.html:43
#: tcms/testcases/templates/testcases/search.html:51
#: tcms/testplans/templates/testplans/clone.html:21
#: tcms/testplans/templates/testplans/get.html:64
#: tcms/testplans/templates/testplans/mutable.html:33
#: tcms/testplans/templates/testplans/search.html:50
#: tcms/testplans/templates/testplans/search.html:117
#: tcms/testruns/templates/testruns/get.html:45
#: tcms/testruns/templates/testruns/mutable.html:46
#: tcms/testruns/templates/testruns/search.html:35
#: tcms/testruns/templates/testruns/search.html:183
msgid "Product"
msgstr "Termék"

#: tcms/bugs/templates/bugs/get.html:80
#: tcms/bugs/templates/bugs/mutable.html:60
#: tcms/bugs/templates/bugs/search.html:57
#: tcms/bugs/templates/bugs/search.html:112
#: tcms/telemetry/templates/telemetry/include/filters.html:17
#: tcms/templates/include/bug_details.html:15 tcms/templates/navbar.html:71
#: tcms/testplans/templates/testplans/clone.html:36
#: tcms/testplans/templates/testplans/get.html:70
#: tcms/testplans/templates/testplans/mutable.html:48
#: tcms/testplans/templates/testplans/search.html:60
#: tcms/testplans/templates/testplans/search.html:118
#: tcms/testruns/templates/testruns/get.html:50
#: tcms/testruns/templates/testruns/search.html:45
#: tcms/testruns/templates/testruns/search.html:184
msgid "Version"
msgstr "Verzió"

#: tcms/bugs/templates/bugs/get.html:85
#: tcms/bugs/templates/bugs/mutable.html:78
#: tcms/bugs/templates/bugs/search.html:67
#: tcms/bugs/templates/bugs/search.html:113 tcms/management/models.py:132
#: tcms/telemetry/templates/telemetry/include/filters.html:25
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:30
#: tcms/templates/include/bug_details.html:17
#: tcms/testruns/templates/testruns/get.html:55
#: tcms/testruns/templates/testruns/get.html:414
#: tcms/testruns/templates/testruns/mutable.html:89
#: tcms/testruns/templates/testruns/search.html:55
#: tcms/testruns/templates/testruns/search.html:185
msgid "Build"
msgstr "Build"

#: tcms/bugs/templates/bugs/get.html:119
msgid "commented on"
msgstr "hozzászólt"

#: tcms/bugs/templates/bugs/get.html:149
msgid "Reopen"
msgstr "Újranyit"

#: tcms/bugs/templates/bugs/get.html:151
#: tcms/bugs/templates/bugs/mutable.html:114
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:78
#: tcms/testcases/templates/testcases/mutable.html:263
#: tcms/testplans/templates/testplans/get.html:477
#: tcms/testplans/templates/testplans/mutable.html:173
#: tcms/testruns/templates/testruns/get.html:480
#: tcms/testruns/templates/testruns/get.html:593
#: tcms/testruns/templates/testruns/mutable.html:210
msgid "Save"
msgstr "Mentés"

#: tcms/bugs/templates/bugs/get.html:153
msgid "Close"
msgstr "Lezárás"

#: tcms/bugs/templates/bugs/mutable.html:20
#: tcms/bugs/templates/bugs/search.html:13
#: tcms/bugs/templates/bugs/search.html:109
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:27
#: tcms/testcases/templates/testcases/mutable.html:24
#: tcms/testcases/templates/testcases/search.html:13
#: tcms/testcases/templates/testcases/search.html:162
#: tcms/testplans/templates/testplans/get.html:212
#: tcms/testplans/templates/testplans/get.html:487
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:3
#: tcms/testruns/templates/testruns/get.html:251
#: tcms/testruns/templates/testruns/get.html:258
#: tcms/testruns/templates/testruns/get.html:261
#: tcms/testruns/templates/testruns/mutable.html:23
#: tcms/testruns/templates/testruns/mutable.html:229
#: tcms/testruns/templates/testruns/search.html:14
#: tcms/testruns/templates/testruns/search.html:181
msgid "Summary"
msgstr "Összegzés"

#: tcms/bugs/templates/bugs/mutable.html:28
msgid "add new Severity"
msgstr "új súlyosság hozzáadása"

#: tcms/bugs/templates/bugs/mutable.html:46
#: tcms/testcases/templates/testcases/mutable.html:44
#: tcms/testplans/templates/testplans/clone.html:22
#: tcms/testplans/templates/testplans/mutable.html:34
#: tcms/testruns/templates/testruns/mutable.html:48
msgid "add new Product"
msgstr "új Termék hozzáadása"

#: tcms/bugs/templates/bugs/mutable.html:62
#: tcms/testplans/templates/testplans/clone.html:38
#: tcms/testplans/templates/testplans/mutable.html:50
msgid "add new Version"
msgstr "új Verzió hozzáadása"

#: tcms/bugs/templates/bugs/mutable.html:81
#: tcms/bugs/templates/bugs/mutable.html:82
#: tcms/testruns/templates/testruns/mutable.html:93
#: tcms/testruns/templates/testruns/mutable.html:94
msgid "add new Build"
msgstr "új Build hozzáadása"

#: tcms/bugs/templates/bugs/mutable.html:98
#: tcms/testruns/templates/testruns/mutable.html:32
#: tcms/testruns/templates/testruns/mutable.html:39
msgid "Username or email"
msgstr "Felhasználónév vagy email cím"

#: tcms/bugs/templates/bugs/search.html:5 tcms/settings/common.py:414
msgid "Search Bugs"
msgstr "Hibajegyek Keresése"

#: tcms/bugs/templates/bugs/search.html:28
#: tcms/testcases/templates/testcases/search.html:19
#: tcms/testplans/templates/testplans/search.html:24
msgid "Created"
msgstr "Létrehozva"

#: tcms/bugs/templates/bugs/search.html:31
#: tcms/telemetry/templates/telemetry/include/filters.html:53
#: tcms/testcases/templates/testcases/search.html:22
#: tcms/testplans/templates/testplans/search.html:27
#: tcms/testruns/templates/testruns/search.html:90
#: tcms/testruns/templates/testruns/search.html:110
#: tcms/testruns/templates/testruns/search.html:131
#: tcms/testruns/templates/testruns/search.html:151
msgid "Before"
msgstr "Előtte"

#: tcms/bugs/templates/bugs/search.html:37
#: tcms/telemetry/templates/telemetry/include/filters.html:43
#: tcms/testcases/templates/testcases/search.html:28
#: tcms/testplans/templates/testplans/search.html:33
#: tcms/testruns/templates/testruns/search.html:97
#: tcms/testruns/templates/testruns/search.html:117
#: tcms/testruns/templates/testruns/search.html:138
#: tcms/testruns/templates/testruns/search.html:158
msgid "After"
msgstr "Utána"

#: tcms/bugs/templates/bugs/search.html:81
#: tcms/bugs/templates/bugs/search.html:86
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:45
#: tcms/templates/registration/login.html:17
#: tcms/templates/registration/registration_form.html:15
#: tcms/testcases/templates/testcases/search.html:124
#: tcms/testplans/templates/testplans/search.html:84
#: tcms/testplans/templates/testplans/search.html:90
#: tcms/testruns/templates/testruns/get.html:524
#: tcms/testruns/templates/testruns/search.html:69
#: tcms/testruns/templates/testruns/search.html:75
msgid "Username"
msgstr "Felhasználónév"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:39
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "Yes"
msgstr "Igen"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:42
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "No"
msgstr "Nem"

#: tcms/bugs/templates/bugs/search.html:97
#: tcms/testcases/templates/testcases/search.html:150
#: tcms/testplans/templates/testplans/get.html:221
#: tcms/testplans/templates/testplans/search.html:103
#: tcms/testruns/templates/testruns/get.html:273
#: tcms/testruns/templates/testruns/search.html:170
msgid "Search"
msgstr "Keresés"

#: tcms/bugs/templates/bugs/search.html:107
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:26
#: tcms/testcases/templates/testcases/get.html:166
#: tcms/testcases/templates/testcases/search.html:144
#: tcms/testcases/templates/testcases/search.html:161
#: tcms/testplans/templates/testplans/search.html:114
#: tcms/testruns/templates/testruns/search.html:180
msgid "ID"
msgstr "ID"

#: tcms/bugs/templates/bugs/search.html:110
#: tcms/templates/include/bug_details.html:5
#: tcms/testcases/templates/testcases/search.html:163
#: tcms/testplans/templates/testplans/search.html:116
#: tcms/testruns/templates/testruns/mutable.html:231
msgid "Created on"
msgstr "Létrehozva"

#: tcms/bugs/views.py:42 tcms/testcases/views.py:130
#: tcms/testplans/templates/testplans/get.html:380 tcms/testplans/views.py:143
#: tcms/testruns/views.py:194 tcms/testruns/views.py:308
msgid "Edit"
msgstr "Szerkesztés"

#: tcms/bugs/views.py:45 tcms/testcases/views.py:143
#: tcms/testplans/views.py:151 tcms/testruns/views.py:207
#: tcms/testruns/views.py:316
msgid "Object permissions"
msgstr "Objektum jogosultságok"

#: tcms/bugs/views.py:50
#: tcms/templates/include/comments_for_object_template.html:10
#: tcms/templates/include/properties_card.html:32
#: tcms/templates/include/properties_card.html:47 tcms/testcases/views.py:151
#: tcms/testplans/templates/testplans/get.html:193
#: tcms/testplans/templates/testplans/get.html:384 tcms/testplans/views.py:159
#: tcms/testruns/templates/testruns/get.html:238
#: tcms/testruns/templates/testruns/get.html:450 tcms/testruns/views.py:215
#: tcms/testruns/views.py:324
msgid "Delete"
msgstr "Törlés"

#: tcms/bugs/views.py:68 tcms/settings/common.py:400
msgid "New Bug"
msgstr "Új Hibajegy"

#: tcms/bugs/views.py:188
msgid "Edit bug"
msgstr "Hibajegy szerkesztése"

#: tcms/bugs/views.py:231
msgid "*bug closed*"
msgstr "*hibajegy lezárva*"

#: tcms/bugs/views.py:235
msgid "*bug reopened*"
msgstr "*hibajegy újranyitva*"

#: tcms/core/history.py:52
#, python-format
msgid "UPDATE: %(model_name)s #%(pk)d - %(title)s"
msgstr "FRISSÍTÉS: %(model_name)s #%(pk)d - %(title)s"

#: tcms/core/history.py:62
#, python-format
msgid "Updated on %(history_date)s\n"
"Updated by %(username)s\n\n"
"%(diff)s\n\n"
"For more information:\n"
"%(instance_url)s"
msgstr "Frissítve: %(history_date)s\n"
"Frissítette: %(username)s\n\n"
"%(diff)s\n\n"
"További információ:\n"
"%(instance_url)s"

#: tcms/core/templates/dashboard.html:3 tcms/settings/common.py:430
msgid "Dashboard"
msgstr "Dashboard"

#: tcms/core/templates/dashboard.html:8
#: tcms/testruns/templates/testruns/get.html:153
msgid "Test executions"
msgstr "Teszt végrehajtása"

#: tcms/core/templates/dashboard.html:15
#, python-format
msgid "%(amount)s%% complete"
msgstr "%(amount)s%% kész"

#: tcms/core/templates/dashboard.html:24
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:38
#: tcms/testruns/templates/testruns/get.html:78
msgid "Started at"
msgstr "Kezdés időpontja"

#: tcms/core/templates/dashboard.html:32
#, python-format
msgid "%(total_count)s TestRun(s) or TestCase(s) assigned to you need to be executed.\n"
"Here are the latest %(count)s."
msgstr "%(total_count)s Tesztfuttatás(ok) vagy Teszteset(ek) van hozzád rendelve végrehajtásra.\n"
"Itt van a legújabb %(count)s."

#: tcms/core/templates/dashboard.html:36 tcms/core/templates/dashboard.html:80
msgid "SEE ALL"
msgstr "ÖSSZES"

#: tcms/core/templates/dashboard.html:39
msgid "There are no TestRun(s) assigned to you"
msgstr "Nem tartozik hozzád Tesztfuttatás"

#: tcms/core/templates/dashboard.html:46
msgid "Your Test plans"
msgstr "Tesztterveid"

#: tcms/core/templates/dashboard.html:52
msgid "TestPlan"
msgstr "Tesztterv"

#: tcms/core/templates/dashboard.html:54
#: tcms/testcases/templates/testcases/get.html:169
#: tcms/testplans/templates/testplans/mutable.html:66
#: tcms/testplans/templates/testplans/search.html:70
#: tcms/testplans/templates/testplans/search.html:119
msgid "Type"
msgstr "Típus"

#: tcms/core/templates/dashboard.html:55
#: tcms/templates/include/tc_executions.html:7
msgid "Executions"
msgstr "Végrehajtások"

#: tcms/core/templates/dashboard.html:76
#, python-format
msgid "You manage %(total_count)s TestPlan(s), %(disabled_count)s are disabled.\n"
"Here are the latest %(count)s."
msgstr "%(total_count)s saját kezelésű tesztterv, %(disabled_count)s letiltva.\n"
"Itt van a legújabb %(count)s."

#: tcms/core/templates/dashboard.html:83
msgid "There are no TestPlan(s) that belong to you"
msgstr "Nem tartozik hozzád Tesztterv"

#: tcms/core/views.py:47
#, python-format
msgid "Base URL is not configured! See <a href=\"%(doc_url)s\">documentation</a> and <a href=\"%(admin_url)s\">change it</a>"
msgstr "Az alap URL nincs beállítva! Lásd a <a href=\"%(doc_url)s\">dokumentációt</a> a <a href=\"%(admin_url)s\">beállításokhoz</a>"

#: tcms/core/views.py:71
#, python-format
msgid "You have %(unapplied_migration_count)s unapplied migration(s). See <a href=\"%(doc_url)s\">documentation</a>"
msgstr "%(unapplied_migration_count)s nem alkalmazott migrációd van. Lásd a <a href=\"%(doc_url)s\">dokumentációt</a>"

#: tcms/core/views.py:92
#, python-format
msgid "You are not using a secure connection. See <a href=\"%(doc_url)s\">documentation</a> and enable SSL."
msgstr "Nem biztonságos kapcsolatot használsz. Tekintsd meg a <a href=\"%(doc_url)s\">dokumentációt</a> és engedélyezd az SSL-t."

#: tcms/kiwi_attachments/validators.py:10
#, python-brace-format
msgid "File contains forbidden tag: <{tag_name}>"
msgstr "A file nem enegedélyezett címkét tartalmaz: <{tag_name}>"

#: tcms/kiwi_attachments/validators.py:92
#, python-brace-format
msgid "File contains forbidden attribute: `{attr_name}`"
msgstr "A file nem enegedélyezett attribútomot tartalmaz:: `{attr_name}`"

#: tcms/kiwi_attachments/validators.py:97
msgid "Uploading executable files is forbidden"
msgstr "Futattható fájlok nem tőlthetőek fel"

#: tcms/kiwi_auth/admin.py:36 tcms/settings/common.py:443
msgid "Users"
msgstr "Felhasználók"

#: tcms/kiwi_auth/admin.py:82
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:5
#: tcms/templates/navbar.html:102
msgid "Reset email address"
msgstr "Email cím alap álapotba állítása"

#: tcms/kiwi_auth/admin.py:148
msgid "Personal info"
msgstr "Személyes adatok"

#: tcms/kiwi_auth/admin.py:150
msgid "Permissions"
msgstr "Jogosultságok"

#: tcms/kiwi_auth/admin.py:187
msgid "This is the last superuser, it cannot be deleted!"
msgstr "Ez az utolsó adminisztrátór felhasználó, nem törölhető!"

#: tcms/kiwi_auth/forms.py:28
msgid "A user with that email already exists."
msgstr "Már létezik felhasználó ezzel az email címmel."

#: tcms/kiwi_auth/forms.py:48
msgid "Please confirm your Kiwi TCMS account email address"
msgstr "Kérjük erősítse meg a Kiwi TCMS fiókja email címét"

#: tcms/kiwi_auth/forms.py:142
msgid "Email mismatch"
msgstr "Különböző email címek"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:14
msgid "Warning"
msgstr "Figyelem"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:19
msgid "After clicking the 'Save' button your account will become <strong>inactive</strong>\n"
"and you will be <strong>logged out</strong>! A confirmation email will be sent to the newly specified address!<br>\n"
"Double check that your new email address is <strong>entered correctly</strong> otherwise\n"
"<strong>you may be left locked out</strong> of your account!\n"
"After following the activation link you will be able to log in as usual!"
msgstr "A 'Save' gombra kattintás utána a fiók <strong>inaktiv</strong> állapotú lesz és <strong>ki lesz léptetve </strong>! Egy megerősítő email lesz küldve az újonnan beállított email címre!<br>\n"
"Ellenőrizze még egszer, hogy email cmímet <strong>helyesen adta meg</strong> különben\n"
"<strong>kizárhatja magát</strong> a fiókjából!\n"
"Az aktiváló linkre katintás után a megszokot módon jelentkezhet be."

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:51
msgid "NOT yourself"
msgstr ""

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:58
#: tcms/templates/registration/password_reset_form.html:16
#: tcms/templates/registration/registration_form.html:39
msgid "E-mail"
msgstr "Email"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:66
#: tcms/templates/registration/password_reset_confirm.html:22
#: tcms/templates/registration/registration_form.html:31
msgid "Confirm"
msgstr "Megerősítés"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:71
msgid "Please type! Do not copy-and-paste value from previous field!"
msgstr "Kérem gépeljen! Ne másoljon és illsszen be értékeket az előző mezőből!"

#: tcms/kiwi_auth/views.py:70
msgid "Your account has been created, please check your mailbox for confirmation"
msgstr "A fiókod létrehozva, a megerősítésért ellenőrizd a levelesládádat"

#: tcms/kiwi_auth/views.py:75
msgid "Your account has been created, but you need an administrator to activate it"
msgstr "A fiókod létrehozva, de egy adminisztrátornak aktiválnia kell"

#: tcms/kiwi_auth/views.py:80
msgid "Following is the administrator list"
msgstr "Rendszergazda lista"

#: tcms/kiwi_auth/views.py:123
msgid "This activation key no longer exists in the database"
msgstr "Az aktiválási kulcs már nem létezik az adatbázisban"

#: tcms/kiwi_auth/views.py:129
msgid "This activation key has expired"
msgstr "Ez az aktivációs kulcs elévült"

#: tcms/kiwi_auth/views.py:141
msgid "Your account has been activated successfully"
msgstr "A fiók sikeresen aktiválva"

#: tcms/kiwi_auth/views.py:168 tcms/kiwi_auth/views.py:177
#: tcms/kiwi_auth/views.py:196 tcms/kiwi_auth/views.py:205
#, python-format
msgid "You are viewing records from tenant '%s'"
msgstr "You are viewing records from tenant '%s'"

#: tcms/kiwi_auth/views.py:256
msgid "Email address has been reset, please check inbox for further instructions"
msgstr ""

#: tcms/management/models.py:58
#: tcms/telemetry/templates/telemetry/testing/breakdown.html:34
msgid "Priorities"
msgstr "Prioritások"

#: tcms/management/models.py:133
msgid "Builds"
msgstr "Buildek"

#: tcms/management/models.py:144
#: tcms/testcases/templates/testcases/search.html:136
#: tcms/testplans/templates/testplans/get.html:217
#: tcms/testplans/templates/testplans/search.html:94
#: tcms/testruns/templates/testruns/get.html:263
#: tcms/testruns/templates/testruns/search.html:27
msgid "Tag"
msgstr "Címke"

#: tcms/management/models.py:145 tcms/templates/include/tags_card.html:6
#: tcms/testcases/templates/testcases/search.html:171
#: tcms/testplans/templates/testplans/get.html:456
#: tcms/testplans/templates/testplans/search.html:121
#: tcms/testruns/templates/testruns/get.html:353
#: tcms/testruns/templates/testruns/search.html:190
msgid "Tags"
msgstr "Címkék"

#: tcms/rpc/api/bug.py:69
msgid "Enable reporting to this Issue Tracker by configuring its base_url!"
msgstr "Engedélyezd a jelentést ehhez a problémakövetőhöz a base_url konfigurálásával!"

#: tcms/rpc/api/forms/__init__.py:9
msgid "Invalid date format. Expected YYYY-MM-DD [HH:MM:SS]."
msgstr "Érvénytelen dátumformátum. Az elvárt formátum: YYYY-MM-DD [HH:MM:SS]."

#: tcms/settings/common.py:391
msgid "TESTING"
msgstr "TESZTELÉS"

#: tcms/settings/common.py:393 tcms/testruns/templates/testruns/mutable.html:70
msgid "New Test Plan"
msgstr "Új Tesztterv"

#: tcms/settings/common.py:395
#: tcms/testcases/templates/testcases/mutable.html:12
#: tcms/testplans/templates/testplans/get.html:131
msgid "New Test Case"
msgstr "Új Teszteset"

#: tcms/settings/common.py:397 tcms/testplans/templates/testplans/get.html:121
#: tcms/testruns/templates/testruns/get.html:172
#: tcms/testruns/templates/testruns/mutable.html:11
msgid "New Test Run"
msgstr "Új Tesztfuttatás"

#: tcms/settings/common.py:407
msgid "SEARCH"
msgstr "KERESÉS"

#: tcms/settings/common.py:409 tcms/testplans/templates/testplans/search.html:5
msgid "Search Test Plans"
msgstr "Teszttervek Keresése"

#: tcms/settings/common.py:410 tcms/testcases/templates/testcases/search.html:5
msgid "Search Test Cases"
msgstr "Tesztesetek Keresése"

#: tcms/settings/common.py:411 tcms/testruns/templates/testruns/search.html:5
msgid "Search Test Runs"
msgstr "Tesztfuttatások Keresése"

#: tcms/settings/common.py:412
msgid "Search Test Executions"
msgstr ""

#: tcms/settings/common.py:421
msgid "TELEMETRY"
msgstr "TELEMETRIA"

#: tcms/settings/common.py:424
msgid "Testing"
msgstr "Tesztelés"

#: tcms/settings/common.py:426
msgid "Breakdown"
msgstr "Elemzés"

#: tcms/settings/common.py:428
msgid "Execution"
msgstr "Végrehajtás"

#: tcms/settings/common.py:431
#: tcms/testruns/templates/testruns/mutable.html:177
msgid "Matrix"
msgstr "Mátrix"

#: tcms/settings/common.py:432
msgid "Trends"
msgstr "Trendek"

#: tcms/settings/common.py:435
#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:5
msgid "TestCase health"
msgstr "Teszteset állapota"

#: tcms/settings/common.py:441
msgid "ADMIN"
msgstr "ADMIN"

#: tcms/settings/common.py:444
msgid "Groups"
msgstr "Csoportok"

#: tcms/settings/common.py:446
msgid "Everything else"
msgstr "Minden más"

#: tcms/settings/common.py:449
msgid "MORE"
msgstr ""

#: tcms/settings/common.py:459
msgid "Report an Issue"
msgstr "Hibabejelentés"

#: tcms/settings/common.py:462
msgid "Ask for help on StackOverflow"
msgstr "Segítségkérés StackOverflow-on keresztül"

#: tcms/settings/common.py:466
msgid "Donate €5 via Open Collective"
msgstr "€5 támogatás az Open Collective-en keresztül"

#: tcms/settings/common.py:468
msgid "Administration Guide"
msgstr "Admin Útmutató"

#: tcms/settings/common.py:469
msgid "User Guide"
msgstr "Felhasználói Útmutató"

#: tcms/settings/common.py:470
msgid "API Help"
msgstr "API Súgó"

#: tcms/signals.py:85
msgid "New user awaiting approval"
msgstr "Új felhasználó jóváhagyásra vár"

#: tcms/signals.py:163
#, python-format
msgid "NEW: TestRun #%(pk)d - %(summary)s"
msgstr "ÚJ: Tesztfuttatás #%(pk)d - %(summary)s"

#: tcms/signals.py:235
#, python-format
msgid "Bug #%(pk)d - %(summary)s"
msgstr "Meghibásodás #%(pk)d - %(summary)s"

#: tcms/telemetry/api.py:60 testcases.TestCaseStatus/name:2
msgid "CONFIRMED"
msgstr "MEGERŐSÍTETT"

#: tcms/telemetry/api.py:61
msgid "OTHER"
msgstr "EGYÉB"

#: tcms/telemetry/api.py:133 tcms/telemetry/api.py:185
#: tcms/telemetry/api.py:191
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:9
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:13
#: tcms/testruns/templates/testruns/get.html:129
msgid "TOTAL"
msgstr "ÖSSZESEN"

#: tcms/telemetry/templates/telemetry/include/filters.html:36
#: tcms/testcases/templates/testcases/search.html:68
#: tcms/testplans/templates/testplans/search.html:115
#: tcms/testruns/templates/testruns/get.html:40
#: tcms/testruns/templates/testruns/mutable.html:67
#: tcms/testruns/templates/testruns/search.html:182
msgid "Test plan"
msgstr "Tesztterv"

#: tcms/telemetry/templates/telemetry/include/filters.html:66
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
#: tcms/testcases/templates/testcases/search.html:142
msgid "Test run"
msgstr "Teszt futtatás"

#: tcms/telemetry/templates/telemetry/include/filters.html:70
#: tcms/testruns/templates/testruns/search.html:17
msgid "Test run summary"
msgstr "Tesztfuttatás összefoglaló"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:5
msgid "Testing Breakdown"
msgstr "Tesztelés Elemzés"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:14
msgid "Total"
msgstr "Összes"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:24
#: tcms/testcases/templates/testcases/get.html:93
#: tcms/testcases/templates/testcases/mutable.html:98
#: tcms/testcases/templates/testcases/search.html:36
#: tcms/testcases/templates/testcases/search.html:168
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testplans/templates/testplans/get.html:489
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:5
#: tcms/testruns/templates/testruns/get.html:264
#: tcms/testruns/templates/testruns/get.html:361
msgid "Automated"
msgstr "Automatizált"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:28
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testruns/templates/testruns/get.html:361
msgid "Manual"
msgstr "Kézi"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:39
#: tcms/testcases/models.py:42
msgid "Categories"
msgstr "Kategóriák"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:5
msgid "Execution Dashboard"
msgstr "Végrehajtási irányítópult"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:15
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:15
msgid "Child TPs"
msgstr "Gyermek TPk"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:28
#: tcms/templates/include/bug_details.html:3
#: tcms/testcases/templates/testcases/get.html:59
#: tcms/testcases/templates/testcases/mutable.html:74
#: tcms/testcases/templates/testcases/search.html:91
#: tcms/testcases/templates/testcases/search.html:167
#: tcms/testplans/templates/testplans/get.html:149
#: tcms/testplans/templates/testplans/get.html:363
#: tcms/testplans/templates/testplans/get.html:396
#: tcms/testplans/templates/testplans/get.html:488
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:4
#: tcms/testruns/templates/testruns/get.html:189
#: tcms/testruns/templates/testruns/get.html:269
#: tcms/testruns/templates/testruns/get.html:385
#: tcms/testruns/templates/testruns/mutable.html:232
msgid "Status"
msgstr "Állapot"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:31
#: tcms/testcases/templates/testcases/get.html:208
#: tcms/testplans/templates/testplans/get.html:447
#: tcms/testruns/templates/testruns/get.html:349
msgid "Components"
msgstr "Komponensek"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
#: tcms/testcases/templates/testcases/get.html:33
#: tcms/testcases/templates/testcases/mutable.html:36
#: tcms/testcases/templates/testcases/mutable.html:207
#: tcms/testcases/templates/testcases/search.html:170
#: tcms/testplans/templates/testplans/get.html:175
#: tcms/testplans/templates/testplans/get.html:378
#: tcms/testplans/templates/testplans/get.html:416
#: tcms/testplans/templates/testplans/get.html:493
#: tcms/testplans/templates/testplans/mutable.html:133
#: tcms/testplans/templates/testplans/search.html:88
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:9
#: tcms/testruns/templates/testruns/get.html:66
#: tcms/testruns/templates/testruns/mutable.html:36
#: tcms/testruns/templates/testruns/search.html:73
#: tcms/testruns/templates/testruns/search.html:189
msgid "Default tester"
msgstr "Alapértelmezett tesztelő"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
msgid "TC"
msgstr ""

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
msgid "TR"
msgstr ""

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:37
#: tcms/testruns/templates/testruns/get.html:268
#: tcms/testruns/templates/testruns/get.html:375
msgid "Tested by"
msgstr "Tesztelte"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:39
#: tcms/testruns/templates/testruns/get.html:96
#: tcms/testruns/templates/testruns/get.html:405
#: tcms/testruns/templates/testruns/mutable.html:140
msgid "Finished at"
msgstr "Befejezve"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:5
msgid "Execution Trends"
msgstr "Végrehajtási trendek"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:21
msgid "Positive"
msgstr "Pozitív"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:24
msgid "Neutral"
msgstr "Semleges"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:27
msgid "Negative"
msgstr "Negatív"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:5
msgid "Execution Matrix"
msgstr "Végrehajtási mátrix"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:21
msgid "Order"
msgstr "Rendelés"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Ascending"
msgstr "Növekvő"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Descending"
msgstr "Csökkenő"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
msgid "Test case"
msgstr "Teszteset"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:12
msgid "Most frequently failing test cases"
msgstr "Leggyakoribb sikertelen tesztek"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:16
msgid "Test Case"
msgstr "Teszteset"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:18
msgid "Failed executions"
msgstr "Sikertelen végrehajtások"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:19
#, python-format
msgid "%% of failed executions"
msgstr "%% sikertelen végrehajtások"

#: tcms/templates/404.html:5 tcms/templates/404.html:16
msgid "Page not found"
msgstr "Az oldal nem található"

#: tcms/templates/500.html:5 tcms/templates/500.html:16
msgid "Internal Server Error"
msgstr "Belső szerverhiba"

#: tcms/templates/attachments/add.html:3
msgid "Attachment upload error"
msgstr "Csatolmány feltöltése hibára futott"

#: tcms/templates/attachments/delete_link.html:3
msgid "Are you sure you want to delete this attachment?"
msgstr "Biztosan törölhető ez a melléklet?"

#: tcms/templates/base.html:8
msgid "day"
msgstr "nap"

#: tcms/templates/base.html:8
msgid "days"
msgstr "napok"

#: tcms/templates/base.html:9
msgid "hour"
msgstr "óra"

#: tcms/templates/base.html:9
msgid "hours"
msgstr "órák"

#: tcms/templates/base.html:10
msgid "minute"
msgstr "perc"

#: tcms/templates/base.html:10
msgid "minutes"
msgstr "percek"

#: tcms/templates/base.html:11
msgid "second"
msgstr "másodperc"

#: tcms/templates/base.html:11
msgid "seconds"
msgstr "másodpercek"

#: tcms/templates/base.html:16 tcms/templates/registration/login.html:46
msgid "the leading open source test case management system"
msgstr "a vezető nyílt forráskódú teszteset kezelő rendszer"

#: tcms/templates/email/confirm_registration.txt:1
#, python-format
msgid "Welcome to Kiwi TCMS!\n\n"
"To confirm email address for username `%(user)s` and activate your account\n"
"please follow this URL:\n"
"%(confirm_url)s\n\n"
"Regards,\n"
"Kiwi TCMS"
msgstr ""

#: tcms/templates/email/post_bug_save/email.txt:2
#, python-format
msgid "Bug %(pk)s has been updated.\n\n"
"Link: %(bug_url)s\n\n"
"Summary: %(summary)s\n"
"Created at: %(creation_date)s\n"
"Reporter: %(reporter)s\n"
"Assignee: %(assignee)s\n"
"Product: %(product)s\n"
"Version: %(version)s\n"
"Build: %(build)s\n"
"Last comment:\n"
"%(last_comment)s"
msgstr ""

#: tcms/templates/email/post_case_delete/email.txt:2
#, python-format
msgid "TestCase has been deleted by %(username)s!"
msgstr "Tesztesetet törölte %(username)s!"

#: tcms/templates/email/post_run_save/email.txt:2
#, python-format
msgid "Test run %(pk)s has been created or updated for you.\n\n"
"### Links ###\n"
"Test run: %(run_url)s\n"
"Test plan: %(plan_url)s\n\n"
"### Basic run information ###\n"
"Summary: %(summary)s\n\n"
"Managed: %(manager)s.\n"
"Default tester: %(default_tester)s.\n\n"
"Product: %(product)s\n"
"Product version: %(version)s\n"
"Build: %(build)s\n\n"
"Notes:\n"
"%(notes)s"
msgstr "Tesztfuttatás %(pk)s létrehozva vagy frissítve lett.\n\n"
"### Linkek ###\n"
"Tesztfuttatás: %(run_url)s\n"
"Tesztterv: %(plan_url)s\n\n"
"### Alapvető futtatási információk ###\n"
"Összefoglalás: %(summary)s\n\n"
"Kezelő: %(manager)s.\n"
"Alapértelmezett tesztelő: %(default_tester)s.\n\n"
"Termék: %(product)s\n"
"Termék verzió: %(version)s\n"
"Build: %(build)s\n\n"
"Megjegyzések:\n"
"%(notes)s"

#: tcms/templates/email/user_registered/notify_admins.txt:2
#, python-format
msgid "Dear Administrator,\n"
"somebody just registered an account with username %(username)s at your\n"
"Kiwi TCMS instance and is awaiting your approval!\n\n"
"Go to %(user_url)s to activate the account!"
msgstr "Kedves Adminisztrátor,\n"
"A következő felhasználói fiók regisztrálásra került, és jóváhagyásra vár a Kiwi TCMS-ben. Felhasználónév %(username)s.\n\n"
"A fiók aktiválásához kattints ide %(user_url)s!"

#: tcms/templates/include/attachments.html:10
#: tcms/testplans/templates/testplans/get.html:437
#: tcms/testruns/templates/testruns/get.html:487
msgid "Attachments"
msgstr "Mellékletek"

#: tcms/templates/include/attachments.html:18
msgid "File"
msgstr "Fájl"

#: tcms/templates/include/attachments.html:19
msgid "Owner"
msgstr "Tulajdonos"

#: tcms/templates/include/attachments.html:20
msgid "Date"
msgstr "Dátum"

#: tcms/templates/include/attachments.html:35
#: tcms/templates/include/bugs_table.html:4
#: tcms/testplans/templates/testplans/get.html:332
msgid "No records found"
msgstr "Nincs találat"

#: tcms/templates/include/bugs_table.html:15
#: tcms/testruns/templates/testruns/get.html:573
msgid "URL"
msgstr "URL"

#: tcms/templates/include/properties_card.html:9 tcms/testruns/admin.py:117
#: tcms/testruns/templates/testruns/get.html:345
msgid "Parameters"
msgstr "Paraméterek"

#: tcms/templates/include/properties_card.html:15
#: tcms/testruns/templates/testruns/mutable.html:170
msgid "This is a tech-preview feature!"
msgstr "Ez egy technikai előnézet funkció!"

#: tcms/templates/include/properties_card.html:68
msgid "name=value"
msgstr "név=érték"

#: tcms/templates/include/properties_card.html:69
#: tcms/templates/include/tags_card.html:28
#: tcms/testcases/templates/testcases/get.html:184
#: tcms/testcases/templates/testcases/get.html:229
#: tcms/testplans/templates/testplans/get.html:258
#: tcms/testruns/templates/testruns/get.html:292
#: tcms/testruns/templates/testruns/get.html:546
msgid "Add"
msgstr "Hozzáad"

#: tcms/templates/include/tags_card.html:13
#: tcms/testcases/templates/testcases/get.html:167
#: tcms/testcases/templates/testcases/get.html:215
#: tcms/testplans/templates/testplans/clone.html:14
#: tcms/testplans/templates/testplans/get.html:205
#: tcms/testplans/templates/testplans/mutable.html:24
#: tcms/testplans/templates/testplans/search.html:18
#: tcms/testruns/templates/testruns/get.html:579
msgid "Name"
msgstr "Név"

#: tcms/templates/initdb.html:5 tcms/templates/initdb.html:17
#: tcms/templates/initdb.html:29
msgid "Initialize database"
msgstr "Adatbázis inicializálása"

#: tcms/templates/initdb.html:20
msgid "Your database has not been initialized yet. Click the button below to initialize it!"
msgstr "Az adatbázis még nem lett inicializálva. Kattintson az alábbi gombra az inicializáláshoz!"

#: tcms/templates/initdb.html:22
msgid "WARNING: this operation will take a while! This page will redirect when done."
msgstr "FIGYELEM: ez a művelet eltart egy darabig! Ez az oldal átirányít, ha elkészült."

#: tcms/templates/initdb.html:27
msgid "Please wait"
msgstr "Kérem várjon"

#: tcms/templates/navbar.html:9
msgid "Toggle navigation"
msgstr "Navigációs mód"

#: tcms/templates/navbar.html:15
msgid "DASHBOARD"
msgstr "VEZÉRLŐPULT"

#: tcms/templates/navbar.html:41
msgid "Language"
msgstr "Nyelv"

#: tcms/templates/navbar.html:45
msgid "Change language"
msgstr "Nyelv választása"

#: tcms/templates/navbar.html:46
msgid "Supported languages"
msgstr "Támogatott nyelvek"

#: tcms/templates/navbar.html:47
msgid "Request new language"
msgstr "Új nyelv kérése"

#: tcms/templates/navbar.html:53
msgid "Translation mode"
msgstr "Fordítási mód"

#: tcms/templates/navbar.html:57
msgid "Translation guide"
msgstr "Fordítási útmutató"

#: tcms/templates/navbar.html:63
msgid "Help"
msgstr "Súgó"

#: tcms/templates/navbar.html:78
msgid "Welcome Guest"
msgstr "Üdvözöllek Vendég"

#: tcms/templates/navbar.html:84
msgid "My Test Runs"
msgstr "Teszt futtatásaim"

#: tcms/templates/navbar.html:88
msgid "My Test Plans"
msgstr "Tesztterveim"

#: tcms/templates/navbar.html:94
msgid "My profile"
msgstr "Profil"

#: tcms/templates/navbar.html:98
#: tcms/templates/registration/password_reset_confirm.html:29
msgid "Change password"
msgstr "Jelszó megváltoztatása"

#: tcms/templates/navbar.html:108
msgid "Logout"
msgstr "Kijelentkezés"

#: tcms/templates/navbar.html:113 tcms/templates/registration/login.html:4
msgid "Login"
msgstr "Bejelentkezés"

#: tcms/templates/navbar.html:120
#: tcms/templates/registration/registration_form.html:53
msgid "Register"
msgstr "Regisztráció"

#: tcms/templates/registration/login.html:24
#: tcms/templates/registration/password_reset_confirm.html:15
#: tcms/templates/registration/registration_form.html:23
msgid "Password"
msgstr "Jelszó"

#: tcms/templates/registration/login.html:31
msgid "Forgot password"
msgstr "Elfelejtett jelszó"

#: tcms/templates/registration/login.html:34
msgid "Log in"
msgstr "Bejelentkezés"

#: tcms/templates/registration/login.html:45
msgid "Welcome to Kiwi TCMS"
msgstr "Üdvözöl a Kiwi TCMS"

#: tcms/templates/registration/login.html:50
msgid "Please login to get started"
msgstr "A kezdéshez be kell jelentkezni"

#: tcms/templates/registration/login.html:52
msgid "or"
msgstr "vagy"

#: tcms/templates/registration/login.html:53
msgid "register an account"
msgstr "fiók regisztrálása"

#: tcms/templates/registration/login.html:54
msgid "if you don't have one!"
msgstr "ha még nincs!"

#: tcms/templates/registration/password_reset_complete.html:12
msgid "Your password has been set. You may go ahead and"
msgstr "Jelszó beállítva. Tovább lehet lépni és"

#: tcms/templates/registration/password_reset_complete.html:13
msgid "now"
msgstr "most"

#: tcms/templates/registration/password_reset_confirm.html:43
msgid "Please enter your new password twice so we can verify you typed it in correctly"
msgstr "A jelszót kétszer kell megadni, így ellenőrizhetjük, hogy helyesen lett-e begépelve"

#: tcms/templates/registration/password_reset_confirm.html:46
msgid "request a new password reset"
msgstr "új jelszó visszaállítás kérése"

#: tcms/templates/registration/password_reset_done.html:11
msgid "Password reset email was sent"
msgstr "Jelszó visszaállításához szükséges email elküldve"

#: tcms/templates/registration/password_reset_form.html:27
msgid "Password reset"
msgstr "Jelszó visszaállítása"

#: tcms/templates/registration/password_reset_form.html:34
msgid "Kiwi TCMS password reset"
msgstr "Kiwi TCMS jelszó visszaállítása"

#: tcms/templates/registration/registration_form.html:4
msgid "Register new account"
msgstr "Új fiók regisztrálása"

#: tcms/testcases/admin.py:28
msgid "For more information about customizing test case statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-case-statuses\">\n"
"        the documentation</a>!"
msgstr "A teszteset állapotok testreszabásáról további információ a \n"
"<a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-case-statuses\">\n"
"dokumentációban</a> található!"

#: tcms/testcases/admin.py:56
msgid "1 confirmed & 1 uncomfirmed status required!"
msgstr "1 megerősített & 1 nem megerősített státusz szükséges!"

#: tcms/testcases/admin.py:131
msgid "Bug URL"
msgstr "Hiba URL"

#: tcms/testcases/admin.py:151
msgid "External Issue Tracker Integration"
msgstr "Külső Issue Tracker integráció"

#: tcms/testcases/admin.py:161
msgid "<h1>Warning: read the\n"
"<a href=\"http://kiwitcms.readthedocs.io/en/latest/admin.html#configure-external-bug-trackers\">\n"
"Configure external bug trackers</a> section before editting the values below!</h1>"
msgstr "<h1>Figyelmeztetés: olvassa el\n"
"<a href=\"http://kiwitcms.readthedocs.io/en/latest/admin.html#configure-external-bug-trackers\">\n"
"Külső hibakövetők konfigurálása</a> fejezetet mielőtt szerkesztené az alábbi paramétereket!</h1>"

#: tcms/testcases/admin.py:168
msgid "Configuration health check"
msgstr "Konfiguráció állapotellenőrzés"

#: tcms/testcases/admin.py:172
msgid "Kiwi TCMS will try fetching details for the given bug URL using the integration defined above! Click the `Save and continue` button and watch out for messages at the top of the screen. <strong>WARNING:</strong> in case of failures some issue trackers will fall back to fetching details via the OpenGraph protocol. In that case the result will include field named `from_open_graph`."
msgstr "A Kiwi TCMS megpróbálja lekérni a megadott hiba URL címének részleteit a fent meghatározott integráció segítségével! Kattintson a \"Mentés és folytatás\" gombra, és figyeljen a képernyő tetején megjelenő üzenetekre. <strong>FIGYELMEZTETÉS:</strong> hiba esetén egyes problémakövetők visszalépnek az OpenGraph protokollon keresztül történő adatlekérdezéshez. Ebben az esetben az eredmény tartalmazza a \"from_open_graph\" nevű mezőt."

#: tcms/testcases/admin.py:192
msgid "Failed creating Issue Tracker"
msgstr "Nem sikerült létrehozni Issue Trackert"

#: tcms/testcases/admin.py:201
msgid "Details extracted via OpenGraph. Issue Tracker may still be configured incorrectly!"
msgstr ""

#: tcms/testcases/admin.py:210
msgid "Details extracted via API. Issue Tracker configuration looks good!"
msgstr ""

#: tcms/testcases/admin.py:223
msgid "Issue Tracker configuration check failed"
msgstr "Az Issue Tracker konfiguráció ellenőrzése sikertelen"

#: tcms/testcases/helpers/email.py:22
#, python-format
msgid "DELETED: TestCase #%(pk)d - %(summary)s"
msgstr "TÖRÖLT: Teszteset #%(pk)d - %(summary)s"

#: tcms/testcases/models.py:23
msgid "Test case status"
msgstr "Teszteset státusz"

#: tcms/testcases/models.py:24
msgid "Test case statuses"
msgstr "Teszteset státuszok"

#: tcms/testcases/models.py:379
#: tcms/testcases/templates/testcases/mutable.html:107
msgid "Template"
msgstr "Sablon"

#: tcms/testcases/models.py:380
msgid "Templates"
msgstr "Sablonok"

#: tcms/testcases/templates/testcases/clone.html:5
msgid "Clone TestCase"
msgstr "Teszteset klónozása"

#: tcms/testcases/templates/testcases/clone.html:15
msgid "Add new TC into TP"
msgstr "Új Teszteset hozzáadása a Teszttervhez"

#: tcms/testcases/templates/testcases/clone.html:30
msgid "Selected TC"
msgstr "Kiválasztott Teszteset"

#: tcms/testcases/templates/testcases/clone.html:45 tcms/testcases/views.py:134
#: tcms/testplans/templates/testplans/clone.html:73
#: tcms/testplans/templates/testplans/get.html:138
#: tcms/testplans/templates/testplans/get.html:358 tcms/testplans/views.py:144
#: tcms/testruns/views.py:198
msgid "Clone"
msgstr "Klón"

#: tcms/testcases/templates/testcases/get.html:28
#: tcms/testcases/templates/testcases/get.html:168
#: tcms/testcases/templates/testcases/mutable.html:184
#: tcms/testcases/templates/testcases/search.html:122
#: tcms/testcases/templates/testcases/search.html:169
#: tcms/testplans/templates/testplans/get.html:54
#: tcms/testplans/templates/testplans/get.html:412
#: tcms/testplans/templates/testplans/get.html:492
#: tcms/testplans/templates/testplans/mutable.html:121
#: tcms/testplans/templates/testplans/search.html:82
#: tcms/testplans/templates/testplans/search.html:120
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:8
#: tcms/testruns/templates/testruns/mutable.html:230
msgid "Author"
msgstr "Szerző"

#: tcms/testcases/templates/testcases/get.html:49
#: tcms/testcases/templates/testcases/mutable.html:58
#: tcms/testcases/templates/testcases/search.html:100
#: tcms/testcases/templates/testcases/search.html:164
#: tcms/testplans/templates/testplans/get.html:408
#: tcms/testplans/templates/testplans/get.html:491
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:7
#: tcms/testruns/templates/testruns/get.html:266
#: tcms/testruns/templates/testruns/get.html:367
#: tcms/testruns/templates/testruns/mutable.html:233
msgid "Category"
msgstr "Kategória"

#: tcms/testcases/templates/testcases/get.html:64
#: tcms/testcases/templates/testcases/mutable.html:86
#: tcms/testcases/templates/testcases/search.html:82
#: tcms/testcases/templates/testcases/search.html:166
#: tcms/testplans/templates/testplans/get.html:162
#: tcms/testplans/templates/testplans/get.html:371
#: tcms/testplans/templates/testplans/get.html:404
#: tcms/testplans/templates/testplans/get.html:490
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:6
#: tcms/testruns/templates/testruns/get.html:265
#: tcms/testruns/templates/testruns/get.html:363
#: tcms/testruns/templates/testruns/mutable.html:234
msgid "Priority"
msgstr "Prioritás"

#: tcms/testcases/templates/testcases/get.html:73
#: tcms/testcases/templates/testcases/mutable.html:120
msgid "Setup duration"
msgstr "Beállítás időtartama"

#: tcms/testcases/templates/testcases/get.html:78
#: tcms/testcases/templates/testcases/mutable.html:127
msgid "Testing duration"
msgstr "Tesztelés időtartama"

#: tcms/testcases/templates/testcases/get.html:83
msgid "Expected duration"
msgstr "Várható időtartam"

#: tcms/testcases/templates/testcases/get.html:98
#: tcms/testcases/templates/testcases/mutable.html:143
msgid "Script"
msgstr "Szkript"

#: tcms/testcases/templates/testcases/get.html:103
#: tcms/testcases/templates/testcases/mutable.html:149
msgid "Arguments"
msgstr "Paraméterek"

#: tcms/testcases/templates/testcases/get.html:108
#: tcms/testcases/templates/testcases/mutable.html:157
msgid "Requirements"
msgstr "Követelmények"

#: tcms/testcases/templates/testcases/get.html:113
#: tcms/testcases/templates/testcases/mutable.html:162
#: tcms/testplans/templates/testplans/get.html:80
#: tcms/testplans/templates/testplans/mutable.html:102
msgid "Reference link"
msgstr "Referencia link"

#: tcms/testcases/templates/testcases/get.html:133
#: tcms/testcases/templates/testcases/mutable.html:170
#: tcms/testplans/templates/testplans/get.html:433
#: tcms/testplans/templates/testplans/get.html:434
#: tcms/testruns/templates/testruns/get.html:400
#: tcms/testruns/templates/testruns/mutable.html:202
msgid "Notes"
msgstr "Megjegyzések"

#: tcms/testcases/templates/testcases/get.html:152
msgid "Bugs"
msgstr "Hibák"

#: tcms/testcases/templates/testcases/get.html:159
msgid "Test plans"
msgstr "Teszttervek"

#: tcms/testcases/templates/testcases/mutable.html:10
msgid "Edit TestCase"
msgstr "Teszteset szerkesztése"

#: tcms/testcases/templates/testcases/mutable.html:59
msgid "add new Category"
msgstr "új Kategória hozzáadása"

#: tcms/testcases/templates/testcases/mutable.html:108
msgid "add new Template"
msgstr "új Sablon hozzáadása"

#: tcms/testcases/templates/testcases/mutable.html:180
#: tcms/testruns/templates/testruns/get.html:517
msgid "Notify"
msgstr "Értesítés"

#: tcms/testcases/templates/testcases/mutable.html:191
msgid "Manager of runs"
msgstr "Futtatások kezelője"

#: tcms/testcases/templates/testcases/mutable.html:198
msgid "Asignees"
msgstr "Felelősök"

#: tcms/testcases/templates/testcases/mutable.html:214
msgid "Default tester of runs"
msgstr "Futtatások alapértelmezett tesztelője"

#: tcms/testcases/templates/testcases/mutable.html:223
msgid "Notify when"
msgstr "Értesítsen, amikor"

#: tcms/testcases/templates/testcases/mutable.html:224
#: tcms/testplans/templates/testplans/mutable.html:142
msgid "applies only for changes made by somebody else"
msgstr ""

#: tcms/testcases/templates/testcases/mutable.html:229
msgid "TestCase is updated"
msgstr "Tesztkészlet frissítve"

#: tcms/testcases/templates/testcases/mutable.html:236
msgid "TestCase is deleted"
msgstr "Teszteset törölve"

#: tcms/testcases/templates/testcases/mutable.html:245
msgid "CC to"
msgstr "CC"

#: tcms/testcases/templates/testcases/mutable.html:250
msgid "Email addresses separated by comma. A notification email will be sent to each Email address within CC list."
msgstr "Email címek vesszővel elválasztva. Egy értesítő levél lesz elküldve a CC listában található összes email címre."

#: tcms/testcases/templates/testcases/search.html:15
msgid "Test case summary"
msgstr "Teszteset összefoglaló"

#: tcms/testcases/templates/testcases/search.html:45
msgid "Both"
msgstr "Mindkettő"

#: tcms/testcases/templates/testcases/search.html:61
msgid "include in search request"
msgstr ""

#: tcms/testcases/templates/testcases/search.html:74
msgid "include child test plans"
msgstr ""

#: tcms/testcases/templates/testcases/search.html:112
#: tcms/testcases/templates/testcases/search.html:165
#: tcms/testplans/templates/testplans/get.html:216
#: tcms/testruns/templates/testruns/get.html:262
msgid "Component"
msgstr "Komponens"

#: tcms/testcases/templates/testcases/search.html:128
msgid "Text"
msgstr "Szöveg"

#: tcms/testcases/templates/testcases/search.html:139
#: tcms/testplans/templates/testplans/search.html:97
#: tcms/testruns/templates/testruns/search.html:30
msgid "Separate multiple values with comma (,)"
msgstr "Több érték elválasztása vesszővel (,)"

#: tcms/testcases/templates/testcases/search.html:178
msgid "Select"
msgstr "Választ"

#: tcms/testcases/views.py:138 tcms/testplans/views.py:146
#: tcms/testruns/templates/testruns/get.html:458 tcms/testruns/views.py:202
msgid "History"
msgstr "Előzmények"

#: tcms/testcases/views.py:250
msgid "TestCase cloning was successful"
msgstr "A teszteset klónozása sikeres volt"

#: tcms/testcases/views.py:281
msgid "At least one TestCase is required"
msgstr "Legalább egy Teszteset szükséges"

#: tcms/testplans/templates/testplans/clone.html:5
msgid "Clone TestPlan"
msgstr "Tesztterv klónozása"

#: tcms/testplans/templates/testplans/clone.html:55
msgid "Clone TCs"
msgstr "Tesztesetek klónozása"

#: tcms/testplans/templates/testplans/clone.html:59
msgid "Clone or link existing TCs into new TP"
msgstr "A meglévő Tesztesetek összekapcsolása vagy klónozása új Teszttervbe"

#: tcms/testplans/templates/testplans/clone.html:63
msgid "Parent TP"
msgstr "Szülő Tesztterv"

#: tcms/testplans/templates/testplans/clone.html:67
msgid "Set the source TP as parent of new TP"
msgstr "Forrás Tesztterv beállítása mint az új Tesztterv szülője"

#: tcms/testplans/templates/testplans/get.html:25
#: tcms/testruns/templates/testruns/get.html:23
msgid "Enter username, email or user ID:"
msgstr "Felhasználónév, e-mail cím, vagy a felhasználó ID megadása:"

#: tcms/testplans/templates/testplans/get.html:26
#: tcms/testruns/templates/testruns/get.html:22
msgid "No rows selected! Please select at least one!"
msgstr "Nincs sor kiválasztva! Válasszon legalább egyet!"

#: tcms/testplans/templates/testplans/get.html:27
#: tcms/testruns/templates/testruns/get.html:24
msgid "Are you sure?"
msgstr "Biztos benne?"

#: tcms/testplans/templates/testplans/get.html:28
msgid "Cannot create TestRun with unconfirmed test cases"
msgstr "Nem hozható létre Tesztfuttatás meg nem erősített Tesztesetekkel"

#: tcms/testplans/templates/testplans/get.html:29
msgid "Error adding test cases"
msgstr "Hiba a teszteset hozzáadása közben"

#: tcms/testplans/templates/testplans/get.html:43
msgid "Show more"
msgstr "Több mutatása"

#: tcms/testplans/templates/testplans/get.html:75
msgid "Plan Type"
msgstr "Terv típusa"

#: tcms/testplans/templates/testplans/get.html:100
msgid "Test cases"
msgstr "Tesztesetek"

#: tcms/testplans/templates/testplans/get.html:182
#: tcms/testplans/templates/testplans/get.html:421
#: tcms/testplans/templates/testplans/get.html:494
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:10
msgid "Reviewer"
msgstr "Áttekintő"

#: tcms/testplans/templates/testplans/get.html:231
#: tcms/testplans/templates/testplans/get.html:234
msgid "Sort key"
msgstr "Rendezési kulcs"

#: tcms/testplans/templates/testplans/get.html:244
msgid "Re-order cases"
msgstr "Esetek újrarendezése"

#: tcms/testplans/templates/testplans/get.html:254
#: tcms/testruns/templates/testruns/get.html:288
msgid "Search and add test cases"
msgstr "Teszteset keresése és hozzáadása"

#: tcms/testplans/templates/testplans/get.html:263
#: tcms/testruns/templates/testruns/get.html:298
msgid "Advanced search"
msgstr "Részletes keresés"

#: tcms/testplans/templates/testplans/get.html:286
msgid "Active test runs"
msgstr "Aktív teszt futtatások"

#: tcms/testplans/templates/testplans/get.html:308
msgid "More"
msgstr "Több"

#: tcms/testplans/templates/testplans/get.html:310
msgid "Inactive"
msgstr "Inaktív"

#: tcms/testplans/templates/testplans/get.html:444
#: tcms/testruns/templates/testruns/get.html:494
msgid "No attachments"
msgstr "Nincsenek mellékletek"

#: tcms/testplans/templates/testplans/get.html:469
#: tcms/testruns/templates/testruns/get.html:483
msgid "Comments"
msgstr "Hozzászólások"

#: tcms/testplans/templates/testplans/mutable.html:10
msgid "Edit TestPlan"
msgstr "Tesztterv szerkesztése"

#: tcms/testplans/templates/testplans/mutable.html:12
msgid "Create new TestPlan"
msgstr "Új Tesztterv készítése"

#: tcms/testplans/templates/testplans/mutable.html:82
msgid "Parent ID"
msgstr "Szülő ID"

#: tcms/testplans/templates/testplans/mutable.html:100
msgid "Enter to assign; Backspace + Enter to clear"
msgstr ""

#: tcms/testplans/templates/testplans/mutable.html:110
msgid "Test plan document:"
msgstr "Tesztterv leírás:"

#: tcms/testplans/templates/testplans/mutable.html:119
msgid "Notify:"
msgstr "Értesítés:"

#: tcms/testplans/templates/testplans/mutable.html:127
msgid "TestCase author"
msgstr "Tesztkészlet szerző"

#: tcms/testplans/templates/testplans/mutable.html:141
msgid "Notify when:"
msgstr "Értesítés, amikor:"

#: tcms/testplans/templates/testplans/mutable.html:145
msgid "TestPlan is updated"
msgstr "Tesztterv frissítésre került"

#: tcms/testplans/templates/testplans/mutable.html:151
msgid "Test cases are updated"
msgstr "Tesztesetek frissítésre kerültek"

#: tcms/testplans/templates/testplans/mutable.html:165
#: tcms/testplans/templates/testplans/search.html:41
msgid "Active"
msgstr "Aktív"

#: tcms/testplans/templates/testplans/search.html:12
msgid "Some child test plans do not match search criteria"
msgstr ""

#: tcms/testplans/templates/testplans/search.html:20
msgid "Test plan name"
msgstr "Tesztterv neve"

#: tcms/testruns/admin.py:32
msgid "Permission denied: TestRun does not belong to you"
msgstr "Hozzáférés megtagadva: A teszt futtatás nem hozzád tartozik"

#: tcms/testruns/admin.py:39
msgid "For more information about customizing test execution statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-execution-statuses\">\n"
"        the documentation</a>!"
msgstr "További információk a tesztfuttatás státuszainak testreszabásáról lásd\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-execution-statuses\">\n"
"        dokumentációt</a>!"

#: tcms/testruns/admin.py:95
msgid "1 negative, 1 neutral & 1 positive status required!"
msgstr "1 negatív, 1 semleges és 1 pozitív állapot szükséges!"

#: tcms/testruns/admin.py:106
msgid "Edit parameters"
msgstr "Paraméterek szerkesztése"

#: tcms/testruns/forms.py:39
msgid "Full"
msgstr "Teljes"

#: tcms/testruns/forms.py:40
msgid "Pairwise"
msgstr "Párosítva"

#: tcms/testruns/models.py:230
msgid "Test execution statuses"
msgstr "Teszt végrehajtás állapotok"

#: tcms/testruns/templates/testruns/get.html:25
msgid "Unconfirmed test cases were not added"
msgstr "A nem megerősített tesztesetek nem kerültek hozzáadásra"

#: tcms/testruns/templates/testruns/get.html:26
msgid "Type 0 or 1"
msgstr "0 vagy 1 típus"

#: tcms/testruns/templates/testruns/get.html:27
msgid "Comment"
msgstr "Hozzászólás"

#: tcms/testruns/templates/testruns/get.html:60
#: tcms/testruns/templates/testruns/mutable.html:29
#: tcms/testruns/templates/testruns/search.html:67
#: tcms/testruns/templates/testruns/search.html:188
msgid "Manager"
msgstr "Menedzser"

#: tcms/testruns/templates/testruns/get.html:73
#: tcms/testruns/templates/testruns/mutable.html:110
#: tcms/testruns/templates/testruns/search.html:127
msgid "Planned start"
msgstr "Tervezett indítás"

#: tcms/testruns/templates/testruns/get.html:84
msgid "Start"
msgstr "Indítás"

#: tcms/testruns/templates/testruns/get.html:91
#: tcms/testruns/templates/testruns/mutable.html:123
#: tcms/testruns/templates/testruns/search.html:147
msgid "Planned stop"
msgstr "Tervezett befejezés"

#: tcms/testruns/templates/testruns/get.html:102
msgid "Stop"
msgstr "Befejezés"

#: tcms/testruns/templates/testruns/get.html:139
#: tcms/testruns/templates/testruns/mutable.html:160
msgid "Environment"
msgstr "Környezet"

#: tcms/testruns/templates/testruns/get.html:182
msgid "Update text version"
msgstr "Szöveges verzió frissítése"

#: tcms/testruns/templates/testruns/get.html:217
msgid "Add comment"
msgstr "Megjegyzés hozzáadása"

#: tcms/testruns/templates/testruns/get.html:228
#: tcms/testruns/templates/testruns/get.html:331
#: tcms/testruns/templates/testruns/get.html:568
msgid "Add hyperlink"
msgstr "Hivatkozás hozzáadása"

#: tcms/testruns/templates/testruns/get.html:279
msgid "Mine"
msgstr "Saját"

#: tcms/testruns/templates/testruns/get.html:280
msgid "All"
msgstr "Mind"

#: tcms/testruns/templates/testruns/get.html:306
msgid "records"
msgstr "rekordok"

#: tcms/testruns/templates/testruns/get.html:332
#: tcms/testruns/templates/testruns/get.html:607
#: tcms/testruns/templates/testruns/get.html:624
msgid "Report bug"
msgstr "Hibajelentés"

#: tcms/testruns/templates/testruns/get.html:339
msgid "Test case is not part of parent test plan"
msgstr "A teszteset nem része a szülő teszttervnek"

#: tcms/testruns/templates/testruns/get.html:371
msgid "Assigned to"
msgstr "Hozzárendelve"

#: tcms/testruns/templates/testruns/get.html:379
#: tcms/testruns/templates/testruns/get.html:381
msgid "Last bug"
msgstr ""

#: tcms/testruns/templates/testruns/get.html:423
msgid "Text version"
msgstr "Szöveges változat"

#: tcms/testruns/templates/testruns/get.html:431
msgid "Bugs and hyperlinks"
msgstr "Hibajegyek és hiperhivatkozások"

#: tcms/testruns/templates/testruns/get.html:585
msgid "Is a defect"
msgstr "Hiba"

#: tcms/testruns/templates/testruns/get.html:592
#: tcms/testruns/templates/testruns/get.html:623
msgid "Cancel"
msgstr "Mégse"

#: tcms/testruns/templates/testruns/get.html:612
msgid "Issue Tracker"
msgstr "Hibajegy követő"

#: tcms/testruns/templates/testruns/mutable.html:7
msgid "Edit TestRun"
msgstr "Tesztfuttatás szerkesztése"

#: tcms/testruns/templates/testruns/mutable.html:9
msgid "Clone TestRun"
msgstr "Tesztfuttatás klónozása"

#: tcms/testruns/templates/testruns/mutable.html:180
msgid "Affects only test cases with parameters"
msgstr ""

#: tcms/testruns/templates/testruns/mutable.html:194
msgid "more information"
msgstr "további információ"

#: tcms/testruns/templates/testruns/mutable.html:218
msgid "Selected TestCase(s):"
msgstr "Kiválasztott teszteset(ek):"

#: tcms/testruns/templates/testruns/mutable.html:221
#, python-format
msgid "%(count)s of the pre-selected test cases is not CONFIRMED and will not be cloned!\n"
"See test plan for more details!"
msgstr "%(count)s az előre kiválasztott tesztesetek közül nincs MEGERŐSÍTVE és nem lesz klónozva!\n"
"További részletekért lásd a teszttervet!"

#: tcms/testruns/templates/testruns/search.html:21
msgid "Plan ID"
msgstr "Terv ID"

#: tcms/testruns/templates/testruns/search.html:23
msgid "TestPlan ID"
msgstr "Tesztterv ID"

#: tcms/testruns/templates/testruns/search.html:79
msgid "Running"
msgstr "Folyamatban"

#: tcms/testruns/templates/testruns/search.html:86
#: tcms/testruns/templates/testruns/search.html:186
msgid "Start date"
msgstr "Indítás dátuma"

#: tcms/testruns/templates/testruns/search.html:106
#: tcms/testruns/templates/testruns/search.html:187
msgid "Stop date"
msgstr "Indítás dátuma"

#: tcms/testruns/views.py:273
msgid "Clone of "
msgstr "Klónozva a "

#: testcases.TestCaseStatus/name:1
msgid "PROPOSED"
msgstr "JAVASOLT"

#: testcases.TestCaseStatus/name:3
msgid "DISABLED"
msgstr "TILTOTT"

#: testcases.TestCaseStatus/name:4
msgid "NEED_UPDATE"
msgstr "FRISSÍTENI SZÜKSÉGES"

#: testruns.TestExecutionStatus/name:1
msgid "IDLE"
msgstr "TÉTLEN"

#: testruns.TestExecutionStatus/name:2
msgid "RUNNING"
msgstr "FOLYAMATBAN"

#: testruns.TestExecutionStatus/name:3
msgid "PAUSED"
msgstr "MEGÁLLÍTOTT"

#: testruns.TestExecutionStatus/name:4
msgid "PASSED"
msgstr "ÁTMENT"

#: testruns.TestExecutionStatus/name:5
msgid "FAILED"
msgstr "SIKERTELEN"

#: testruns.TestExecutionStatus/name:6
msgid "BLOCKED"
msgstr "ZÁROLT"

#: testruns.TestExecutionStatus/name:7
msgid "ERROR"
msgstr "HIBA"

#: testruns.TestExecutionStatus/name:8
msgid "WAIVED"
msgstr "LEMONDOTT"

#: tcms_github_app/admin.py:122
#, python-format
msgid "For additional configuration see\n"
"<a href=\"%s\">GitHub</a>"
msgstr "További konfigurációért lásd\n"
"<a href=\"%s\">GitHub</a>"

#: tcms_github_app/menu.py:11
msgid "GitHub integration"
msgstr "GitHub integráció"

#: tcms_github_app/menu.py:12
msgid "Resync"
msgstr "Újraszinkronizálás"

#: tcms_github_app/menu.py:13
msgid "Settings"
msgstr "Beállítások"

#: tcms_github_app/middleware.py:41
#, python-format
msgid "Unconfigured GitHub App %d"
msgstr "Nem konfigurált GitHub App %d"

#: tcms_github_app/utils.py:274
#, python-format
msgid "%s was imported from GitHub"
msgstr "%s lett importálva a GitHub-ról"

#: tcms_github_app/utils.py:278
#, python-format
msgid "%s already exists"
msgstr "%s már létezik"

#: tcms_github_app/views.py:48
#, python-format
msgid "You have not logged-in via GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "Még nem jelentkeztél be GitHub fiókkal! <a href=\"%s\">Kattints ide </a>!"

#: tcms_github_app/views.py:62
#, python-format
msgid "You have not installed Kiwi TCMS into your GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "A Kiwi TCMS még nincs telepítve a GitHub fiókba! <a href=\"%s\">Kattints ide </a>!"

#: tcms_github_app/views.py:76
msgid "Multiple GitHub App installations detected! See below:"
msgstr "Több GitHub App telepítés észlelve! Lásd alább:"

#: tcms_github_app/views.py:85
#, python-format
msgid "Edit GitHub App <a href=\"%s\">%s</a>"
msgstr "A GitHub App szerkesztése <a href=\"%s\">%s</a>"

#: tcms_github_app/views.py:102
#, python-format
msgid "Cannot find GitHub App installation for tenant \"%s\""
msgstr "A \"%s\" bérlő számára nincs feltelepítve a GitHub applikáció"

#: tcms_github_app/views.py:111
msgid "Multiple GitHub App installations detected!"
msgstr "Több GitHub App telepítés észlelve!"

#: tcms_github_marketplace/menu.py:11
msgid "Subscriptions"
msgstr "Feliratkozások"

#: tcms_github_marketplace/templates/tcms_github_marketplace/email/exit_poll.txt:1
msgid "Thank you for using Kiwi TCMS via a paid subscription.\n"
"We're sorry to see you go but we'd like to know why so we can improve in the future!\n\n"
"You can share your feedback at https://forms.gle/RJhFengbjN9C6wtUA\n\n"
"Thank you!"
msgstr ""

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:6
msgid "Tenant subscriptions"
msgstr "Bérlő előfizetések"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:15
msgid "You can access the following tenants"
msgstr "A következő bérlőket érheti el"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:20
msgid "Tenant"
msgstr "Bérlő"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:31
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:90
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:30
msgid "Organization"
msgstr "Szervezet"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:44
msgid "Docker credentials"
msgstr "Docker hozzáférési adatok"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:61
msgid "Private containers instructions"
msgstr ""

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:75
msgid "You own the following tenants"
msgstr "A következő bérlőkkel rendelkezel"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:97
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:98
msgid "Price"
msgstr "Ár"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:102
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:103
msgid "Subscription type"
msgstr "Előfizetés típusa"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:107
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:108
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:8
msgid "Paid until"
msgstr "Eddig fizetve"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:114
msgid "Cancel subscription"
msgstr "Előfizetés megszüntetése"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:127
msgid "You don't own any tenants"
msgstr "Nem rendelkezel bérlőkkel"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:131
msgid "Subscribe via FastSpring"
msgstr "Előfizetés FastSpringen keresztül"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:147
msgid "Transaction history"
msgstr "Tranzakció előzmények"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:169
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:170
msgid "Sender"
msgstr "Feladó"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:174
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:175
msgid "Vendor"
msgstr "Beszállító"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:179
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:180
msgid "Received on"
msgstr "Ekkor érkezett"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:4
msgid "Extra emails"
msgstr "További email címek"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:14
msgid "Kiwi TCMS will try to match recurring billing events against tenant.owner.email + tenant.extra_emails"
msgstr ""

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:17
msgid "Separate by comma (,), semi-colon (;) or white space ( )"
msgstr ""

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:44
msgid "Private Tenant Warning"
msgstr "Magánbérlő figyelmeztetés"

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:49
msgid "You are about to create a Private Tenant for Kiwi TCMS.\n"
"It will take a few minutes until your DB schema is ready!\n"
"After clicking the 'Save' button <strong>do not</strong> close or refresh this page!<br>\n"
"You will be redirected to your new tenant when the creation process is complete!\n"
"If you see a 500 Internal Server Error page please contact\n"
"<a href=\"mailto:<EMAIL>\"><EMAIL></a> immediately!"
msgstr "Arra készülsz, hogy magánbérlőt hozol létre a Kiwi TCMS számára. Az adatbázis séma létrehozásához néhány percre van szükség! Miután a 'Mentés' gombra kattintottál <strong>ne</strong> frissítsd vagy zárd be ezt az oldalt!<br>\n"
"Amikor a létrehozási folyamat befejeződött az új bérlőre leszel átirányítva.\n"
"Amennyiben 500-as belső szerver hibát kapsz, kérlek vedd fel azonnal a kapcsolatot <a href=\"mailto:<EMAIL>\"><EMAIL></a>!"

#: tcms_github_marketplace/utils.py:95
msgid "Kiwi TCMS Subscription Exit Poll"
msgstr ""

#: tcms_github_marketplace/views.py:567
msgid "Kiwi TCMS subscription notification"
msgstr ""

#: tcms_github_marketplace/views.py:749
msgid "mo"
msgstr "hó"

#: tcms_github_marketplace/views.py:752
msgid "yr"
msgstr "év"

#: tcms_enterprise/pipeline.py:17
msgid "Email address is required"
msgstr "Email cím megadása kötelező"

#: tcms_enterprise/templates/registration/custom_login.html:10
msgid "or Continue With"
msgstr "vagy folytatás ezzel"

#: tcms_settings_dir/enterprise.py:19
msgid "Legal information"
msgstr "Jogi információ"

#: tcms_tenants/admin.py:55 tcms_tenants/admin.py:62
#: tcms_tenants/middleware.py:35
msgid "Unauthorized"
msgstr "Jogosulatlan"

#: tcms_tenants/admin.py:86
msgid "Existing username, email or user ID"
msgstr "Meglévő felhasználónév, email vagy felhasználói azonosító"

#: tcms_tenants/admin.py:159
msgid "Full name"
msgstr "Teljes név"

#: tcms_tenants/forms.py:30
msgid "Invalid string"
msgstr "Érvénytelen karaktersorozat"

#: tcms_tenants/menu.py:15
msgid "Create"
msgstr "Létrehozás"

#: tcms_tenants/menu.py:20
#: tcms_tenants/templates/tcms_tenants/invite_users.html:17
msgid "Invite users"
msgstr "Felhasználók meghívása"

#: tcms_tenants/menu.py:21
msgid "Authorized users"
msgstr "Engedélyezett felhasználók"

#: tcms_tenants/middleware.py:59
msgid "Unpaid"
msgstr "Nem fizetett"

#: tcms_tenants/middleware.py:70
msgid "Tenant expires soon"
msgstr ""

#: tcms_tenants/templates/tcms_tenants/email/invite_user.txt:1
#, python-format
msgid "Dear tester,\n"
"%(invited_by)s has invited you to join their Kiwi TCMS tenant at\n"
"%(tenant_url)s\n\n"
"In case you have never logged in before an account was created for you\n"
"automatically. You can login with a social account which has the same email\n"
"address or go to %(password_reset_url)s to reset your password.\n"
"The password reset email message also contains your username!"
msgstr ""

#: tcms_tenants/templates/tcms_tenants/email/new.txt:1
#, python-format
msgid "Your Kiwi TCMS tenant was created at:\n"
"%(tenant_url)s\n\n"
"If you have troubles please contact support!"
msgstr "A Kiwi TCMS bérlő létrehozásra került:\n"
"%(tenant_url)s\n\n"
"Probléma esetén vedd fel a kapcsolatot a támogatókkal!"

#: tcms_tenants/templates/tcms_tenants/invite_users.html:28
msgid "Email"
msgstr "Email"

#: tcms_tenants/templates/tcms_tenants/new.html:18
msgid "New tenant"
msgstr "Új bérlő"

#: tcms_tenants/templates/tcms_tenants/new.html:35
msgid "Company, team or project name"
msgstr "Vállalat, csapat vagy projekt neve"

#: tcms_tenants/templates/tcms_tenants/new.html:43
msgid "Schema"
msgstr "Séma"

#: tcms_tenants/templates/tcms_tenants/new.html:56
msgid "Validation pattern"
msgstr "Érvényesítési minta"

#: tcms_tenants/templates/tcms_tenants/new.html:61
msgid "Publicly readable"
msgstr "Nyilvánosan olvasható"

#: tcms_tenants/templates/tcms_tenants/new.html:80
msgid "Tenant logo"
msgstr ""

#: tcms_tenants/utils.py:66
msgid "Schema name already in use"
msgstr "A sémanév már használatban van"

#: tcms_tenants/utils.py:170
msgid "New Kiwi TCMS tenant created"
msgstr "Új Kiwi TCMS bérlő létrehozva"

#: tcms_tenants/utils.py:230
#, python-brace-format
msgid "User {user.username} added to tenant group {group.name}"
msgstr ""

#: tcms_tenants/utils.py:262
msgid "Invitation to join Kiwi TCMS"
msgstr "Kiwi TCMS meghívó küldése"

#: tcms_tenants/views.py:84
msgid "Only super-user and tenant owner are allowed to edit tenant properties"
msgstr "Csak a szuper-felhasználó és a bérlő tulajdonosa szerkesztheti a bérlő tulajdonságait"

#: tcms_tenants/views.py:102
msgid "Edit tenant"
msgstr "Bérlő szerkesztése"

#: tcms_tenants/views.py:153
msgid "Only users who are authorized for this tenant can invite others"
msgstr "Csak azok a felhasználók hívhatnak meg másokat akik erre jogosultak"

#: tenant_groups/admin.py:30
msgid "users"
msgstr "felhasználók"

#: tenant_groups/models.py:34
msgid "name"
msgstr "név"

#: tenant_groups/models.py:37
msgid "permissions"
msgstr "jogosultság"

#: tenant_groups/models.py:47
msgid "group"
msgstr "csoport"

#: tenant_groups/models.py:48
msgid "groups"
msgstr "csoportok"

#: trackers_integration/menu.py:4
msgid "Personal API tokens"
msgstr "Egyéni API tóken"
