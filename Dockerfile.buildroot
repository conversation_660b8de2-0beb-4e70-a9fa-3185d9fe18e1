# checkov:skip=CKV_DOCKER_2:Ensure that HEALTHCHECK instructions have been added to container images
# checkov:skip=CKV_DOCKER_3:Ensure that a user for the container has been created
# checkov:skip=CKV_DOCKER_7:Ensure the base image uses a non latest version tag
FROM registry.access.redhat.com/ubi9-minimal

RUN microdnf -y module enable nodejs:22 && \
    microdnf -y --nodocs install python3.11-devel mariadb-connector-c-devel tar gzip make \
    postgresql-devel libffi-devel gcc gettext npm unzip which rust cargo findutils \
    libjpeg-turbo-devel && \
    microdnf -y --nodocs update && \
    microdnf clean all

COPY ./requirements/mariadb.pc /usr/lib64/pkgconfig/mariadb.pc

ENV PATH=/venv/bin:${PATH} \
    VIRTUAL_ENV=/venv

# Create a virtualenv for the application dependencies
RUN python3.11 -m venv /venv

# because we get some errors from other packages which need newer versions
RUN pip3 install --no-cache-dir --upgrade pip setuptools twine wheel

# build and install the application
COPY . /Kiwi/
WORKDIR /Kiwi

# install app dependencies so we can build the app later
RUN pip3 install --no-cache-dir -r requirements/mariadb.txt -r requirements/postgres.txt

RUN sed -i "s/tcms.settings.devel/tcms.settings.product/" manage.py

# compile tcms/static/js/bundle.js explicitly
RUN pushd tcms/ && npm install --include=dev && ./node_modules/.bin/webpack && popd

RUN ./tests/check-build
RUN pip3 install --no-cache-dir dist/kiwitcms-*.tar.gz
