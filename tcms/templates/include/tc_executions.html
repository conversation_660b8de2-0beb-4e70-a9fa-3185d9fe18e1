{% load i18n %}
{% load comments %}

<div class="card-pf card-pf-accented">
    <h2 class="card-pf-title">
        <span class="fa fa-forward"></span>
        {% trans 'Executions' %}
    </h2>

    <div class="card-pf-body">
        <div class="list-group tree-list-view-pf">
    {% for execution in executions %}
        {% ifchanged execution.run.plan.pk %}
            {% if not forloop.first %}
            </div> <!-- /plan -->
            {% endif %}

            <div class="list-group-item" id="execution-for-plan-{{ execution.run.plan.pk }}">
                <div class="list-group-item-header">
                    <div class="list-view-pf-main-info">
                        <div class="list-view-pf-left">
                            <span class="fa fa-angle-right"></span>
                        </div>

                        <div class="list-view-pf-body">
                            <div class="list-view-pf-description">
                                <div class="list-group-item-text">
                                    <a href="{% url 'test_plan_url' execution.run.plan.pk %}">TP-{{ execution.run.plan.pk }}: {{ execution.run.plan.name }}</a>
                                </div>
                            </div>
                        </div>
                    </div> <!-- /main info -->
                </div> <!-- /header -->
        {% endifchanged %}
                <!-- start caseruns -->
                <div class="list-group-item-container container-fluid">
                {% get_comment_list for execution as execution_comments %}
                {% with bugs=execution.get_bugs %}
                    <div class="list-group-item">
                        <div class="list-group-item-header">
                            <div class="list-view-pf-main-info">
                                <div class="list-view-pf-left">
                                    <span class="fa {% if execution_comments or show_bugs and bugs %}fa-angle-right{% else %}fa-exclamation{% endif %}"></span>
                                </div>

                                <div class="list-view-pf-body">
                                    <div class="list-view-pf-description">
                                        <div class="list-group-item-heading">
                                            TE-{{ execution.pk }}
                                        </div>
                                        <div class="list-group-item-text">
                                            <a href="{% url 'testruns-get' execution.run.pk %}">TR-{{ execution.run.pk }}: {{ execution.run.summary }}</a>
                                        </div>
                                    </div>

                                    <div class="list-view-pf-additional-info">
                                        <div class="list-view-pf-additional-info-item">
                                            <span class="{{ execution.status.icon }}"></span>
                                            <strong>{{ execution.status.name }}</strong>
                                        </div>
                                        {% if show_bugs and bugs %}
                                        <div class="list-view-pf-additional-info-item">
                                            <span class="fa fa-bug"></span>
                                            <strong>{{ bugs|length }}</strong>
                                        </div>
                                        {% endif %}
                                        {% if execution_comments %}
                                        <div class="list-view-pf-additional-info-item">
                                            <span class="fa fa-comments"></span>
                                            <strong>{{ execution_comments|length }}</strong>
                                        </div>
                                        {% endif %}
                                        <div class="list-view-pf-additional-info-item">
                                            <span class="pficon pficon-user"></span>
                                            <a href="#">{{ execution.tested_by }}</a>
                                        </div>
                                        <div class="list-view-pf-additional-info-item">
                                            <span class="fa fa-calendar-o"></span>
                                            {{ execution.stop_date }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> <!-- / caserun header -->
                {% if show_bugs %}
                    {% for bug in bugs %}
                        <div class="list-group-item-container container-fluid">
                            <div class="list-group-item">
                                <div class="list-group-item-header">
                                    <div class="list-view-pf-main-info">
                                        <div class="list-view-pf-left">
                                            <span class="fa fa-bug"></span>
                                        </div>
                                        <div class="list-view-pf-body">
                                            <div class="list-view-pf-description">
                                                <div class="list-group-item-text">
                                                    <a href="{{ bug.url }}" class="bug-url">{{ bug.url }}</a>
                                                </div>
                                            </div>

                                            <div class="list-view-pf-additional-info">
                                                <div class="list-view-pf-additional-info-item">
                                                    <a href="#"
                                                        data-toggle="popover" data-html="true"
                                                        data-content="undefined"
                                                        data-trigger="focus"
                                                        data-placement="top">
                                                        <span class="fa fa-info-circle"></span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> <!-- /bug -->
                    {% endfor %}
                {% endif %}

                    {% for comment in execution_comments %}
                        <!-- comments -->
                        <div class="list-group-item-container container-fluid">
                            <div class="list-group-item">
                                <div class="list-group-item-header">
                                    <div class="list-view-pf-main-info">
                                        <div class="list-view-pf-left">
                                            <span class="fa fa-comment"></span>
                                        </div>

                                        <div class="list-view-pf-body">
                                            <div class="list-view-pf-description">
                                                <div class="list-group-item-heading">
                                                    #{{ forloop.counter }}
                                                </div>
                                                <div class="list-group-item-text">
                                                    {{ comment.comment }}
                                                </div>
                                            </div>
                                            <div class="list-view-pf-additional-info">
                                                <div class="list-view-pf-additional-info-item">
                                                    <span class="fa fa-calendar-o"></span>
                                                    {{ comment.submit_date }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div> <!-- /header -->
                            </div>
                        </div> <!-- /comment -->
                    {% endfor %}
                    </div>
                {% endwith %}
                </div> <!-- /caseruns -->
            {% if forloop.last %}
            </div> <!-- /plan -->
            {% endif %}
    {% endfor %}
        </div>
    </div> <!-- /card -->
</div>
