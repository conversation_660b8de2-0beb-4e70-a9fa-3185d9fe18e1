.. _usecase:

Use Cases
=========

The following Use Cases represent some common scenarios for using Kiwi TCMS.

Manual Testing
--------------

#. QE Project / Team Lead assigns feature to be tested.
#. QA searches TCMS for Test Plan. See the section :ref:`searching-testplans`.
#. QA creates new Test Run to record execution results. See the section
   :ref:`creating-testrun`.
#. QA executes Test Run. See the section :ref:`executing-testrun`.

|Manual Testing Use Case.|

Writing a Test Plan
-------------------

#. QE Project / Team Lead assigns feature to be tested.
#. QA writes a new Test Plan. See the section :ref:`creating-testplan`.
#. QA adds Test Cases:

   #. Create new Test Case. See the section :ref:`create-test-case`.
   #. Add existing Test Case. See the section :ref:`use-existing-test-case`.

#. QA executes Test Run. See the section :ref:`executing-testrun`.

|Writing a Test Plan Use Case.|

Manager Assigns Testing Priorities
----------------------------------

#. Manager searches for a Test Plan. See the section
   :ref:`searching-testplans`.
#. Manager assigns priorities to Test Cases.
#. QA searches for Test Plan.
#. QA creates new Test Run. See the section :ref:`creating-testrun`.
#. QA executes Test Run, based on Test Case priorities. See the
   section :ref:`executing-testrun`.

|PM Reporting Use Case.|

Cloning a Test Plan
-------------------

#. QE Project / Team Lead assigns feature to be tested in new version of
   product.
#. QA searches TCMS for Test Plan. See the section :ref:`searching-testplans`.
#. QA clones Test Plan. See the section :ref:`cloning-testplan`.
#. QA creates new Test Run. See the section :ref:`creating-testrun`.
#. QA executes Test Run. See the section :ref:`executing-testrun`.

|Cloning a Test Plan Use Case.|

.. |Manual Testing Use Case.| image:: ../_static/Manual_Testing.png
.. |Writing a Test Plan Use Case.| image:: ../_static/Create_New_TP.png
.. |PM Reporting Use Case.| image:: ../_static/PM_Reporting.png
.. |Cloning a Test Plan Use Case.| image:: ../_static/TP_Cloning.png
