# See https://docs.readthedocs.io/en/stable/config-file/v2.html for details

# Required
version: 2

sphinx:
   builder: html
   configuration: docs/source/conf.py

build:
  apt_packages:
    - python3-pydot
    - graphviz
  os: "ubuntu-22.04"
  tools:
    python: "3.11"
  jobs:
    pre_build:
     - GIT_DIR=$(mktemp -d) && git clone --depth 1 https://github.com/kiwitcms/trackers-integration $GIT_DIR && ln -s $GIT_DIR/trackers_integration trackers_integration
     - make -C docs/ apidoc

# Optionally set the version of Python and requirements required to build your docs
python:
   install:
   - requirements: requirements/readthedocs.txt
