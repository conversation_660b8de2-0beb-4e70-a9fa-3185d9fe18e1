msgid ""
msgstr ""
"Project-Id-Version: kiwitcms\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-30 12:22+0000\n"
"PO-Revision-Date: 2025-07-30 13:13\n"
"Last-Translator: \n"
"Language-Team: Korean\n"
"Language: ko_KR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: kiwitcms\n"
"X-Crowdin-Project-ID: 295734\n"
"X-Crowdin-Language: ko\n"
"X-Crowdin-File: /master/tcms/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 19\n"

#: tcms/bugs/forms.py:33
msgid "Description of problem:\n\n\n"
"How often reproducible:\n\n\n"
"Steps to Reproduce:\n"
"1.\n"
"2.\n"
"3.\n\n"
"Actual results:\n\n\n"
"Expected results:\n\n\n"
"Additional info:"
msgstr "문제에 대한 설명:\n\n\n"
"재현 가능한 빈도:\n\n\n"
"재현 단계:\n"
"1.\n"
"2.\n"
"3.\n\n"
"실제 결과:\n\n\n"
"예상 결과:\n\n\n"
"추가 정보:"

#: tcms/bugs/models.py:24 tcms/bugs/templates/bugs/get.html:50
#: tcms/bugs/templates/bugs/mutable.html:27
#: tcms/bugs/templates/bugs/search.html:18
#: tcms/bugs/templates/bugs/search.html:108
msgid "Severity"
msgstr "심각도"

#: tcms/bugs/templates/bugs/get.html:37 tcms/bugs/templates/bugs/search.html:89
#: tcms/issuetracker/kiwitcms.py:49 tcms/templates/include/bug_details.html:3
msgid "Open"
msgstr "열기"

#: tcms/bugs/templates/bugs/get.html:39 tcms/issuetracker/kiwitcms.py:49
#: tcms/templates/include/bug_details.html:3
msgid "Closed"
msgstr "닫힘"

#: tcms/bugs/templates/bugs/get.html:59 tcms/bugs/templates/bugs/search.html:79
#: tcms/bugs/templates/bugs/search.html:114
#: tcms/templates/include/bug_details.html:7
msgid "Reporter"
msgstr "보고자"

#: tcms/bugs/templates/bugs/get.html:64
#: tcms/bugs/templates/bugs/mutable.html:94
#: tcms/bugs/templates/bugs/search.html:84
#: tcms/bugs/templates/bugs/search.html:115
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:36
#: tcms/templates/include/bug_details.html:10
#: tcms/testruns/templates/testruns/get.html:207
#: tcms/testruns/templates/testruns/get.html:267
msgid "Assignee"
msgstr "담당자"

#: tcms/bugs/templates/bugs/get.html:75
#: tcms/bugs/templates/bugs/mutable.html:45
#: tcms/bugs/templates/bugs/search.html:47
#: tcms/bugs/templates/bugs/search.html:111
#: tcms/core/templates/dashboard.html:53 tcms/management/admin.py:107
#: tcms/telemetry/templates/telemetry/include/filters.html:8
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:29
#: tcms/templates/include/bug_details.html:13
#: tcms/testcases/templates/testcases/get.html:44
#: tcms/testcases/templates/testcases/get.html:170
#: tcms/testcases/templates/testcases/mutable.html:43
#: tcms/testcases/templates/testcases/search.html:51
#: tcms/testplans/templates/testplans/clone.html:21
#: tcms/testplans/templates/testplans/get.html:64
#: tcms/testplans/templates/testplans/mutable.html:33
#: tcms/testplans/templates/testplans/search.html:50
#: tcms/testplans/templates/testplans/search.html:117
#: tcms/testruns/templates/testruns/get.html:45
#: tcms/testruns/templates/testruns/mutable.html:46
#: tcms/testruns/templates/testruns/search.html:35
#: tcms/testruns/templates/testruns/search.html:183
msgid "Product"
msgstr "제품"

#: tcms/bugs/templates/bugs/get.html:80
#: tcms/bugs/templates/bugs/mutable.html:60
#: tcms/bugs/templates/bugs/search.html:57
#: tcms/bugs/templates/bugs/search.html:112
#: tcms/telemetry/templates/telemetry/include/filters.html:17
#: tcms/templates/include/bug_details.html:15 tcms/templates/navbar.html:71
#: tcms/testplans/templates/testplans/clone.html:36
#: tcms/testplans/templates/testplans/get.html:70
#: tcms/testplans/templates/testplans/mutable.html:48
#: tcms/testplans/templates/testplans/search.html:60
#: tcms/testplans/templates/testplans/search.html:118
#: tcms/testruns/templates/testruns/get.html:50
#: tcms/testruns/templates/testruns/search.html:45
#: tcms/testruns/templates/testruns/search.html:184
msgid "Version"
msgstr "버전"

#: tcms/bugs/templates/bugs/get.html:85
#: tcms/bugs/templates/bugs/mutable.html:78
#: tcms/bugs/templates/bugs/search.html:67
#: tcms/bugs/templates/bugs/search.html:113 tcms/management/models.py:132
#: tcms/telemetry/templates/telemetry/include/filters.html:25
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:30
#: tcms/templates/include/bug_details.html:17
#: tcms/testruns/templates/testruns/get.html:55
#: tcms/testruns/templates/testruns/get.html:414
#: tcms/testruns/templates/testruns/mutable.html:89
#: tcms/testruns/templates/testruns/search.html:55
#: tcms/testruns/templates/testruns/search.html:185
msgid "Build"
msgstr "빌드"

#: tcms/bugs/templates/bugs/get.html:119
msgid "commented on"
msgstr "에 대한 댓글"

#: tcms/bugs/templates/bugs/get.html:149
msgid "Reopen"
msgstr "다시 열기"

#: tcms/bugs/templates/bugs/get.html:151
#: tcms/bugs/templates/bugs/mutable.html:114
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:78
#: tcms/testcases/templates/testcases/mutable.html:263
#: tcms/testplans/templates/testplans/get.html:477
#: tcms/testplans/templates/testplans/mutable.html:173
#: tcms/testruns/templates/testruns/get.html:480
#: tcms/testruns/templates/testruns/get.html:593
#: tcms/testruns/templates/testruns/mutable.html:210
msgid "Save"
msgstr "저장"

#: tcms/bugs/templates/bugs/get.html:153
msgid "Close"
msgstr "닫기"

#: tcms/bugs/templates/bugs/mutable.html:20
#: tcms/bugs/templates/bugs/search.html:13
#: tcms/bugs/templates/bugs/search.html:109
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:27
#: tcms/testcases/templates/testcases/mutable.html:24
#: tcms/testcases/templates/testcases/search.html:13
#: tcms/testcases/templates/testcases/search.html:162
#: tcms/testplans/templates/testplans/get.html:212
#: tcms/testplans/templates/testplans/get.html:487
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:3
#: tcms/testruns/templates/testruns/get.html:251
#: tcms/testruns/templates/testruns/get.html:258
#: tcms/testruns/templates/testruns/get.html:261
#: tcms/testruns/templates/testruns/mutable.html:23
#: tcms/testruns/templates/testruns/mutable.html:229
#: tcms/testruns/templates/testruns/search.html:14
#: tcms/testruns/templates/testruns/search.html:181
msgid "Summary"
msgstr "요약"

#: tcms/bugs/templates/bugs/mutable.html:28
msgid "add new Severity"
msgstr "새로운 심각도 추가"

#: tcms/bugs/templates/bugs/mutable.html:46
#: tcms/testcases/templates/testcases/mutable.html:44
#: tcms/testplans/templates/testplans/clone.html:22
#: tcms/testplans/templates/testplans/mutable.html:34
#: tcms/testruns/templates/testruns/mutable.html:48
msgid "add new Product"
msgstr "새 제품 추가"

#: tcms/bugs/templates/bugs/mutable.html:62
#: tcms/testplans/templates/testplans/clone.html:38
#: tcms/testplans/templates/testplans/mutable.html:50
msgid "add new Version"
msgstr "새 버전 추가"

#: tcms/bugs/templates/bugs/mutable.html:81
#: tcms/bugs/templates/bugs/mutable.html:82
#: tcms/testruns/templates/testruns/mutable.html:93
#: tcms/testruns/templates/testruns/mutable.html:94
msgid "add new Build"
msgstr "새로운 빌드 추가"

#: tcms/bugs/templates/bugs/mutable.html:98
#: tcms/testruns/templates/testruns/mutable.html:32
#: tcms/testruns/templates/testruns/mutable.html:39
msgid "Username or email"
msgstr "사용자 이름 또는 전자 메일"

#: tcms/bugs/templates/bugs/search.html:5 tcms/settings/common.py:414
msgid "Search Bugs"
msgstr "버그 검색"

#: tcms/bugs/templates/bugs/search.html:28
#: tcms/testcases/templates/testcases/search.html:19
#: tcms/testplans/templates/testplans/search.html:24
msgid "Created"
msgstr "작성일"

#: tcms/bugs/templates/bugs/search.html:31
#: tcms/telemetry/templates/telemetry/include/filters.html:53
#: tcms/testcases/templates/testcases/search.html:22
#: tcms/testplans/templates/testplans/search.html:27
#: tcms/testruns/templates/testruns/search.html:90
#: tcms/testruns/templates/testruns/search.html:110
#: tcms/testruns/templates/testruns/search.html:131
#: tcms/testruns/templates/testruns/search.html:151
msgid "Before"
msgstr "이전"

#: tcms/bugs/templates/bugs/search.html:37
#: tcms/telemetry/templates/telemetry/include/filters.html:43
#: tcms/testcases/templates/testcases/search.html:28
#: tcms/testplans/templates/testplans/search.html:33
#: tcms/testruns/templates/testruns/search.html:97
#: tcms/testruns/templates/testruns/search.html:117
#: tcms/testruns/templates/testruns/search.html:138
#: tcms/testruns/templates/testruns/search.html:158
msgid "After"
msgstr "이후"

#: tcms/bugs/templates/bugs/search.html:81
#: tcms/bugs/templates/bugs/search.html:86
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:45
#: tcms/templates/registration/login.html:17
#: tcms/templates/registration/registration_form.html:15
#: tcms/testcases/templates/testcases/search.html:124
#: tcms/testplans/templates/testplans/search.html:84
#: tcms/testplans/templates/testplans/search.html:90
#: tcms/testruns/templates/testruns/get.html:524
#: tcms/testruns/templates/testruns/search.html:69
#: tcms/testruns/templates/testruns/search.html:75
msgid "Username"
msgstr "사용자 이름"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:39
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "Yes"
msgstr "네"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:42
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "No"
msgstr "아니오"

#: tcms/bugs/templates/bugs/search.html:97
#: tcms/testcases/templates/testcases/search.html:150
#: tcms/testplans/templates/testplans/get.html:221
#: tcms/testplans/templates/testplans/search.html:103
#: tcms/testruns/templates/testruns/get.html:273
#: tcms/testruns/templates/testruns/search.html:170
msgid "Search"
msgstr "검색"

#: tcms/bugs/templates/bugs/search.html:107
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:26
#: tcms/testcases/templates/testcases/get.html:166
#: tcms/testcases/templates/testcases/search.html:144
#: tcms/testcases/templates/testcases/search.html:161
#: tcms/testplans/templates/testplans/search.html:114
#: tcms/testruns/templates/testruns/search.html:180
msgid "ID"
msgstr "식별자"

#: tcms/bugs/templates/bugs/search.html:110
#: tcms/templates/include/bug_details.html:5
#: tcms/testcases/templates/testcases/search.html:163
#: tcms/testplans/templates/testplans/search.html:116
#: tcms/testruns/templates/testruns/mutable.html:231
msgid "Created on"
msgstr "작성일"

#: tcms/bugs/views.py:42 tcms/testcases/views.py:130
#: tcms/testplans/templates/testplans/get.html:380 tcms/testplans/views.py:143
#: tcms/testruns/views.py:194 tcms/testruns/views.py:308
msgid "Edit"
msgstr "편집"

#: tcms/bugs/views.py:45 tcms/testcases/views.py:143
#: tcms/testplans/views.py:151 tcms/testruns/views.py:207
#: tcms/testruns/views.py:316
msgid "Object permissions"
msgstr "객체 권한"

#: tcms/bugs/views.py:50
#: tcms/templates/include/comments_for_object_template.html:10
#: tcms/templates/include/properties_card.html:32
#: tcms/templates/include/properties_card.html:47 tcms/testcases/views.py:151
#: tcms/testplans/templates/testplans/get.html:193
#: tcms/testplans/templates/testplans/get.html:384 tcms/testplans/views.py:159
#: tcms/testruns/templates/testruns/get.html:238
#: tcms/testruns/templates/testruns/get.html:450 tcms/testruns/views.py:215
#: tcms/testruns/views.py:324
msgid "Delete"
msgstr "삭제"

#: tcms/bugs/views.py:68 tcms/settings/common.py:400
msgid "New Bug"
msgstr "새 버그"

#: tcms/bugs/views.py:188
msgid "Edit bug"
msgstr "버그 편집"

#: tcms/bugs/views.py:231
msgid "*bug closed*"
msgstr "*버그 마감*"

#: tcms/bugs/views.py:235
msgid "*bug reopened*"
msgstr "*버그 재개*"

#: tcms/core/history.py:52
#, python-format
msgid "UPDATE: %(model_name)s #%(pk)d - %(title)s"
msgstr "갱신: %(model_name)s#%(pk)d - %(title)s"

#: tcms/core/history.py:62
#, python-format
msgid "Updated on %(history_date)s\n"
"Updated by %(username)s\n\n"
"%(diff)s\n\n"
"For more information:\n"
"%(instance_url)s"
msgstr "%(history_date)s 에 %(username)s 님이 갱신\n\n"
"%(diff)s\n\n"
"자세한 내용은 다음을 참조하세요:\n"
"%(instance_url)s"

#: tcms/core/templates/dashboard.html:3 tcms/settings/common.py:430
msgid "Dashboard"
msgstr "대시보드"

#: tcms/core/templates/dashboard.html:8
#: tcms/testruns/templates/testruns/get.html:153
msgid "Test executions"
msgstr "테스트 실행"

#: tcms/core/templates/dashboard.html:15
#, python-format
msgid "%(amount)s%% complete"
msgstr "%(amount)s%% 완료"

#: tcms/core/templates/dashboard.html:24
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:38
#: tcms/testruns/templates/testruns/get.html:78
msgid "Started at"
msgstr "시작 시점"

#: tcms/core/templates/dashboard.html:32
#, python-format
msgid "%(total_count)s TestRun(s) or TestCase(s) assigned to you need to be executed.\n"
"Here are the latest %(count)s."
msgstr "할당된 %(total_count)s 개의 테스트 수행 혹은 테스트 케이스를 실행해야 합니다.\n"
"최신 %(count)s 개는 다음과 같습니다."

#: tcms/core/templates/dashboard.html:36 tcms/core/templates/dashboard.html:80
msgid "SEE ALL"
msgstr "모두 보기"

#: tcms/core/templates/dashboard.html:39
msgid "There are no TestRun(s) assigned to you"
msgstr "담당하고 있는 테스트 수행이 없습니다"

#: tcms/core/templates/dashboard.html:46
msgid "Your Test plans"
msgstr "테스트 계획"

#: tcms/core/templates/dashboard.html:52
msgid "TestPlan"
msgstr "테스트 계획"

#: tcms/core/templates/dashboard.html:54
#: tcms/testcases/templates/testcases/get.html:169
#: tcms/testplans/templates/testplans/mutable.html:66
#: tcms/testplans/templates/testplans/search.html:70
#: tcms/testplans/templates/testplans/search.html:119
msgid "Type"
msgstr "유형"

#: tcms/core/templates/dashboard.html:55
#: tcms/templates/include/tc_executions.html:7
msgid "Executions"
msgstr "실행"

#: tcms/core/templates/dashboard.html:76
#, python-format
msgid "You manage %(total_count)s TestPlan(s), %(disabled_count)s are disabled.\n"
"Here are the latest %(count)s."
msgstr "%(total_count)s 개의 테스트 계획을 관리하고 %(disabled_count)s 개가 비활성화되었습니다.\n"
"최신 %(count)s 개는 다음과 같습니다."

#: tcms/core/templates/dashboard.html:83
msgid "There are no TestPlan(s) that belong to you"
msgstr "당신에게 속한 테스트 계획이 없습니다"

#: tcms/core/views.py:47
#, python-format
msgid "Base URL is not configured! See <a href=\"%(doc_url)s\">documentation</a> and <a href=\"%(admin_url)s\">change it</a>"
msgstr "기본 URL이 구성되지 않았습니다! 참조<a href=\"%(doc_url)s\">설명서</a> 및 <a href=\"%(admin_url)s\">변경</a>"

#: tcms/core/views.py:71
#, python-format
msgid "You have %(unapplied_migration_count)s unapplied migration(s). See <a href=\"%(doc_url)s\">documentation</a>"
msgstr "%(unapplied_migration_count)s 개의 적용되지 않은 마이그레이션이 있습니다. <a href=\"%(doc_url)s\">문서</a>를 참고하세요"

#: tcms/core/views.py:92
#, python-format
msgid "You are not using a secure connection. See <a href=\"%(doc_url)s\">documentation</a> and enable SSL."
msgstr "보안 연결을 사용하지 않고 있습니다. <a href=\"%(doc_url)s\">안내서</a>를 참고해 SSL을 활성화 하세요."

#: tcms/kiwi_attachments/validators.py:10
#, python-brace-format
msgid "File contains forbidden tag: <{tag_name}>"
msgstr "파일이 금지된 태그를 포함합니다: <{tag_name}>"

#: tcms/kiwi_attachments/validators.py:92
#, python-brace-format
msgid "File contains forbidden attribute: `{attr_name}`"
msgstr "파일이 금지된 속성을 포함합니다: `{attr_name}`"

#: tcms/kiwi_attachments/validators.py:97
msgid "Uploading executable files is forbidden"
msgstr "실행 파일은 올릴 수 없습니다"

#: tcms/kiwi_auth/admin.py:36 tcms/settings/common.py:443
msgid "Users"
msgstr "사용자"

#: tcms/kiwi_auth/admin.py:82
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:5
#: tcms/templates/navbar.html:102
msgid "Reset email address"
msgstr "전자우편 주소 재설정"

#: tcms/kiwi_auth/admin.py:148
msgid "Personal info"
msgstr "개인 정보"

#: tcms/kiwi_auth/admin.py:150
msgid "Permissions"
msgstr "권한"

#: tcms/kiwi_auth/admin.py:187
msgid "This is the last superuser, it cannot be deleted!"
msgstr "마지막 슈퍼유저는 삭제할 수 없습니다!"

#: tcms/kiwi_auth/forms.py:28
msgid "A user with that email already exists."
msgstr "해당 전자우편 주소를 가진 사용자가 이미 존재합니다."

#: tcms/kiwi_auth/forms.py:48
msgid "Please confirm your Kiwi TCMS account email address"
msgstr "Kiwi TCMS 계정의 전자우편 주소를 확인해 주세요!"

#: tcms/kiwi_auth/forms.py:142
msgid "Email mismatch"
msgstr "전자우편 불일치"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:14
msgid "Warning"
msgstr "경고"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:19
msgid "After clicking the 'Save' button your account will become <strong>inactive</strong>\n"
"and you will be <strong>logged out</strong>! A confirmation email will be sent to the newly specified address!<br>\n"
"Double check that your new email address is <strong>entered correctly</strong> otherwise\n"
"<strong>you may be left locked out</strong> of your account!\n"
"After following the activation link you will be able to log in as usual!"
msgstr "'저장' 버튼을 클릭하면 계정이 <strong>비활성화</strong> 상태가 되고 <strong>로그아웃</strong> 상태가 됩니다! 또한 새로 지정한 전자우편 주소로 확인 전자우편이 전송됩니다!<br>\n"
"새 전자우편 주소가 <strong>올바르게 입력되었는지</strong> 다시 한 번 확인하세요. 잘못될 경우 <strong>계정이 잠길 수도 있습니다!</strong>\n"
"확인 전자우편에 있는 활성화 링크를 따라가면 평소와 같이 로그인할 수 있습니다!"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:51
msgid "NOT yourself"
msgstr ""

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:58
#: tcms/templates/registration/password_reset_form.html:16
#: tcms/templates/registration/registration_form.html:39
msgid "E-mail"
msgstr "전자우편"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:66
#: tcms/templates/registration/password_reset_confirm.html:22
#: tcms/templates/registration/registration_form.html:31
msgid "Confirm"
msgstr "확인"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:71
msgid "Please type! Do not copy-and-paste value from previous field!"
msgstr "직접 입력하세요! 이전 필드에서 복사한 값을 붙여넣지 마세요!"

#: tcms/kiwi_auth/views.py:70
msgid "Your account has been created, please check your mailbox for confirmation"
msgstr "계정이 생성되었습니다. 확인 링크를 메일로 가서 확인하세요"

#: tcms/kiwi_auth/views.py:75
msgid "Your account has been created, but you need an administrator to activate it"
msgstr "계정이 생성되었지만 활성화하려면 관리자가 필요합니다"

#: tcms/kiwi_auth/views.py:80
msgid "Following is the administrator list"
msgstr "다음은 관리자 목록입니다"

#: tcms/kiwi_auth/views.py:123
msgid "This activation key no longer exists in the database"
msgstr "이 활성화 키는 더 이상 데이터베이스에 없습니다"

#: tcms/kiwi_auth/views.py:129
msgid "This activation key has expired"
msgstr "이 활성화 키가 만료되었습니다"

#: tcms/kiwi_auth/views.py:141
msgid "Your account has been activated successfully"
msgstr "계정이 활성화 되었습니다."

#: tcms/kiwi_auth/views.py:168 tcms/kiwi_auth/views.py:177
#: tcms/kiwi_auth/views.py:196 tcms/kiwi_auth/views.py:205
#, python-format
msgid "You are viewing records from tenant '%s'"
msgstr "'%s' 테넌트의 레코드르 보는 중입니다"

#: tcms/kiwi_auth/views.py:256
msgid "Email address has been reset, please check inbox for further instructions"
msgstr "전자우편 주소가 재설정되었습니다. 다음으로 넘어가려면 수신함을 확인해 주세요"

#: tcms/management/models.py:58
#: tcms/telemetry/templates/telemetry/testing/breakdown.html:34
msgid "Priorities"
msgstr "우선순위"

#: tcms/management/models.py:133
msgid "Builds"
msgstr "빌드"

#: tcms/management/models.py:144
#: tcms/testcases/templates/testcases/search.html:136
#: tcms/testplans/templates/testplans/get.html:217
#: tcms/testplans/templates/testplans/search.html:94
#: tcms/testruns/templates/testruns/get.html:263
#: tcms/testruns/templates/testruns/search.html:27
msgid "Tag"
msgstr "태그"

#: tcms/management/models.py:145 tcms/templates/include/tags_card.html:6
#: tcms/testcases/templates/testcases/search.html:171
#: tcms/testplans/templates/testplans/get.html:456
#: tcms/testplans/templates/testplans/search.html:121
#: tcms/testruns/templates/testruns/get.html:353
#: tcms/testruns/templates/testruns/search.html:190
msgid "Tags"
msgstr "태그"

#: tcms/rpc/api/bug.py:69
msgid "Enable reporting to this Issue Tracker by configuring its base_url!"
msgstr "base_url 구성하여 이 문제 추적기에 보고할 수 있습니다!"

#: tcms/rpc/api/forms/__init__.py:9
msgid "Invalid date format. Expected YYYY-MM-DD [HH:MM:SS]."
msgstr "날짜 형식은 YYYY-MM-DD [HH:MM:SS] 여야 합니다."

#: tcms/settings/common.py:391
msgid "TESTING"
msgstr "테스트"

#: tcms/settings/common.py:393 tcms/testruns/templates/testruns/mutable.html:70
msgid "New Test Plan"
msgstr "새 테스트 계획"

#: tcms/settings/common.py:395
#: tcms/testcases/templates/testcases/mutable.html:12
#: tcms/testplans/templates/testplans/get.html:131
msgid "New Test Case"
msgstr "새 테스트 케이스"

#: tcms/settings/common.py:397 tcms/testplans/templates/testplans/get.html:121
#: tcms/testruns/templates/testruns/get.html:172
#: tcms/testruns/templates/testruns/mutable.html:11
msgid "New Test Run"
msgstr "새 테스트 수행"

#: tcms/settings/common.py:407
msgid "SEARCH"
msgstr "검색"

#: tcms/settings/common.py:409 tcms/testplans/templates/testplans/search.html:5
msgid "Search Test Plans"
msgstr "테스트 계획 검색"

#: tcms/settings/common.py:410 tcms/testcases/templates/testcases/search.html:5
msgid "Search Test Cases"
msgstr "테스트 케이스 검색"

#: tcms/settings/common.py:411 tcms/testruns/templates/testruns/search.html:5
msgid "Search Test Runs"
msgstr "테스트 수행 검색"

#: tcms/settings/common.py:412
msgid "Search Test Executions"
msgstr "테스트 실행 검색"

#: tcms/settings/common.py:421
msgid "TELEMETRY"
msgstr "텔레메트리"

#: tcms/settings/common.py:424
msgid "Testing"
msgstr "테스트"

#: tcms/settings/common.py:426
msgid "Breakdown"
msgstr "중단"

#: tcms/settings/common.py:428
msgid "Execution"
msgstr "실행"

#: tcms/settings/common.py:431
#: tcms/testruns/templates/testruns/mutable.html:177
msgid "Matrix"
msgstr "매트릭스"

#: tcms/settings/common.py:432
msgid "Trends"
msgstr "동향"

#: tcms/settings/common.py:435
#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:5
msgid "TestCase health"
msgstr "테스트 케이스 상태"

#: tcms/settings/common.py:441
msgid "ADMIN"
msgstr "관리자"

#: tcms/settings/common.py:444
msgid "Groups"
msgstr "그룹"

#: tcms/settings/common.py:446
msgid "Everything else"
msgstr "기타"

#: tcms/settings/common.py:449
msgid "MORE"
msgstr ""

#: tcms/settings/common.py:459
msgid "Report an Issue"
msgstr "문제 보고하기"

#: tcms/settings/common.py:462
msgid "Ask for help on StackOverflow"
msgstr "스택오버플로우에 도움 요청하기"

#: tcms/settings/common.py:466
msgid "Donate €5 via Open Collective"
msgstr "Open Collective를 통해 € 5 기부"

#: tcms/settings/common.py:468
msgid "Administration Guide"
msgstr "관리 안내서"

#: tcms/settings/common.py:469
msgid "User Guide"
msgstr "사용자 안내서"

#: tcms/settings/common.py:470
msgid "API Help"
msgstr "API 도움말"

#: tcms/signals.py:85
msgid "New user awaiting approval"
msgstr "승인을 기다리는 새로운 사용자"

#: tcms/signals.py:163
#, python-format
msgid "NEW: TestRun #%(pk)d - %(summary)s"
msgstr "신규: 테스트 수행 #%(pk)d - %(summary)s"

#: tcms/signals.py:235
#, python-format
msgid "Bug #%(pk)d - %(summary)s"
msgstr "버그 #%(pk)d - %(summary)s"

#: tcms/telemetry/api.py:60 testcases.TestCaseStatus/name:2
msgid "CONFIRMED"
msgstr "확인"

#: tcms/telemetry/api.py:61
msgid "OTHER"
msgstr "다른"

#: tcms/telemetry/api.py:133 tcms/telemetry/api.py:185
#: tcms/telemetry/api.py:191
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:9
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:13
#: tcms/testruns/templates/testruns/get.html:129
msgid "TOTAL"
msgstr "합계"

#: tcms/telemetry/templates/telemetry/include/filters.html:36
#: tcms/testcases/templates/testcases/search.html:68
#: tcms/testplans/templates/testplans/search.html:115
#: tcms/testruns/templates/testruns/get.html:40
#: tcms/testruns/templates/testruns/mutable.html:67
#: tcms/testruns/templates/testruns/search.html:182
msgid "Test plan"
msgstr "테스트 계획"

#: tcms/telemetry/templates/telemetry/include/filters.html:66
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
#: tcms/testcases/templates/testcases/search.html:142
msgid "Test run"
msgstr "테스트 수행"

#: tcms/telemetry/templates/telemetry/include/filters.html:70
#: tcms/testruns/templates/testruns/search.html:17
msgid "Test run summary"
msgstr "테스트 수행 요약"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:5
msgid "Testing Breakdown"
msgstr "테스트 분석"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:14
msgid "Total"
msgstr "합계"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:24
#: tcms/testcases/templates/testcases/get.html:93
#: tcms/testcases/templates/testcases/mutable.html:98
#: tcms/testcases/templates/testcases/search.html:36
#: tcms/testcases/templates/testcases/search.html:168
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testplans/templates/testplans/get.html:489
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:5
#: tcms/testruns/templates/testruns/get.html:264
#: tcms/testruns/templates/testruns/get.html:361
msgid "Automated"
msgstr "자동화"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:28
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testruns/templates/testruns/get.html:361
msgid "Manual"
msgstr "수동"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:39
#: tcms/testcases/models.py:42
msgid "Categories"
msgstr "분류"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:5
msgid "Execution Dashboard"
msgstr "실행 대시보드"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:15
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:15
msgid "Child TPs"
msgstr "하위 테스트 계획"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:28
#: tcms/templates/include/bug_details.html:3
#: tcms/testcases/templates/testcases/get.html:59
#: tcms/testcases/templates/testcases/mutable.html:74
#: tcms/testcases/templates/testcases/search.html:91
#: tcms/testcases/templates/testcases/search.html:167
#: tcms/testplans/templates/testplans/get.html:149
#: tcms/testplans/templates/testplans/get.html:363
#: tcms/testplans/templates/testplans/get.html:396
#: tcms/testplans/templates/testplans/get.html:488
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:4
#: tcms/testruns/templates/testruns/get.html:189
#: tcms/testruns/templates/testruns/get.html:269
#: tcms/testruns/templates/testruns/get.html:385
#: tcms/testruns/templates/testruns/mutable.html:232
msgid "Status"
msgstr "상태"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:31
#: tcms/testcases/templates/testcases/get.html:208
#: tcms/testplans/templates/testplans/get.html:447
#: tcms/testruns/templates/testruns/get.html:349
msgid "Components"
msgstr "컴포넌트"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
#: tcms/testcases/templates/testcases/get.html:33
#: tcms/testcases/templates/testcases/mutable.html:36
#: tcms/testcases/templates/testcases/mutable.html:207
#: tcms/testcases/templates/testcases/search.html:170
#: tcms/testplans/templates/testplans/get.html:175
#: tcms/testplans/templates/testplans/get.html:378
#: tcms/testplans/templates/testplans/get.html:416
#: tcms/testplans/templates/testplans/get.html:493
#: tcms/testplans/templates/testplans/mutable.html:133
#: tcms/testplans/templates/testplans/search.html:88
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:9
#: tcms/testruns/templates/testruns/get.html:66
#: tcms/testruns/templates/testruns/mutable.html:36
#: tcms/testruns/templates/testruns/search.html:73
#: tcms/testruns/templates/testruns/search.html:189
msgid "Default tester"
msgstr "기본 테스터"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
msgid "TC"
msgstr "테스트 케이스"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
msgid "TR"
msgstr "테스트 수행"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:37
#: tcms/testruns/templates/testruns/get.html:268
#: tcms/testruns/templates/testruns/get.html:375
msgid "Tested by"
msgstr "테스터"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:39
#: tcms/testruns/templates/testruns/get.html:96
#: tcms/testruns/templates/testruns/get.html:405
#: tcms/testruns/templates/testruns/mutable.html:140
msgid "Finished at"
msgstr "완료 시점"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:5
msgid "Execution Trends"
msgstr "실행 동향"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:21
msgid "Positive"
msgstr "양"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:24
msgid "Neutral"
msgstr "중립"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:27
msgid "Negative"
msgstr "음수"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:5
msgid "Execution Matrix"
msgstr "실행 매트릭스"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:21
msgid "Order"
msgstr "정렬"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Ascending"
msgstr "오름차순"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Descending"
msgstr "내림차순"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
msgid "Test case"
msgstr "테스트 케이스"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:12
msgid "Most frequently failing test cases"
msgstr "가장 자주 실패하는 테스트 케이스"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:16
msgid "Test Case"
msgstr "테스트 케이스"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:18
msgid "Failed executions"
msgstr "실패한 실행"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:19
#, python-format
msgid "%% of failed executions"
msgstr "실패율(%%)"

#: tcms/templates/404.html:5 tcms/templates/404.html:16
msgid "Page not found"
msgstr "존재하지 않는 카드입니다."

#: tcms/templates/500.html:5 tcms/templates/500.html:16
msgid "Internal Server Error"
msgstr "내부 서버 오류"

#: tcms/templates/attachments/add.html:3
msgid "Attachment upload error"
msgstr "첨부 올려주기 오류"

#: tcms/templates/attachments/delete_link.html:3
msgid "Are you sure you want to delete this attachment?"
msgstr "정말 이 첨부 파일을 삭제 하시겠습니까?"

#: tcms/templates/base.html:8
msgid "day"
msgstr "일"

#: tcms/templates/base.html:8
msgid "days"
msgstr "일"

#: tcms/templates/base.html:9
msgid "hour"
msgstr "시간"

#: tcms/templates/base.html:9
msgid "hours"
msgstr "시간"

#: tcms/templates/base.html:10
msgid "minute"
msgstr "분"

#: tcms/templates/base.html:10
msgid "minutes"
msgstr "분"

#: tcms/templates/base.html:11
msgid "second"
msgstr "초"

#: tcms/templates/base.html:11
msgid "seconds"
msgstr "초"

#: tcms/templates/base.html:16 tcms/templates/registration/login.html:46
msgid "the leading open source test case management system"
msgstr "선도적인 오픈 소스 테스트 케이스 관리 시스템"

#: tcms/templates/email/confirm_registration.txt:1
#, python-format
msgid "Welcome to Kiwi TCMS!\n\n"
"To confirm email address for username `%(user)s` and activate your account\n"
"please follow this URL:\n"
"%(confirm_url)s\n\n"
"Regards,\n"
"Kiwi TCMS"
msgstr "Kiwi TCMS에 오신 것을 환영합니다!\n\n"
"`%(user)s` 사용자의 전자 우편 주소를 확인해 계정을 활성화하려면 다음 URL로 접근해 주세요:\n"
"%(confirm_url)s\n\n"
"Kiwi TCMS"

#: tcms/templates/email/post_bug_save/email.txt:2
#, python-format
msgid "Bug %(pk)s has been updated.\n\n"
"Link: %(bug_url)s\n\n"
"Summary: %(summary)s\n"
"Created at: %(creation_date)s\n"
"Reporter: %(reporter)s\n"
"Assignee: %(assignee)s\n"
"Product: %(product)s\n"
"Version: %(version)s\n"
"Build: %(build)s\n"
"Last comment:\n"
"%(last_comment)s"
msgstr "%(pk)s 버그가 수정되었습니다.\n\n"
"링크: %(bug_url)s\n\n"
"요약: %(summary)s\n"
"생성일: %(creation_date)s\n"
"보고자: %(reporter)s\n"
"담당자: %(assignee)s\n"
"제품: %(product)s\n"
"버전: %(version)s\n"
"빌드: %(build)s\n"
"최근 댓글:\n"
"%(last_comment)s"

#: tcms/templates/email/post_case_delete/email.txt:2
#, python-format
msgid "TestCase has been deleted by %(username)s!"
msgstr "%(username)s님이 테스트 케이스를 삭제했습니다"

#: tcms/templates/email/post_run_save/email.txt:2
#, python-format
msgid "Test run %(pk)s has been created or updated for you.\n\n"
"### Links ###\n"
"Test run: %(run_url)s\n"
"Test plan: %(plan_url)s\n\n"
"### Basic run information ###\n"
"Summary: %(summary)s\n\n"
"Managed: %(manager)s.\n"
"Default tester: %(default_tester)s.\n\n"
"Product: %(product)s\n"
"Product version: %(version)s\n"
"Build: %(build)s\n\n"
"Notes:\n"
"%(notes)s"
msgstr "%(pk)s 테스트 수행이 생성 혹은 갱신되었습니다.\n\n"
"### 연결 ###\n"
"테스트 수행: %(run_url)s\n"
"테스트 계획: %(plan_url)s\n\n"
"### 수행 기본 정보 ###\n"
"요약: %(summary)s\n\n"
"관리: %(manager)s\n"
"기본 테스터: %(default_tester)s\n\n"
"제품: %(product)s\n"
"제품 버전: %(version)s\n"
"빌드: %(build)s\n\n"
"노트:\n"
"%(notes)s"

#: tcms/templates/email/user_registered/notify_admins.txt:2
#, python-format
msgid "Dear Administrator,\n"
"somebody just registered an account with username %(username)s at your\n"
"Kiwi TCMS instance and is awaiting your approval!\n\n"
"Go to %(user_url)s to activate the account!"
msgstr "%(username)s라는 사용자가 Kiwi TCMS 계정 등록이 승인되길 기다리고 있습니다!\n\n"
"계정을 활성화하려면 %(user_url)s로 이동하세요!"

#: tcms/templates/include/attachments.html:10
#: tcms/testplans/templates/testplans/get.html:437
#: tcms/testruns/templates/testruns/get.html:487
msgid "Attachments"
msgstr "첨부 파일"

#: tcms/templates/include/attachments.html:18
msgid "File"
msgstr "파일"

#: tcms/templates/include/attachments.html:19
msgid "Owner"
msgstr "소유자"

#: tcms/templates/include/attachments.html:20
msgid "Date"
msgstr "날짜"

#: tcms/templates/include/attachments.html:35
#: tcms/templates/include/bugs_table.html:4
#: tcms/testplans/templates/testplans/get.html:332
msgid "No records found"
msgstr "항목을 찾을 수 없습니다"

#: tcms/templates/include/bugs_table.html:15
#: tcms/testruns/templates/testruns/get.html:573
msgid "URL"
msgstr "URL"

#: tcms/templates/include/properties_card.html:9 tcms/testruns/admin.py:117
#: tcms/testruns/templates/testruns/get.html:345
msgid "Parameters"
msgstr "매개 변수"

#: tcms/templates/include/properties_card.html:15
#: tcms/testruns/templates/testruns/mutable.html:170
msgid "This is a tech-preview feature!"
msgstr "사전 체험 기능입니다!"

#: tcms/templates/include/properties_card.html:68
msgid "name=value"
msgstr "이름=값"

#: tcms/templates/include/properties_card.html:69
#: tcms/templates/include/tags_card.html:28
#: tcms/testcases/templates/testcases/get.html:184
#: tcms/testcases/templates/testcases/get.html:229
#: tcms/testplans/templates/testplans/get.html:258
#: tcms/testruns/templates/testruns/get.html:292
#: tcms/testruns/templates/testruns/get.html:546
msgid "Add"
msgstr "추가"

#: tcms/templates/include/tags_card.html:13
#: tcms/testcases/templates/testcases/get.html:167
#: tcms/testcases/templates/testcases/get.html:215
#: tcms/testplans/templates/testplans/clone.html:14
#: tcms/testplans/templates/testplans/get.html:205
#: tcms/testplans/templates/testplans/mutable.html:24
#: tcms/testplans/templates/testplans/search.html:18
#: tcms/testruns/templates/testruns/get.html:579
msgid "Name"
msgstr "이름"

#: tcms/templates/initdb.html:5 tcms/templates/initdb.html:17
#: tcms/templates/initdb.html:29
msgid "Initialize database"
msgstr "데이터베이스 초기화"

#: tcms/templates/initdb.html:20
msgid "Your database has not been initialized yet. Click the button below to initialize it!"
msgstr "데이터베이스가 아직 초기화되지 않았습니다! 아래 버튼을 클릭해 초기화하세요!"

#: tcms/templates/initdb.html:22
msgid "WARNING: this operation will take a while! This page will redirect when done."
msgstr "경고: 이 작업은 시간이 꽤 걸릴 수 있습니다! 작업이 완료되면 이 페이지가 리디렉션됩니다."

#: tcms/templates/initdb.html:27
msgid "Please wait"
msgstr "잠시만 기다려 주세요"

#: tcms/templates/navbar.html:9
msgid "Toggle navigation"
msgstr "전환 탐색"

#: tcms/templates/navbar.html:15
msgid "DASHBOARD"
msgstr "계기반"

#: tcms/templates/navbar.html:41
msgid "Language"
msgstr "언어"

#: tcms/templates/navbar.html:45
msgid "Change language"
msgstr "언어 변경"

#: tcms/templates/navbar.html:46
msgid "Supported languages"
msgstr "지원되는 언어"

#: tcms/templates/navbar.html:47
msgid "Request new language"
msgstr "새로운 언어 요청"

#: tcms/templates/navbar.html:53
msgid "Translation mode"
msgstr "번역 모드"

#: tcms/templates/navbar.html:57
msgid "Translation guide"
msgstr "번역 안내서"

#: tcms/templates/navbar.html:63
msgid "Help"
msgstr "도움말"

#: tcms/templates/navbar.html:78
msgid "Welcome Guest"
msgstr "방문을 환영합니다"

#: tcms/templates/navbar.html:84
msgid "My Test Runs"
msgstr "내 테스트 수행"

#: tcms/templates/navbar.html:88
msgid "My Test Plans"
msgstr "내 테스트 계획"

#: tcms/templates/navbar.html:94
msgid "My profile"
msgstr "내 프로필"

#: tcms/templates/navbar.html:98
#: tcms/templates/registration/password_reset_confirm.html:29
msgid "Change password"
msgstr "비밀번호 변경"

#: tcms/templates/navbar.html:108
msgid "Logout"
msgstr "로그아웃"

#: tcms/templates/navbar.html:113 tcms/templates/registration/login.html:4
msgid "Login"
msgstr "로그인"

#: tcms/templates/navbar.html:120
#: tcms/templates/registration/registration_form.html:53
msgid "Register"
msgstr "회원가입"

#: tcms/templates/registration/login.html:24
#: tcms/templates/registration/password_reset_confirm.html:15
#: tcms/templates/registration/registration_form.html:23
msgid "Password"
msgstr "비밀번호"

#: tcms/templates/registration/login.html:31
msgid "Forgot password"
msgstr "비밀번호를 잊어버렸나요"

#: tcms/templates/registration/login.html:34
msgid "Log in"
msgstr "로그인"

#: tcms/templates/registration/login.html:45
msgid "Welcome to Kiwi TCMS"
msgstr "Kiwi TCMS에 오신 것을 환영합니다"

#: tcms/templates/registration/login.html:50
msgid "Please login to get started"
msgstr "시작하려면 로그인하세요"

#: tcms/templates/registration/login.html:52
msgid "or"
msgstr "또는"

#: tcms/templates/registration/login.html:53
msgid "register an account"
msgstr "계정 등록"

#: tcms/templates/registration/login.html:54
msgid "if you don't have one!"
msgstr "계정이 없다면!"

#: tcms/templates/registration/password_reset_complete.html:12
msgid "Your password has been set. You may go ahead and"
msgstr "비밀번호가 설정되었습니다. 계속해서"

#: tcms/templates/registration/password_reset_complete.html:13
msgid "now"
msgstr "지금"

#: tcms/templates/registration/password_reset_confirm.html:43
msgid "Please enter your new password twice so we can verify you typed it in correctly"
msgstr "새 비밀번호를 두 번 입력하여 올바르게 입력했는지 확인하세요"

#: tcms/templates/registration/password_reset_confirm.html:46
msgid "request a new password reset"
msgstr "새 비밀번호 재설정 요청"

#: tcms/templates/registration/password_reset_done.html:11
msgid "Password reset email was sent"
msgstr "비밀번호 재설정 전자우편을 발송했습니다"

#: tcms/templates/registration/password_reset_form.html:27
msgid "Password reset"
msgstr "비밀번호 초기화"

#: tcms/templates/registration/password_reset_form.html:34
msgid "Kiwi TCMS password reset"
msgstr "Kiwi TCMS 비밀번호 재설정"

#: tcms/templates/registration/registration_form.html:4
msgid "Register new account"
msgstr "신규 계정 등록"

#: tcms/testcases/admin.py:28
msgid "For more information about customizing test case statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-case-statuses\">\n"
"        the documentation</a>!"
msgstr "테스트 케이스 상태를 사용자 정의하는 방법을 더 자세히 보려면\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-case-statuses\">\n"
"        문서</a>를 참고하세요!"

#: tcms/testcases/admin.py:56
msgid "1 confirmed & 1 uncomfirmed status required!"
msgstr "1개의 확인 상태와 1개의 미확인 상태가 필요합니다!"

#: tcms/testcases/admin.py:131
msgid "Bug URL"
msgstr "버그 URL"

#: tcms/testcases/admin.py:151
msgid "External Issue Tracker Integration"
msgstr "외부 이슈 트래커 통합"

#: tcms/testcases/admin.py:161
msgid "<h1>Warning: read the\n"
"<a href=\"http://kiwitcms.readthedocs.io/en/latest/admin.html#configure-external-bug-trackers\">\n"
"Configure external bug trackers</a> section before editting the values below!</h1>"
msgstr "<h1>경고: 아래 값을 수정하기 전에 <a href=\"http://kiwitcms.readthedocs.io/en/latest/admin.html#configure-external-bug-trackers\">외부 이슈 트래커 설정</a> 섹션을 읽어보세요!</h1>"

#: tcms/testcases/admin.py:168
msgid "Configuration health check"
msgstr "설정 상태 검사"

#: tcms/testcases/admin.py:172
msgid "Kiwi TCMS will try fetching details for the given bug URL using the integration defined above! Click the `Save and continue` button and watch out for messages at the top of the screen. <strong>WARNING:</strong> in case of failures some issue trackers will fall back to fetching details via the OpenGraph protocol. In that case the result will include field named `from_open_graph`."
msgstr "위에서 정의한 통합 정보를 사용해 Kiwi TCMS가 주어진 버그 URL의 세부 정보를 가져옵니다! `저장 후 계속` 버튼을 클릭하고 화면 상단의 메시지를 주의 깊게 살펴보세요. <strong>경고:</strong> 실패할 경우 일부 이슈 트래커는 OpenGraph 프로토콜을 사용해 세부 정보를 가져오려고 시도합니다. 이 경우, 결과에 `from_open_graph` 필드가 포함됩니다."

#: tcms/testcases/admin.py:192
msgid "Failed creating Issue Tracker"
msgstr "이슈 트래커를 만들지 못했습니다"

#: tcms/testcases/admin.py:201
msgid "Details extracted via OpenGraph. Issue Tracker may still be configured incorrectly!"
msgstr "OpenGraph를 사용해 세부 정보를 가져왔습니다. 이슈 트래커가 잘못 설정되었을 수 있습니다!"

#: tcms/testcases/admin.py:210
msgid "Details extracted via API. Issue Tracker configuration looks good!"
msgstr "API를 사용해 세부 정보를 가져왔습니다. 이슈 트래커 설정이 잘 되었습니다!"

#: tcms/testcases/admin.py:223
msgid "Issue Tracker configuration check failed"
msgstr "이슈 트래커 설정 검사 실패"

#: tcms/testcases/helpers/email.py:22
#, python-format
msgid "DELETED: TestCase #%(pk)d - %(summary)s"
msgstr "삭제: 테스트 케이스 #%(pk)d - %(summary)s"

#: tcms/testcases/models.py:23
msgid "Test case status"
msgstr "테스트 케이스 상태"

#: tcms/testcases/models.py:24
msgid "Test case statuses"
msgstr "테스트 케이스 상태"

#: tcms/testcases/models.py:379
#: tcms/testcases/templates/testcases/mutable.html:107
msgid "Template"
msgstr "템플릿"

#: tcms/testcases/models.py:380
msgid "Templates"
msgstr "템플릿"

#: tcms/testcases/templates/testcases/clone.html:5
msgid "Clone TestCase"
msgstr "테스트 케이스 복제"

#: tcms/testcases/templates/testcases/clone.html:15
msgid "Add new TC into TP"
msgstr "같은 테스트 계획에 새로운 테스트 케이스로 추가"

#: tcms/testcases/templates/testcases/clone.html:30
msgid "Selected TC"
msgstr "선택된 테스트 케이스"

#: tcms/testcases/templates/testcases/clone.html:45 tcms/testcases/views.py:134
#: tcms/testplans/templates/testplans/clone.html:73
#: tcms/testplans/templates/testplans/get.html:138
#: tcms/testplans/templates/testplans/get.html:358 tcms/testplans/views.py:144
#: tcms/testruns/views.py:198
msgid "Clone"
msgstr "복제"

#: tcms/testcases/templates/testcases/get.html:28
#: tcms/testcases/templates/testcases/get.html:168
#: tcms/testcases/templates/testcases/mutable.html:184
#: tcms/testcases/templates/testcases/search.html:122
#: tcms/testcases/templates/testcases/search.html:169
#: tcms/testplans/templates/testplans/get.html:54
#: tcms/testplans/templates/testplans/get.html:412
#: tcms/testplans/templates/testplans/get.html:492
#: tcms/testplans/templates/testplans/mutable.html:121
#: tcms/testplans/templates/testplans/search.html:82
#: tcms/testplans/templates/testplans/search.html:120
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:8
#: tcms/testruns/templates/testruns/mutable.html:230
msgid "Author"
msgstr "작성자"

#: tcms/testcases/templates/testcases/get.html:49
#: tcms/testcases/templates/testcases/mutable.html:58
#: tcms/testcases/templates/testcases/search.html:100
#: tcms/testcases/templates/testcases/search.html:164
#: tcms/testplans/templates/testplans/get.html:408
#: tcms/testplans/templates/testplans/get.html:491
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:7
#: tcms/testruns/templates/testruns/get.html:266
#: tcms/testruns/templates/testruns/get.html:367
#: tcms/testruns/templates/testruns/mutable.html:233
msgid "Category"
msgstr "분류"

#: tcms/testcases/templates/testcases/get.html:64
#: tcms/testcases/templates/testcases/mutable.html:86
#: tcms/testcases/templates/testcases/search.html:82
#: tcms/testcases/templates/testcases/search.html:166
#: tcms/testplans/templates/testplans/get.html:162
#: tcms/testplans/templates/testplans/get.html:371
#: tcms/testplans/templates/testplans/get.html:404
#: tcms/testplans/templates/testplans/get.html:490
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:6
#: tcms/testruns/templates/testruns/get.html:265
#: tcms/testruns/templates/testruns/get.html:363
#: tcms/testruns/templates/testruns/mutable.html:234
msgid "Priority"
msgstr "우선순위"

#: tcms/testcases/templates/testcases/get.html:73
#: tcms/testcases/templates/testcases/mutable.html:120
msgid "Setup duration"
msgstr "설정 기간"

#: tcms/testcases/templates/testcases/get.html:78
#: tcms/testcases/templates/testcases/mutable.html:127
msgid "Testing duration"
msgstr "테스트 기간"

#: tcms/testcases/templates/testcases/get.html:83
msgid "Expected duration"
msgstr "예상 기간"

#: tcms/testcases/templates/testcases/get.html:98
#: tcms/testcases/templates/testcases/mutable.html:143
msgid "Script"
msgstr "스크립트"

#: tcms/testcases/templates/testcases/get.html:103
#: tcms/testcases/templates/testcases/mutable.html:149
msgid "Arguments"
msgstr "전달 인자"

#: tcms/testcases/templates/testcases/get.html:108
#: tcms/testcases/templates/testcases/mutable.html:157
msgid "Requirements"
msgstr "요구 사항"

#: tcms/testcases/templates/testcases/get.html:113
#: tcms/testcases/templates/testcases/mutable.html:162
#: tcms/testplans/templates/testplans/get.html:80
#: tcms/testplans/templates/testplans/mutable.html:102
msgid "Reference link"
msgstr "참조 링크"

#: tcms/testcases/templates/testcases/get.html:133
#: tcms/testcases/templates/testcases/mutable.html:170
#: tcms/testplans/templates/testplans/get.html:433
#: tcms/testplans/templates/testplans/get.html:434
#: tcms/testruns/templates/testruns/get.html:400
#: tcms/testruns/templates/testruns/mutable.html:202
msgid "Notes"
msgstr "노트"

#: tcms/testcases/templates/testcases/get.html:152
msgid "Bugs"
msgstr "버그"

#: tcms/testcases/templates/testcases/get.html:159
msgid "Test plans"
msgstr "테스트 계획"

#: tcms/testcases/templates/testcases/mutable.html:10
msgid "Edit TestCase"
msgstr "테스트 케이스 편집"

#: tcms/testcases/templates/testcases/mutable.html:59
msgid "add new Category"
msgstr "새 분류 추가"

#: tcms/testcases/templates/testcases/mutable.html:108
msgid "add new Template"
msgstr "새 템플릿 추가"

#: tcms/testcases/templates/testcases/mutable.html:180
#: tcms/testruns/templates/testruns/get.html:517
msgid "Notify"
msgstr "알림"

#: tcms/testcases/templates/testcases/mutable.html:191
msgid "Manager of runs"
msgstr "수행 관리자"

#: tcms/testcases/templates/testcases/mutable.html:198
msgid "Asignees"
msgstr "담당자"

#: tcms/testcases/templates/testcases/mutable.html:214
msgid "Default tester of runs"
msgstr "수행의 기본 테스터"

#: tcms/testcases/templates/testcases/mutable.html:223
msgid "Notify when"
msgstr "알림 시점"

#: tcms/testcases/templates/testcases/mutable.html:224
#: tcms/testplans/templates/testplans/mutable.html:142
msgid "applies only for changes made by somebody else"
msgstr "다른 사람이 변경한 경우에만 적용됨"

#: tcms/testcases/templates/testcases/mutable.html:229
msgid "TestCase is updated"
msgstr "테스트 케이스가 갱신될 때"

#: tcms/testcases/templates/testcases/mutable.html:236
msgid "TestCase is deleted"
msgstr "테스트 케이스가 삭제될 때"

#: tcms/testcases/templates/testcases/mutable.html:245
msgid "CC to"
msgstr "참조(CC) 대상"

#: tcms/testcases/templates/testcases/mutable.html:250
msgid "Email addresses separated by comma. A notification email will be sent to each Email address within CC list."
msgstr "전자우편 주소는 쉼표로 구분됩니다. 참조(CC) 목록 내의 각 전자우편 주소로 알림이 발송됩니다."

#: tcms/testcases/templates/testcases/search.html:15
msgid "Test case summary"
msgstr "테스트 케이스 요약"

#: tcms/testcases/templates/testcases/search.html:45
msgid "Both"
msgstr "모두"

#: tcms/testcases/templates/testcases/search.html:61
msgid "include in search request"
msgstr "검색 요청에 포함"

#: tcms/testcases/templates/testcases/search.html:74
msgid "include child test plans"
msgstr "하위 테스트 계획 포함"

#: tcms/testcases/templates/testcases/search.html:112
#: tcms/testcases/templates/testcases/search.html:165
#: tcms/testplans/templates/testplans/get.html:216
#: tcms/testruns/templates/testruns/get.html:262
msgid "Component"
msgstr "컴포넌트"

#: tcms/testcases/templates/testcases/search.html:128
msgid "Text"
msgstr "텍스트"

#: tcms/testcases/templates/testcases/search.html:139
#: tcms/testplans/templates/testplans/search.html:97
#: tcms/testruns/templates/testruns/search.html:30
msgid "Separate multiple values with comma (,)"
msgstr "여러 값을 쉼표(,) 로 구분"

#: tcms/testcases/templates/testcases/search.html:178
msgid "Select"
msgstr "선택"

#: tcms/testcases/views.py:138 tcms/testplans/views.py:146
#: tcms/testruns/templates/testruns/get.html:458 tcms/testruns/views.py:202
msgid "History"
msgstr "변경 내역"

#: tcms/testcases/views.py:250
msgid "TestCase cloning was successful"
msgstr "테스트 케이스 복제 성공"

#: tcms/testcases/views.py:281
msgid "At least one TestCase is required"
msgstr "적어도 하나의 테스트 케이스가 필요합니다"

#: tcms/testplans/templates/testplans/clone.html:5
msgid "Clone TestPlan"
msgstr "테스트 계획 복제"

#: tcms/testplans/templates/testplans/clone.html:55
msgid "Clone TCs"
msgstr "테스트 케이스 복제"

#: tcms/testplans/templates/testplans/clone.html:59
msgid "Clone or link existing TCs into new TP"
msgstr "기존 테스트 케이스를 복제하거나 새 테스트 계획에 연결"

#: tcms/testplans/templates/testplans/clone.html:63
msgid "Parent TP"
msgstr "상위 테스트 계획"

#: tcms/testplans/templates/testplans/clone.html:67
msgid "Set the source TP as parent of new TP"
msgstr "원본 테스트 계획을 새 테스트 계획의 상위 테스트 계획으로 설정"

#: tcms/testplans/templates/testplans/get.html:25
#: tcms/testruns/templates/testruns/get.html:23
msgid "Enter username, email or user ID:"
msgstr "사용자 이름, 전자우편 주소 혹은 사용자 ID를 입력:"

#: tcms/testplans/templates/testplans/get.html:26
#: tcms/testruns/templates/testruns/get.html:22
msgid "No rows selected! Please select at least one!"
msgstr "최소 하나 이상의 행을 선택해야 합니다!"

#: tcms/testplans/templates/testplans/get.html:27
#: tcms/testruns/templates/testruns/get.html:24
msgid "Are you sure?"
msgstr "확실한가요?"

#: tcms/testplans/templates/testplans/get.html:28
msgid "Cannot create TestRun with unconfirmed test cases"
msgstr "미확인 테스트 케이스로 테스트 수행을 만들 수 없습니다"

#: tcms/testplans/templates/testplans/get.html:29
msgid "Error adding test cases"
msgstr "테스트 케이스 추가 오류"

#: tcms/testplans/templates/testplans/get.html:43
msgid "Show more"
msgstr "더 보기"

#: tcms/testplans/templates/testplans/get.html:75
msgid "Plan Type"
msgstr "계획 유형"

#: tcms/testplans/templates/testplans/get.html:100
msgid "Test cases"
msgstr "테스트 케이스"

#: tcms/testplans/templates/testplans/get.html:182
#: tcms/testplans/templates/testplans/get.html:421
#: tcms/testplans/templates/testplans/get.html:494
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:10
msgid "Reviewer"
msgstr "비평가"

#: tcms/testplans/templates/testplans/get.html:231
#: tcms/testplans/templates/testplans/get.html:234
msgid "Sort key"
msgstr "키 정렬"

#: tcms/testplans/templates/testplans/get.html:244
msgid "Re-order cases"
msgstr "다시 정렬"

#: tcms/testplans/templates/testplans/get.html:254
#: tcms/testruns/templates/testruns/get.html:288
msgid "Search and add test cases"
msgstr "테스트 케이스 검색 및 추가"

#: tcms/testplans/templates/testplans/get.html:263
#: tcms/testruns/templates/testruns/get.html:298
msgid "Advanced search"
msgstr "고급 검색"

#: tcms/testplans/templates/testplans/get.html:286
msgid "Active test runs"
msgstr "활성 테스트 수행"

#: tcms/testplans/templates/testplans/get.html:308
msgid "More"
msgstr "더보기"

#: tcms/testplans/templates/testplans/get.html:310
msgid "Inactive"
msgstr "비활성"

#: tcms/testplans/templates/testplans/get.html:444
#: tcms/testruns/templates/testruns/get.html:494
msgid "No attachments"
msgstr "첨부 파일이 없습니다"

#: tcms/testplans/templates/testplans/get.html:469
#: tcms/testruns/templates/testruns/get.html:483
msgid "Comments"
msgstr "코멘트"

#: tcms/testplans/templates/testplans/mutable.html:10
msgid "Edit TestPlan"
msgstr "테스트 계획 편집"

#: tcms/testplans/templates/testplans/mutable.html:12
msgid "Create new TestPlan"
msgstr "새 테스트 계획 만들기"

#: tcms/testplans/templates/testplans/mutable.html:82
msgid "Parent ID"
msgstr "상위 테스트 계획 식별자"

#: tcms/testplans/templates/testplans/mutable.html:100
msgid "Enter to assign; Backspace + Enter to clear"
msgstr "엔터 키를 눌러 할당; 백스페이스 + 엔터 키를 눌러 초기화"

#: tcms/testplans/templates/testplans/mutable.html:110
msgid "Test plan document:"
msgstr "테스트 계획 문서:"

#: tcms/testplans/templates/testplans/mutable.html:119
msgid "Notify:"
msgstr "알림 대상:"

#: tcms/testplans/templates/testplans/mutable.html:127
msgid "TestCase author"
msgstr "테스트 케이스 작성자"

#: tcms/testplans/templates/testplans/mutable.html:141
msgid "Notify when:"
msgstr "알림 시점:"

#: tcms/testplans/templates/testplans/mutable.html:145
msgid "TestPlan is updated"
msgstr "테스트 계획이 갱신될 때"

#: tcms/testplans/templates/testplans/mutable.html:151
msgid "Test cases are updated"
msgstr "테스트 케이스가 갱신될 때"

#: tcms/testplans/templates/testplans/mutable.html:165
#: tcms/testplans/templates/testplans/search.html:41
msgid "Active"
msgstr "활성"

#: tcms/testplans/templates/testplans/search.html:12
msgid "Some child test plans do not match search criteria"
msgstr "일부 하위 테스트 계획이 검색 조건에 일치하지 않습니다"

#: tcms/testplans/templates/testplans/search.html:20
msgid "Test plan name"
msgstr "테스트 계획 이름"

#: tcms/testruns/admin.py:32
msgid "Permission denied: TestRun does not belong to you"
msgstr "권한 없음: 소유한 테스트 수행이 아닙니다."

#: tcms/testruns/admin.py:39
msgid "For more information about customizing test execution statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-execution-statuses\">\n"
"        the documentation</a>!"
msgstr "테스트 실행 상태 사용자 지정에 대한 자세한 내용은<a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-execution-statuses\">설명서</a>!"

#: tcms/testruns/admin.py:95
msgid "1 negative, 1 neutral & 1 positive status required!"
msgstr "부정, 중립, 긍정 상태가 1개씩 필요합니다!"

#: tcms/testruns/admin.py:106
msgid "Edit parameters"
msgstr "매개 변수 편집"

#: tcms/testruns/forms.py:39
msgid "Full"
msgstr ""

#: tcms/testruns/forms.py:40
msgid "Pairwise"
msgstr ""

#: tcms/testruns/models.py:230
msgid "Test execution statuses"
msgstr "테스트 실행 상태"

#: tcms/testruns/templates/testruns/get.html:25
msgid "Unconfirmed test cases were not added"
msgstr "미확인 테스트 케이스가 추가되지 않았습니다"

#: tcms/testruns/templates/testruns/get.html:26
msgid "Type 0 or 1"
msgstr "유형 0 혹은 1"

#: tcms/testruns/templates/testruns/get.html:27
msgid "Comment"
msgstr "코멘트"

#: tcms/testruns/templates/testruns/get.html:60
#: tcms/testruns/templates/testruns/mutable.html:29
#: tcms/testruns/templates/testruns/search.html:67
#: tcms/testruns/templates/testruns/search.html:188
msgid "Manager"
msgstr "관리자"

#: tcms/testruns/templates/testruns/get.html:73
#: tcms/testruns/templates/testruns/mutable.html:110
#: tcms/testruns/templates/testruns/search.html:127
msgid "Planned start"
msgstr "시작 예정일"

#: tcms/testruns/templates/testruns/get.html:84
msgid "Start"
msgstr "시작"

#: tcms/testruns/templates/testruns/get.html:91
#: tcms/testruns/templates/testruns/mutable.html:123
#: tcms/testruns/templates/testruns/search.html:147
msgid "Planned stop"
msgstr "중단 예정일"

#: tcms/testruns/templates/testruns/get.html:102
msgid "Stop"
msgstr "중단"

#: tcms/testruns/templates/testruns/get.html:139
#: tcms/testruns/templates/testruns/mutable.html:160
msgid "Environment"
msgstr "환경"

#: tcms/testruns/templates/testruns/get.html:182
msgid "Update text version"
msgstr "텍스트 버전 갱신"

#: tcms/testruns/templates/testruns/get.html:217
msgid "Add comment"
msgstr "댓글 추가"

#: tcms/testruns/templates/testruns/get.html:228
#: tcms/testruns/templates/testruns/get.html:331
#: tcms/testruns/templates/testruns/get.html:568
msgid "Add hyperlink"
msgstr "하이퍼링크 추가"

#: tcms/testruns/templates/testruns/get.html:279
msgid "Mine"
msgstr ""

#: tcms/testruns/templates/testruns/get.html:280
msgid "All"
msgstr "전체"

#: tcms/testruns/templates/testruns/get.html:306
msgid "records"
msgstr "레코드"

#: tcms/testruns/templates/testruns/get.html:332
#: tcms/testruns/templates/testruns/get.html:607
#: tcms/testruns/templates/testruns/get.html:624
msgid "Report bug"
msgstr "버그 신고"

#: tcms/testruns/templates/testruns/get.html:339
msgid "Test case is not part of parent test plan"
msgstr "테스트 케이스가 상위 테스트 계획에 포함되어 있지 않습니다"

#: tcms/testruns/templates/testruns/get.html:371
msgid "Assigned to"
msgstr "담당자"

#: tcms/testruns/templates/testruns/get.html:379
#: tcms/testruns/templates/testruns/get.html:381
msgid "Last bug"
msgstr ""

#: tcms/testruns/templates/testruns/get.html:423
msgid "Text version"
msgstr "텍스트 버전"

#: tcms/testruns/templates/testruns/get.html:431
msgid "Bugs and hyperlinks"
msgstr "버그와 하이퍼 링크"

#: tcms/testruns/templates/testruns/get.html:585
msgid "Is a defect"
msgstr "결함 여부"

#: tcms/testruns/templates/testruns/get.html:592
#: tcms/testruns/templates/testruns/get.html:623
msgid "Cancel"
msgstr "취소"

#: tcms/testruns/templates/testruns/get.html:612
msgid "Issue Tracker"
msgstr "이슈 트래커"

#: tcms/testruns/templates/testruns/mutable.html:7
msgid "Edit TestRun"
msgstr "테스트 수행 편집"

#: tcms/testruns/templates/testruns/mutable.html:9
msgid "Clone TestRun"
msgstr "테스트 수행 복제"

#: tcms/testruns/templates/testruns/mutable.html:180
msgid "Affects only test cases with parameters"
msgstr "매개 변수가 있는 테스트 케이스에만 영향을 줍니다"

#: tcms/testruns/templates/testruns/mutable.html:194
msgid "more information"
msgstr "추가 정보"

#: tcms/testruns/templates/testruns/mutable.html:218
msgid "Selected TestCase(s):"
msgstr "선택한 테스트 케이스:"

#: tcms/testruns/templates/testruns/mutable.html:221
#, python-format
msgid "%(count)s of the pre-selected test cases is not CONFIRMED and will not be cloned!\n"
"See test plan for more details!"
msgstr "확인된 상태가 아닌 %(count)s개의 선택된 테스트 케이스는 복제되지 않습니다!\n"
"자세한 내용은 테스트 계획을 참조하세요!"

#: tcms/testruns/templates/testruns/search.html:21
msgid "Plan ID"
msgstr "테스트 계획 식별자"

#: tcms/testruns/templates/testruns/search.html:23
msgid "TestPlan ID"
msgstr "테스트 계획 식별자"

#: tcms/testruns/templates/testruns/search.html:79
msgid "Running"
msgstr "진행 중"

#: tcms/testruns/templates/testruns/search.html:86
#: tcms/testruns/templates/testruns/search.html:186
msgid "Start date"
msgstr "시작 날짜"

#: tcms/testruns/templates/testruns/search.html:106
#: tcms/testruns/templates/testruns/search.html:187
msgid "Stop date"
msgstr "중단 날짜"

#: tcms/testruns/views.py:273
msgid "Clone of "
msgstr "의 복제 "

#: testcases.TestCaseStatus/name:1
msgid "PROPOSED"
msgstr "제안"

#: testcases.TestCaseStatus/name:3
msgid "DISABLED"
msgstr "비활성화"

#: testcases.TestCaseStatus/name:4
msgid "NEED_UPDATE"
msgstr "업데이트 필요"

#: testruns.TestExecutionStatus/name:1
msgid "IDLE"
msgstr "대기"

#: testruns.TestExecutionStatus/name:2
msgid "RUNNING"
msgstr "진행 중"

#: testruns.TestExecutionStatus/name:3
msgid "PAUSED"
msgstr "일시정지"

#: testruns.TestExecutionStatus/name:4
msgid "PASSED"
msgstr "통과"

#: testruns.TestExecutionStatus/name:5
msgid "FAILED"
msgstr "실패"

#: testruns.TestExecutionStatus/name:6
msgid "BLOCKED"
msgstr "막힘"

#: testruns.TestExecutionStatus/name:7
msgid "ERROR"
msgstr "오류"

#: testruns.TestExecutionStatus/name:8
msgid "WAIVED"
msgstr "면제"

#: tcms_github_app/admin.py:122
#, python-format
msgid "For additional configuration see\n"
"<a href=\"%s\">GitHub</a>"
msgstr "추가 설정 정보는\n"
"<a href=\"%s\">GitHub</a>에서 확인하세요"

#: tcms_github_app/menu.py:11
msgid "GitHub integration"
msgstr "GitHub 통합"

#: tcms_github_app/menu.py:12
msgid "Resync"
msgstr "재동기화"

#: tcms_github_app/menu.py:13
msgid "Settings"
msgstr "설정"

#: tcms_github_app/middleware.py:41
#, python-format
msgid "Unconfigured GitHub App %d"
msgstr "구성되지 않은 GitHub 앱 %d"

#: tcms_github_app/utils.py:274
#, python-format
msgid "%s was imported from GitHub"
msgstr "%s는 GitHub에서 들여왔습니다"

#: tcms_github_app/utils.py:278
#, python-format
msgid "%s already exists"
msgstr "%s는 이미 존재합니다"

#: tcms_github_app/views.py:48
#, python-format
msgid "You have not logged-in via GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "GitHub 계정을 통해 로그인하지 않았습니다! <a href=\"%s\"> 여기를 클릭하세요 </a>!"

#: tcms_github_app/views.py:62
#, python-format
msgid "You have not installed Kiwi TCMS into your GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "당신은 당신의 GitHub 계정에 Kiwi TCMS를 설치하지 않았습니다!<a href=\"%s\"> 여기를 클릭하세요 </a>!"

#: tcms_github_app/views.py:76
msgid "Multiple GitHub App installations detected! See below:"
msgstr "여러 GitHub 앱 설치가 감지되었습니다! 아래를 참조하십시오."

#: tcms_github_app/views.py:85
#, python-format
msgid "Edit GitHub App <a href=\"%s\">%s</a>"
msgstr "GitHub 앱 편집 <a href=\"%s\">%s</a>"

#: tcms_github_app/views.py:102
#, python-format
msgid "Cannot find GitHub App installation for tenant \"%s\""
msgstr "\"%s\" 테넌트에서 GitHub App 설치를 찾을 수 없습니다"

#: tcms_github_app/views.py:111
msgid "Multiple GitHub App installations detected!"
msgstr "여러 GitHub App 설치가 감지되었습니다!"

#: tcms_github_marketplace/menu.py:11
msgid "Subscriptions"
msgstr "구독"

#: tcms_github_marketplace/templates/tcms_github_marketplace/email/exit_poll.txt:1
msgid "Thank you for using Kiwi TCMS via a paid subscription.\n"
"We're sorry to see you go but we'd like to know why so we can improve in the future!\n\n"
"You can share your feedback at https://forms.gle/RJhFengbjN9C6wtUA\n\n"
"Thank you!"
msgstr "Kiwi TCMS를 유료 구독해 주셔서 감사합니다!\n"
"번거로우시겠지만 왜 구독을 하게 되었는지 알려주시면 향후 개선에 큰 도움이 될 수 있습니다!\n\n"
"다음 링크를 통해 의견을 공유해 주세요: https://forms.gle/RJhFengbjN9C6wtUA\n\n"
"감사합니다!"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:6
msgid "Tenant subscriptions"
msgstr "테넌트 구독"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:15
msgid "You can access the following tenants"
msgstr "다음 테넌트에 접근할 수 있습니다"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:20
msgid "Tenant"
msgstr "테넌트"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:31
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:90
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:30
msgid "Organization"
msgstr "조직"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:44
msgid "Docker credentials"
msgstr "도커 자격 증명"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:61
msgid "Private containers instructions"
msgstr "비공개 컨테이너 지침"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:75
msgid "You own the following tenants"
msgstr "다음 테넌트를 소유하고 있습니다"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:97
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:98
msgid "Price"
msgstr "가격"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:102
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:103
msgid "Subscription type"
msgstr "구독 유형"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:107
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:108
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:8
msgid "Paid until"
msgstr "결제 기한"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:114
msgid "Cancel subscription"
msgstr "구독 취소"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:127
msgid "You don't own any tenants"
msgstr "소유 중인 테넌트가 없습니다"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:131
msgid "Subscribe via FastSpring"
msgstr "FastSpring을 통해 구독"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:147
msgid "Transaction history"
msgstr "결제 내역"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:169
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:170
msgid "Sender"
msgstr "청구자"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:174
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:175
msgid "Vendor"
msgstr "공급 업체"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:179
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:180
msgid "Received on"
msgstr "수신"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:4
msgid "Extra emails"
msgstr "기타 전자우편"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:14
msgid "Kiwi TCMS will try to match recurring billing events against tenant.owner.email + tenant.extra_emails"
msgstr ""

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:17
msgid "Separate by comma (,), semi-colon (;) or white space ( )"
msgstr "쉼표(,), 쌍반점(;)이나 공백으로 분리"

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:44
msgid "Private Tenant Warning"
msgstr "비공개 테넌트 경고"

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:49
msgid "You are about to create a Private Tenant for Kiwi TCMS.\n"
"It will take a few minutes until your DB schema is ready!\n"
"After clicking the 'Save' button <strong>do not</strong> close or refresh this page!<br>\n"
"You will be redirected to your new tenant when the creation process is complete!\n"
"If you see a 500 Internal Server Error page please contact\n"
"<a href=\"mailto:<EMAIL>\"><EMAIL></a> immediately!"
msgstr "Kiwi TCMS에 대한 개인 테넌트를 만들려고 합니다.\n"
"DB 스키마가 준비될 때까지 몇 분 정도 걸릴 것입니다!\n"
"'저장' 버튼을 클릭한 후 <strong> 안 함 </strong> 닫거나이 페이지를 새로 고침!<br>\n"
"생성 프로세스가 완료되면 새 테넌트로 리디렉션됩니다!\n"
"500 내부 서버 오류 페이지가 표시되면\n"
"<a href=\"mailto:<EMAIL>\"><EMAIL></a>즉시!"

#: tcms_github_marketplace/utils.py:95
msgid "Kiwi TCMS Subscription Exit Poll"
msgstr "Kiwi TCMS 구독 종료 설문조사"

#: tcms_github_marketplace/views.py:567
msgid "Kiwi TCMS subscription notification"
msgstr "Kiwi TCMS 구독 알림"

#: tcms_github_marketplace/views.py:749
msgid "mo"
msgstr "개월"

#: tcms_github_marketplace/views.py:752
msgid "yr"
msgstr "년"

#: tcms_enterprise/pipeline.py:17
msgid "Email address is required"
msgstr "전자우편 주소는 반드시 입력해야 합니다."

#: tcms_enterprise/templates/registration/custom_login.html:10
msgid "or Continue With"
msgstr "또는 계속하기"

#: tcms_settings_dir/enterprise.py:19
msgid "Legal information"
msgstr "법적 정보"

#: tcms_tenants/admin.py:55 tcms_tenants/admin.py:62
#: tcms_tenants/middleware.py:35
msgid "Unauthorized"
msgstr "권한 없음"

#: tcms_tenants/admin.py:86
msgid "Existing username, email or user ID"
msgstr "존재하는 사용자 이름, 전자우편 주소 혹은 식별자"

#: tcms_tenants/admin.py:159
msgid "Full name"
msgstr "성명"

#: tcms_tenants/forms.py:30
msgid "Invalid string"
msgstr "유효하지 않은 문자열"

#: tcms_tenants/menu.py:15
msgid "Create"
msgstr "만들기"

#: tcms_tenants/menu.py:20
#: tcms_tenants/templates/tcms_tenants/invite_users.html:17
msgid "Invite users"
msgstr "사용자 초대"

#: tcms_tenants/menu.py:21
msgid "Authorized users"
msgstr "인가된 사용자"

#: tcms_tenants/middleware.py:59
msgid "Unpaid"
msgstr "미지급"

#: tcms_tenants/middleware.py:70
msgid "Tenant expires soon"
msgstr ""

#: tcms_tenants/templates/tcms_tenants/email/invite_user.txt:1
#, python-format
msgid "Dear tester,\n"
"%(invited_by)s has invited you to join their Kiwi TCMS tenant at\n"
"%(tenant_url)s\n\n"
"In case you have never logged in before an account was created for you\n"
"automatically. You can login with a social account which has the same email\n"
"address or go to %(password_reset_url)s to reset your password.\n"
"The password reset email message also contains your username!"
msgstr ""

#: tcms_tenants/templates/tcms_tenants/email/new.txt:1
#, python-format
msgid "Your Kiwi TCMS tenant was created at:\n"
"%(tenant_url)s\n\n"
"If you have troubles please contact support!"
msgstr "Kiwi TCMS 테넌트가 %(tenant_url)s에 만들어졌습니다.\n\n"
"문제가 있으면 지원 부서에 문의하세요!"

#: tcms_tenants/templates/tcms_tenants/invite_users.html:28
msgid "Email"
msgstr "전자우편"

#: tcms_tenants/templates/tcms_tenants/new.html:18
msgid "New tenant"
msgstr "새로운 테넌트"

#: tcms_tenants/templates/tcms_tenants/new.html:35
msgid "Company, team or project name"
msgstr "회사, 팀 또는 프로젝트 이름"

#: tcms_tenants/templates/tcms_tenants/new.html:43
msgid "Schema"
msgstr "스키마"

#: tcms_tenants/templates/tcms_tenants/new.html:56
msgid "Validation pattern"
msgstr "검증 패턴"

#: tcms_tenants/templates/tcms_tenants/new.html:61
msgid "Publicly readable"
msgstr "공개적으로 읽기 가능"

#: tcms_tenants/templates/tcms_tenants/new.html:80
msgid "Tenant logo"
msgstr "테넌트 로고"

#: tcms_tenants/utils.py:66
msgid "Schema name already in use"
msgstr "이미 사용 중인 스키마 이름"

#: tcms_tenants/utils.py:170
msgid "New Kiwi TCMS tenant created"
msgstr "새로운 Kiwi TCMS 테넌트 생성"

#: tcms_tenants/utils.py:230
#, python-brace-format
msgid "User {user.username} added to tenant group {group.name}"
msgstr "{group.name} 테넌트 그룹에 {user.username}님이 추가되었습니다"

#: tcms_tenants/utils.py:262
msgid "Invitation to join Kiwi TCMS"
msgstr "Kiwi TCMS 가입 초대"

#: tcms_tenants/views.py:84
msgid "Only super-user and tenant owner are allowed to edit tenant properties"
msgstr "슈퍼유저와 테넌트 소유자만 테넌트 속성을 편집할 수 있습니다"

#: tcms_tenants/views.py:102
msgid "Edit tenant"
msgstr "테넌트 편집"

#: tcms_tenants/views.py:153
msgid "Only users who are authorized for this tenant can invite others"
msgstr "이 테넌트에 권한이 있는 사용자만 다른 사용자를 초대할 수 있습니다"

#: tenant_groups/admin.py:30
msgid "users"
msgstr "사용자"

#: tenant_groups/models.py:34
msgid "name"
msgstr "이름"

#: tenant_groups/models.py:37
msgid "permissions"
msgstr "권한"

#: tenant_groups/models.py:47
msgid "group"
msgstr "그룹"

#: tenant_groups/models.py:48
msgid "groups"
msgstr "그룹"

#: trackers_integration/menu.py:4
msgid "Personal API tokens"
msgstr "개인용 접근 토큰"
