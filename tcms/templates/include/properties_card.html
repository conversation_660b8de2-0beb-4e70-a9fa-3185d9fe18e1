{% load i18n %}

<div class="card-pf card-pf-accented">
    <h2 class="card-pf-title">
        <span class="fa fa-cubes"></span>
        {% if card_title %}
            {{ card_title }}
        {% else %}
            {% trans "Parameters" %}
        {% endif %}

        <span class="fa fa-exclamation-triangle help-tooltip kiwi-float-right kiwi-color-warning"
             data-toggle="tooltip"
             data-placement="left"
             title="{% trans 'This is a tech-preview feature!' %}">
        </span>
    </h2>

    <div class="card-pf-body">
        <div class="panel-group" id="properties-accordion">

        <template id="property-fragment">
            <div class="panel panel-no-border">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#properties-accordion" href="#collapseOne"  class="collapsed js-property-name">
                            ---
                        </a>

                        {% if hide_delete_button %}
                        {% else %}
                        <a href="#" class="js-remove-property no-before kiwi-float-right" title="{% trans 'Delete' %}"
                            data-property-name="">
                            <span class="pficon-error-circle-o hidden-print"></span>
                        </a>
                        {% endif %}
                    </h4>
                </div>

                <div id="collapseOne" class="js-panel-collapse panel-collapse collapse">
                    <div class="panel-body js-panel-body">
                        <template>
                            <span class="badge property-value">
                                <span class="js-property-value">---</span>
                                {% if hide_delete_button %}
                                {% else %}
                                <a href="#" title="{% trans 'Delete' %}" class="js-remove-value" data-id="">
                                    <span class="fa fa-times hidden-print"></span>
                                </a>
                                {% endif %}
                            </span>
                        </template>
                    </div>
                </div>
            </div>
        </template>

            <!-- dynamic content needs to be first otherwise expanding doesn't expand the card -->
            <div class="js-insert-here"></div>

            {% if hide_add_button %}
            {% else%}
            <div class="panel panel-no-border hidden-print">
                <div class="panel-heading">
                    <input type="text" class="form-control typeahead" id="property-value-input"
                        style="width: 80%; display: inline"
                        maxlength="255"
                        placeholder="{% trans 'name=value' %}">
                    <a href="#" class="js-add-property-value kiwi-float-right" title="{% trans 'Add' %}">
                        <span class="fa fa-plus hidden-print"></span>
                    </a>
                </div>
            </div>
            {% endif %}
        </div> <!-- panel-group -->
    </div>
</div>
