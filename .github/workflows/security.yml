name: security

on:
  push:
    branches: master
  pull_request:

permissions: read-all

jobs:
  checkov:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Check with checkov
        run: |
          pip install checkov
          checkov --quiet --directory .

  bandit:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Check with bandit
        run: |
          pip install bandit
          make bandit

  # this is expected to fail b/c bandit detects lots of issues
  # from 3rd party libraries!
  bandit_site_packages:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11]

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Check with bandit
        run: |
          pip install bandit

          pip install -r requirements/mariadb.txt
          pip install -r requirements/postgres.txt

          make bandit_site_packages

  # this is expected to fail but we need to know what issues are there
  npm_audit:
    name: npm audit
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: npm audit
        run: |
          pushd tcms/
          ./npm-install
          npm audit
