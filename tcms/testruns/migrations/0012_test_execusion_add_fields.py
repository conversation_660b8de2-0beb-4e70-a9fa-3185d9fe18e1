# Generated by Django 3.1.6 on 2021-02-17 04:46

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("testruns", "0011_add_planning_fields"),
    ]

    operations = [
        migrations.RenameField(
            model_name="historicaltestexecution",
            old_name="close_date",
            new_name="stop_date",
        ),
        migrations.RenameField(
            model_name="testexecution",
            old_name="close_date",
            new_name="stop_date",
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="stop_date",
            field=models.DateTimeField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name="historicaltestexecution",
            name="stop_date",
            field=models.DateTimeField(blank=True, db_index=True, null=True),
        ),
        migrations.AddField(
            model_name="historicaltestexecution",
            name="start_date",
            field=models.DateTimeField(blank=True, db_index=True, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="testexecution",
            name="start_date",
            field=models.DateTimeField(blank=True, db_index=True, null=True),
        ),
    ]
