msgid ""
msgstr ""
"Project-Id-Version: kiwitcms\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-30 12:22+0000\n"
"PO-Revision-Date: 2025-07-30 13:13\n"
"Last-Translator: \n"
"Language-Team: Esperanto\n"
"Language: eo_UY\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: kiwitcms\n"
"X-Crowdin-Project-ID: 295734\n"
"X-Crowdin-Language: eo\n"
"X-Crowdin-File: /master/tcms/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 19\n"

#: tcms/bugs/forms.py:33
msgid "Description of problem:\n\n\n"
"How often reproducible:\n\n\n"
"Steps to Reproduce:\n"
"1.\n"
"2.\n"
"3.\n\n"
"Actual results:\n\n\n"
"Expected results:\n\n\n"
"Additional info:"
msgstr "crwdns1076:0crwdne1076:0"

#: tcms/bugs/models.py:24 tcms/bugs/templates/bugs/get.html:50
#: tcms/bugs/templates/bugs/mutable.html:27
#: tcms/bugs/templates/bugs/search.html:18
#: tcms/bugs/templates/bugs/search.html:108
msgid "Severity"
msgstr "crwdns1428:0crwdne1428:0"

#: tcms/bugs/templates/bugs/get.html:37 tcms/bugs/templates/bugs/search.html:89
#: tcms/issuetracker/kiwitcms.py:49 tcms/templates/include/bug_details.html:3
msgid "Open"
msgstr "crwdns1018:0crwdne1018:0"

#: tcms/bugs/templates/bugs/get.html:39 tcms/issuetracker/kiwitcms.py:49
#: tcms/templates/include/bug_details.html:3
msgid "Closed"
msgstr "crwdns1020:0crwdne1020:0"

#: tcms/bugs/templates/bugs/get.html:59 tcms/bugs/templates/bugs/search.html:79
#: tcms/bugs/templates/bugs/search.html:114
#: tcms/templates/include/bug_details.html:7
msgid "Reporter"
msgstr "crwdns1022:0crwdne1022:0"

#: tcms/bugs/templates/bugs/get.html:64
#: tcms/bugs/templates/bugs/mutable.html:94
#: tcms/bugs/templates/bugs/search.html:84
#: tcms/bugs/templates/bugs/search.html:115
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:36
#: tcms/templates/include/bug_details.html:10
#: tcms/testruns/templates/testruns/get.html:207
#: tcms/testruns/templates/testruns/get.html:267
msgid "Assignee"
msgstr "crwdns790:0crwdne790:0"

#: tcms/bugs/templates/bugs/get.html:75
#: tcms/bugs/templates/bugs/mutable.html:45
#: tcms/bugs/templates/bugs/search.html:47
#: tcms/bugs/templates/bugs/search.html:111
#: tcms/core/templates/dashboard.html:53 tcms/management/admin.py:107
#: tcms/telemetry/templates/telemetry/include/filters.html:8
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:29
#: tcms/templates/include/bug_details.html:13
#: tcms/testcases/templates/testcases/get.html:44
#: tcms/testcases/templates/testcases/get.html:170
#: tcms/testcases/templates/testcases/mutable.html:43
#: tcms/testcases/templates/testcases/search.html:51
#: tcms/testplans/templates/testplans/clone.html:21
#: tcms/testplans/templates/testplans/get.html:64
#: tcms/testplans/templates/testplans/mutable.html:33
#: tcms/testplans/templates/testplans/search.html:50
#: tcms/testplans/templates/testplans/search.html:117
#: tcms/testruns/templates/testruns/get.html:45
#: tcms/testruns/templates/testruns/mutable.html:46
#: tcms/testruns/templates/testruns/search.html:35
#: tcms/testruns/templates/testruns/search.html:183
msgid "Product"
msgstr "crwdns361:0crwdne361:0"

#: tcms/bugs/templates/bugs/get.html:80
#: tcms/bugs/templates/bugs/mutable.html:60
#: tcms/bugs/templates/bugs/search.html:57
#: tcms/bugs/templates/bugs/search.html:112
#: tcms/telemetry/templates/telemetry/include/filters.html:17
#: tcms/templates/include/bug_details.html:15 tcms/templates/navbar.html:71
#: tcms/testplans/templates/testplans/clone.html:36
#: tcms/testplans/templates/testplans/get.html:70
#: tcms/testplans/templates/testplans/mutable.html:48
#: tcms/testplans/templates/testplans/search.html:60
#: tcms/testplans/templates/testplans/search.html:118
#: tcms/testruns/templates/testruns/get.html:50
#: tcms/testruns/templates/testruns/search.html:45
#: tcms/testruns/templates/testruns/search.html:184
msgid "Version"
msgstr "crwdns384:0crwdne384:0"

#: tcms/bugs/templates/bugs/get.html:85
#: tcms/bugs/templates/bugs/mutable.html:78
#: tcms/bugs/templates/bugs/search.html:67
#: tcms/bugs/templates/bugs/search.html:113 tcms/management/models.py:132
#: tcms/telemetry/templates/telemetry/include/filters.html:25
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:30
#: tcms/templates/include/bug_details.html:17
#: tcms/testruns/templates/testruns/get.html:55
#: tcms/testruns/templates/testruns/get.html:414
#: tcms/testruns/templates/testruns/mutable.html:89
#: tcms/testruns/templates/testruns/search.html:55
#: tcms/testruns/templates/testruns/search.html:185
msgid "Build"
msgstr "crwdns400:0crwdne400:0"

#: tcms/bugs/templates/bugs/get.html:119
msgid "commented on"
msgstr "crwdns1024:0crwdne1024:0"

#: tcms/bugs/templates/bugs/get.html:149
msgid "Reopen"
msgstr "crwdns1102:0crwdne1102:0"

#: tcms/bugs/templates/bugs/get.html:151
#: tcms/bugs/templates/bugs/mutable.html:114
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:78
#: tcms/testcases/templates/testcases/mutable.html:263
#: tcms/testplans/templates/testplans/get.html:477
#: tcms/testplans/templates/testplans/mutable.html:173
#: tcms/testruns/templates/testruns/get.html:480
#: tcms/testruns/templates/testruns/get.html:593
#: tcms/testruns/templates/testruns/mutable.html:210
msgid "Save"
msgstr "crwdns407:0crwdne407:0"

#: tcms/bugs/templates/bugs/get.html:153
msgid "Close"
msgstr "crwdns1026:0crwdne1026:0"

#: tcms/bugs/templates/bugs/mutable.html:20
#: tcms/bugs/templates/bugs/search.html:13
#: tcms/bugs/templates/bugs/search.html:109
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:27
#: tcms/testcases/templates/testcases/mutable.html:24
#: tcms/testcases/templates/testcases/search.html:13
#: tcms/testcases/templates/testcases/search.html:162
#: tcms/testplans/templates/testplans/get.html:212
#: tcms/testplans/templates/testplans/get.html:487
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:3
#: tcms/testruns/templates/testruns/get.html:251
#: tcms/testruns/templates/testruns/get.html:258
#: tcms/testruns/templates/testruns/get.html:261
#: tcms/testruns/templates/testruns/mutable.html:23
#: tcms/testruns/templates/testruns/mutable.html:229
#: tcms/testruns/templates/testruns/search.html:14
#: tcms/testruns/templates/testruns/search.html:181
msgid "Summary"
msgstr "crwdns399:0crwdne399:0"

#: tcms/bugs/templates/bugs/mutable.html:28
msgid "add new Severity"
msgstr "crwdns1430:0crwdne1430:0"

#: tcms/bugs/templates/bugs/mutable.html:46
#: tcms/testcases/templates/testcases/mutable.html:44
#: tcms/testplans/templates/testplans/clone.html:22
#: tcms/testplans/templates/testplans/mutable.html:34
#: tcms/testruns/templates/testruns/mutable.html:48
msgid "add new Product"
msgstr "crwdns452:0crwdne452:0"

#: tcms/bugs/templates/bugs/mutable.html:62
#: tcms/testplans/templates/testplans/clone.html:38
#: tcms/testplans/templates/testplans/mutable.html:50
msgid "add new Version"
msgstr "crwdns454:0crwdne454:0"

#: tcms/bugs/templates/bugs/mutable.html:81
#: tcms/bugs/templates/bugs/mutable.html:82
#: tcms/testruns/templates/testruns/mutable.html:93
#: tcms/testruns/templates/testruns/mutable.html:94
msgid "add new Build"
msgstr "crwdns401:0crwdne401:0"

#: tcms/bugs/templates/bugs/mutable.html:98
#: tcms/testruns/templates/testruns/mutable.html:32
#: tcms/testruns/templates/testruns/mutable.html:39
msgid "Username or email"
msgstr "crwdns404:0crwdne404:0"

#: tcms/bugs/templates/bugs/search.html:5 tcms/settings/common.py:414
msgid "Search Bugs"
msgstr "crwdns1042:0crwdne1042:0"

#: tcms/bugs/templates/bugs/search.html:28
#: tcms/testcases/templates/testcases/search.html:19
#: tcms/testplans/templates/testplans/search.html:24
msgid "Created"
msgstr "crwdns1316:0crwdne1316:0"

#: tcms/bugs/templates/bugs/search.html:31
#: tcms/telemetry/templates/telemetry/include/filters.html:53
#: tcms/testcases/templates/testcases/search.html:22
#: tcms/testplans/templates/testplans/search.html:27
#: tcms/testruns/templates/testruns/search.html:90
#: tcms/testruns/templates/testruns/search.html:110
#: tcms/testruns/templates/testruns/search.html:131
#: tcms/testruns/templates/testruns/search.html:151
msgid "Before"
msgstr "crwdns536:0crwdne536:0"

#: tcms/bugs/templates/bugs/search.html:37
#: tcms/telemetry/templates/telemetry/include/filters.html:43
#: tcms/testcases/templates/testcases/search.html:28
#: tcms/testplans/templates/testplans/search.html:33
#: tcms/testruns/templates/testruns/search.html:97
#: tcms/testruns/templates/testruns/search.html:117
#: tcms/testruns/templates/testruns/search.html:138
#: tcms/testruns/templates/testruns/search.html:158
msgid "After"
msgstr "crwdns534:0crwdne534:0"

#: tcms/bugs/templates/bugs/search.html:81
#: tcms/bugs/templates/bugs/search.html:86
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:45
#: tcms/templates/registration/login.html:17
#: tcms/templates/registration/registration_form.html:15
#: tcms/testcases/templates/testcases/search.html:124
#: tcms/testplans/templates/testplans/search.html:84
#: tcms/testplans/templates/testplans/search.html:90
#: tcms/testruns/templates/testruns/get.html:524
#: tcms/testruns/templates/testruns/search.html:69
#: tcms/testruns/templates/testruns/search.html:75
msgid "Username"
msgstr "crwdns366:0crwdne366:0"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:39
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "Yes"
msgstr "crwdns538:0crwdne538:0"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:42
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "No"
msgstr "crwdns540:0crwdne540:0"

#: tcms/bugs/templates/bugs/search.html:97
#: tcms/testcases/templates/testcases/search.html:150
#: tcms/testplans/templates/testplans/get.html:221
#: tcms/testplans/templates/testplans/search.html:103
#: tcms/testruns/templates/testruns/get.html:273
#: tcms/testruns/templates/testruns/search.html:170
msgid "Search"
msgstr "crwdns392:0crwdne392:0"

#: tcms/bugs/templates/bugs/search.html:107
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:26
#: tcms/testcases/templates/testcases/get.html:166
#: tcms/testcases/templates/testcases/search.html:144
#: tcms/testcases/templates/testcases/search.html:161
#: tcms/testplans/templates/testplans/search.html:114
#: tcms/testruns/templates/testruns/search.html:180
msgid "ID"
msgstr "crwdns393:0crwdne393:0"

#: tcms/bugs/templates/bugs/search.html:110
#: tcms/templates/include/bug_details.html:5
#: tcms/testcases/templates/testcases/search.html:163
#: tcms/testplans/templates/testplans/search.html:116
#: tcms/testruns/templates/testruns/mutable.html:231
msgid "Created on"
msgstr "crwdns410:0crwdne410:0"

#: tcms/bugs/views.py:42 tcms/testcases/views.py:130
#: tcms/testplans/templates/testplans/get.html:380 tcms/testplans/views.py:143
#: tcms/testruns/views.py:194 tcms/testruns/views.py:308
msgid "Edit"
msgstr "crwdns510:0crwdne510:0"

#: tcms/bugs/views.py:45 tcms/testcases/views.py:143
#: tcms/testplans/views.py:151 tcms/testruns/views.py:207
#: tcms/testruns/views.py:316
msgid "Object permissions"
msgstr "crwdns1240:0crwdne1240:0"

#: tcms/bugs/views.py:50
#: tcms/templates/include/comments_for_object_template.html:10
#: tcms/templates/include/properties_card.html:32
#: tcms/templates/include/properties_card.html:47 tcms/testcases/views.py:151
#: tcms/testplans/templates/testplans/get.html:193
#: tcms/testplans/templates/testplans/get.html:384 tcms/testplans/views.py:159
#: tcms/testruns/templates/testruns/get.html:238
#: tcms/testruns/templates/testruns/get.html:450 tcms/testruns/views.py:215
#: tcms/testruns/views.py:324
msgid "Delete"
msgstr "crwdns516:0crwdne516:0"

#: tcms/bugs/views.py:68 tcms/settings/common.py:400
msgid "New Bug"
msgstr "crwdns1040:0crwdne1040:0"

#: tcms/bugs/views.py:188
msgid "Edit bug"
msgstr "crwdns1034:0crwdne1034:0"

#: tcms/bugs/views.py:231
msgid "*bug closed*"
msgstr "crwdns1036:0crwdne1036:0"

#: tcms/bugs/views.py:235
msgid "*bug reopened*"
msgstr "crwdns1104:0crwdne1104:0"

#: tcms/core/history.py:52
#, python-format
msgid "UPDATE: %(model_name)s #%(pk)d - %(title)s"
msgstr "crwdns323:0%(model_name)scrwdnd323:0%(pk)dcrwdnd323:0%(title)scrwdne323:0"

#: tcms/core/history.py:62
#, python-format
msgid "Updated on %(history_date)s\n"
"Updated by %(username)s\n\n"
"%(diff)s\n\n"
"For more information:\n"
"%(instance_url)s"
msgstr "crwdns324:0%(history_date)scrwdnd324:0%(username)scrwdnd324:0%(diff)scrwdnd324:0%(instance_url)scrwdne324:0"

#: tcms/core/templates/dashboard.html:3 tcms/settings/common.py:430
msgid "Dashboard"
msgstr "crwdns353:0crwdne353:0"

#: tcms/core/templates/dashboard.html:8
#: tcms/testruns/templates/testruns/get.html:153
msgid "Test executions"
msgstr "crwdns354:0crwdne354:0"

#: tcms/core/templates/dashboard.html:15
#, python-format
msgid "%(amount)s%% complete"
msgstr "crwdns1108:0%(amount)scrwdne1108:0"

#: tcms/core/templates/dashboard.html:24
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:38
#: tcms/testruns/templates/testruns/get.html:78
msgid "Started at"
msgstr "crwdns355:0crwdne355:0"

#: tcms/core/templates/dashboard.html:32
#, python-format
msgid "%(total_count)s TestRun(s) or TestCase(s) assigned to you need to be executed.\n"
"Here are the latest %(count)s."
msgstr "crwdns1000:0%(total_count)scrwdnd1000:0%(count)scrwdne1000:0"

#: tcms/core/templates/dashboard.html:36 tcms/core/templates/dashboard.html:80
msgid "SEE ALL"
msgstr "crwdns357:0crwdne357:0"

#: tcms/core/templates/dashboard.html:39
msgid "There are no TestRun(s) assigned to you"
msgstr "crwdns358:0crwdne358:0"

#: tcms/core/templates/dashboard.html:46
msgid "Your Test plans"
msgstr "crwdns359:0crwdne359:0"

#: tcms/core/templates/dashboard.html:52
msgid "TestPlan"
msgstr "crwdns360:0crwdne360:0"

#: tcms/core/templates/dashboard.html:54
#: tcms/testcases/templates/testcases/get.html:169
#: tcms/testplans/templates/testplans/mutable.html:66
#: tcms/testplans/templates/testplans/search.html:70
#: tcms/testplans/templates/testplans/search.html:119
msgid "Type"
msgstr "crwdns362:0crwdne362:0"

#: tcms/core/templates/dashboard.html:55
#: tcms/templates/include/tc_executions.html:7
msgid "Executions"
msgstr "crwdns363:0crwdne363:0"

#: tcms/core/templates/dashboard.html:76
#, python-format
msgid "You manage %(total_count)s TestPlan(s), %(disabled_count)s are disabled.\n"
"Here are the latest %(count)s."
msgstr "crwdns1002:0%(total_count)scrwdnd1002:0%(disabled_count)scrwdnd1002:0%(count)scrwdne1002:0"

#: tcms/core/templates/dashboard.html:83
msgid "There are no TestPlan(s) that belong to you"
msgstr "crwdns365:0crwdne365:0"

#: tcms/core/views.py:47
#, python-format
msgid "Base URL is not configured! See <a href=\"%(doc_url)s\">documentation</a> and <a href=\"%(admin_url)s\">change it</a>"
msgstr "crwdns448:0%(doc_url)scrwdnd448:0%(admin_url)scrwdne448:0"

#: tcms/core/views.py:71
#, python-format
msgid "You have %(unapplied_migration_count)s unapplied migration(s). See <a href=\"%(doc_url)s\">documentation</a>"
msgstr "crwdns1212:0%(unapplied_migration_count)scrwdnd1212:0%(doc_url)scrwdne1212:0"

#: tcms/core/views.py:92
#, python-format
msgid "You are not using a secure connection. See <a href=\"%(doc_url)s\">documentation</a> and enable SSL."
msgstr "crwdns1393:0%(doc_url)scrwdne1393:0"

#: tcms/kiwi_attachments/validators.py:10
#, python-brace-format
msgid "File contains forbidden tag: <{tag_name}>"
msgstr "crwdns1495:0{tag_name}crwdne1495:0"

#: tcms/kiwi_attachments/validators.py:92
#, python-brace-format
msgid "File contains forbidden attribute: `{attr_name}`"
msgstr "crwdns1497:0{attr_name}crwdne1497:0"

#: tcms/kiwi_attachments/validators.py:97
msgid "Uploading executable files is forbidden"
msgstr "crwdns1485:0crwdne1485:0"

#: tcms/kiwi_auth/admin.py:36 tcms/settings/common.py:443
msgid "Users"
msgstr "crwdns1432:0crwdne1432:0"

#: tcms/kiwi_auth/admin.py:82
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:5
#: tcms/templates/navbar.html:102
msgid "Reset email address"
msgstr "crwdns1500:0crwdne1500:0"

#: tcms/kiwi_auth/admin.py:148
msgid "Personal info"
msgstr "crwdns328:0crwdne328:0"

#: tcms/kiwi_auth/admin.py:150
msgid "Permissions"
msgstr "crwdns329:0crwdne329:0"

#: tcms/kiwi_auth/admin.py:187
msgid "This is the last superuser, it cannot be deleted!"
msgstr "crwdns1451:0crwdne1451:0"

#: tcms/kiwi_auth/forms.py:28
msgid "A user with that email already exists."
msgstr "crwdns224:0crwdne224:0"

#: tcms/kiwi_auth/forms.py:48
msgid "Please confirm your Kiwi TCMS account email address"
msgstr "crwdns1502:0crwdne1502:0"

#: tcms/kiwi_auth/forms.py:142
msgid "Email mismatch"
msgstr "crwdns1504:0crwdne1504:0"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:14
msgid "Warning"
msgstr "crwdns1506:0crwdne1506:0"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:19
msgid "After clicking the 'Save' button your account will become <strong>inactive</strong>\n"
"and you will be <strong>logged out</strong>! A confirmation email will be sent to the newly specified address!<br>\n"
"Double check that your new email address is <strong>entered correctly</strong> otherwise\n"
"<strong>you may be left locked out</strong> of your account!\n"
"After following the activation link you will be able to log in as usual!"
msgstr "crwdns1508:0crwdne1508:0"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:51
msgid "NOT yourself"
msgstr "crwdns1510:0crwdne1510:0"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:58
#: tcms/templates/registration/password_reset_form.html:16
#: tcms/templates/registration/registration_form.html:39
msgid "E-mail"
msgstr "crwdns374:0crwdne374:0"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:66
#: tcms/templates/registration/password_reset_confirm.html:22
#: tcms/templates/registration/registration_form.html:31
msgid "Confirm"
msgstr "crwdns370:0crwdne370:0"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:71
msgid "Please type! Do not copy-and-paste value from previous field!"
msgstr "crwdns1512:0crwdne1512:0"

#: tcms/kiwi_auth/views.py:70
msgid "Your account has been created, please check your mailbox for confirmation"
msgstr "crwdns293:0crwdne293:0"

#: tcms/kiwi_auth/views.py:75
msgid "Your account has been created, but you need an administrator to activate it"
msgstr "crwdns294:0crwdne294:0"

#: tcms/kiwi_auth/views.py:80
msgid "Following is the administrator list"
msgstr "crwdns295:0crwdne295:0"

#: tcms/kiwi_auth/views.py:123
msgid "This activation key no longer exists in the database"
msgstr "crwdns296:0crwdne296:0"

#: tcms/kiwi_auth/views.py:129
msgid "This activation key has expired"
msgstr "crwdns297:0crwdne297:0"

#: tcms/kiwi_auth/views.py:141
msgid "Your account has been activated successfully"
msgstr "crwdns298:0crwdne298:0"

#: tcms/kiwi_auth/views.py:168 tcms/kiwi_auth/views.py:177
#: tcms/kiwi_auth/views.py:196 tcms/kiwi_auth/views.py:205
#, python-format
msgid "You are viewing records from tenant '%s'"
msgstr "crwdns1471:0%scrwdne1471:0"

#: tcms/kiwi_auth/views.py:256
msgid "Email address has been reset, please check inbox for further instructions"
msgstr "crwdns1514:0crwdne1514:0"

#: tcms/management/models.py:58
#: tcms/telemetry/templates/telemetry/testing/breakdown.html:34
msgid "Priorities"
msgstr "crwdns960:0crwdne960:0"

#: tcms/management/models.py:133
msgid "Builds"
msgstr "crwdns1320:0crwdne1320:0"

#: tcms/management/models.py:144
#: tcms/testcases/templates/testcases/search.html:136
#: tcms/testplans/templates/testplans/get.html:217
#: tcms/testplans/templates/testplans/search.html:94
#: tcms/testruns/templates/testruns/get.html:263
#: tcms/testruns/templates/testruns/search.html:27
msgid "Tag"
msgstr "crwdns388:0crwdne388:0"

#: tcms/management/models.py:145 tcms/templates/include/tags_card.html:6
#: tcms/testcases/templates/testcases/search.html:171
#: tcms/testplans/templates/testplans/get.html:456
#: tcms/testplans/templates/testplans/search.html:121
#: tcms/testruns/templates/testruns/get.html:353
#: tcms/testruns/templates/testruns/search.html:190
msgid "Tags"
msgstr "crwdns494:0crwdne494:0"

#: tcms/rpc/api/bug.py:69
msgid "Enable reporting to this Issue Tracker by configuring its base_url!"
msgstr "crwdns854:0crwdne854:0"

#: tcms/rpc/api/forms/__init__.py:9
msgid "Invalid date format. Expected YYYY-MM-DD [HH:MM:SS]."
msgstr "crwdns1112:0crwdne1112:0"

#: tcms/settings/common.py:391
msgid "TESTING"
msgstr "crwdns331:0crwdne331:0"

#: tcms/settings/common.py:393 tcms/testruns/templates/testruns/mutable.html:70
msgid "New Test Plan"
msgstr "crwdns332:0crwdne332:0"

#: tcms/settings/common.py:395
#: tcms/testcases/templates/testcases/mutable.html:12
#: tcms/testplans/templates/testplans/get.html:131
msgid "New Test Case"
msgstr "crwdns333:0crwdne333:0"

#: tcms/settings/common.py:397 tcms/testplans/templates/testplans/get.html:121
#: tcms/testruns/templates/testruns/get.html:172
#: tcms/testruns/templates/testruns/mutable.html:11
msgid "New Test Run"
msgstr "crwdns1322:0crwdne1322:0"

#: tcms/settings/common.py:407
msgid "SEARCH"
msgstr "crwdns334:0crwdne334:0"

#: tcms/settings/common.py:409 tcms/testplans/templates/testplans/search.html:5
msgid "Search Test Plans"
msgstr "crwdns335:0crwdne335:0"

#: tcms/settings/common.py:410 tcms/testcases/templates/testcases/search.html:5
msgid "Search Test Cases"
msgstr "crwdns337:0crwdne337:0"

#: tcms/settings/common.py:411 tcms/testruns/templates/testruns/search.html:5
msgid "Search Test Runs"
msgstr "crwdns336:0crwdne336:0"

#: tcms/settings/common.py:412
msgid "Search Test Executions"
msgstr "crwdns1535:0crwdne1535:0"

#: tcms/settings/common.py:421
msgid "TELEMETRY"
msgstr "crwdns912:0crwdne912:0"

#: tcms/settings/common.py:424
msgid "Testing"
msgstr "crwdns952:0crwdne952:0"

#: tcms/settings/common.py:426
msgid "Breakdown"
msgstr "crwdns954:0crwdne954:0"

#: tcms/settings/common.py:428
msgid "Execution"
msgstr "crwdns1473:0crwdne1473:0"

#: tcms/settings/common.py:431
#: tcms/testruns/templates/testruns/mutable.html:177
msgid "Matrix"
msgstr "crwdns1413:0crwdne1413:0"

#: tcms/settings/common.py:432
msgid "Trends"
msgstr "crwdns1475:0crwdne1475:0"

#: tcms/settings/common.py:435
#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:5
msgid "TestCase health"
msgstr "crwdns1038:0crwdne1038:0"

#: tcms/settings/common.py:441
msgid "ADMIN"
msgstr "crwdns344:0crwdne344:0"

#: tcms/settings/common.py:444
msgid "Groups"
msgstr "crwdns1434:0crwdne1434:0"

#: tcms/settings/common.py:446
msgid "Everything else"
msgstr "crwdns348:0crwdne348:0"

#: tcms/settings/common.py:449
msgid "MORE"
msgstr "crwdns1546:0crwdne1546:0"

#: tcms/settings/common.py:459
msgid "Report an Issue"
msgstr "crwdns417:0crwdne417:0"

#: tcms/settings/common.py:462
msgid "Ask for help on StackOverflow"
msgstr "crwdns418:0crwdne418:0"

#: tcms/settings/common.py:466
msgid "Donate €5 via Open Collective"
msgstr "crwdns1206:0crwdne1206:0"

#: tcms/settings/common.py:468
msgid "Administration Guide"
msgstr "crwdns420:0crwdne420:0"

#: tcms/settings/common.py:469
msgid "User Guide"
msgstr "crwdns419:0crwdne419:0"

#: tcms/settings/common.py:470
msgid "API Help"
msgstr "crwdns421:0crwdne421:0"

#: tcms/signals.py:85
msgid "New user awaiting approval"
msgstr "crwdns287:0crwdne287:0"

#: tcms/signals.py:163
#, python-format
msgid "NEW: TestRun #%(pk)d - %(summary)s"
msgstr "crwdns325:0%(pk)dcrwdnd325:0%(summary)scrwdne325:0"

#: tcms/signals.py:235
#, python-format
msgid "Bug #%(pk)d - %(summary)s"
msgstr "crwdns1403:0%(pk)dcrwdnd1403:0%(summary)scrwdne1403:0"

#: tcms/telemetry/api.py:60 testcases.TestCaseStatus/name:2
msgid "CONFIRMED"
msgstr "crwdns284:0crwdne284:0"

#: tcms/telemetry/api.py:61
msgid "OTHER"
msgstr "crwdns970:0crwdne970:0"

#: tcms/telemetry/api.py:133 tcms/telemetry/api.py:185
#: tcms/telemetry/api.py:191
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:9
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:13
#: tcms/testruns/templates/testruns/get.html:129
msgid "TOTAL"
msgstr "crwdns836:0crwdne836:0"

#: tcms/telemetry/templates/telemetry/include/filters.html:36
#: tcms/testcases/templates/testcases/search.html:68
#: tcms/testplans/templates/testplans/search.html:115
#: tcms/testruns/templates/testruns/get.html:40
#: tcms/testruns/templates/testruns/mutable.html:67
#: tcms/testruns/templates/testruns/search.html:182
msgid "Test plan"
msgstr "crwdns394:0crwdne394:0"

#: tcms/telemetry/templates/telemetry/include/filters.html:66
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
#: tcms/testcases/templates/testcases/search.html:142
msgid "Test run"
msgstr "crwdns978:0crwdne978:0"

#: tcms/telemetry/templates/telemetry/include/filters.html:70
#: tcms/testruns/templates/testruns/search.html:17
msgid "Test run summary"
msgstr "crwdns423:0crwdne423:0"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:5
msgid "Testing Breakdown"
msgstr "crwdns956:0crwdne956:0"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:14
msgid "Total"
msgstr "crwdns958:0crwdne958:0"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:24
#: tcms/testcases/templates/testcases/get.html:93
#: tcms/testcases/templates/testcases/mutable.html:98
#: tcms/testcases/templates/testcases/search.html:36
#: tcms/testcases/templates/testcases/search.html:168
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testplans/templates/testplans/get.html:489
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:5
#: tcms/testruns/templates/testruns/get.html:264
#: tcms/testruns/templates/testruns/get.html:361
msgid "Automated"
msgstr "crwdns424:0crwdne424:0"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:28
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testruns/templates/testruns/get.html:361
msgid "Manual"
msgstr "crwdns792:0crwdne792:0"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:39
#: tcms/testcases/models.py:42
msgid "Categories"
msgstr "crwdns962:0crwdne962:0"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:5
msgid "Execution Dashboard"
msgstr "crwdns1477:0crwdne1477:0"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:15
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:15
msgid "Child TPs"
msgstr "crwdns1467:0crwdne1467:0"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:28
#: tcms/templates/include/bug_details.html:3
#: tcms/testcases/templates/testcases/get.html:59
#: tcms/testcases/templates/testcases/mutable.html:74
#: tcms/testcases/templates/testcases/search.html:91
#: tcms/testcases/templates/testcases/search.html:167
#: tcms/testplans/templates/testplans/get.html:149
#: tcms/testplans/templates/testplans/get.html:363
#: tcms/testplans/templates/testplans/get.html:396
#: tcms/testplans/templates/testplans/get.html:488
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:4
#: tcms/testruns/templates/testruns/get.html:189
#: tcms/testruns/templates/testruns/get.html:269
#: tcms/testruns/templates/testruns/get.html:385
#: tcms/testruns/templates/testruns/mutable.html:232
msgid "Status"
msgstr "crwdns411:0crwdne411:0"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:31
#: tcms/testcases/templates/testcases/get.html:208
#: tcms/testplans/templates/testplans/get.html:447
#: tcms/testruns/templates/testruns/get.html:349
msgid "Components"
msgstr "crwdns498:0crwdne498:0"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
#: tcms/testcases/templates/testcases/get.html:33
#: tcms/testcases/templates/testcases/mutable.html:36
#: tcms/testcases/templates/testcases/mutable.html:207
#: tcms/testcases/templates/testcases/search.html:170
#: tcms/testplans/templates/testplans/get.html:175
#: tcms/testplans/templates/testplans/get.html:378
#: tcms/testplans/templates/testplans/get.html:416
#: tcms/testplans/templates/testplans/get.html:493
#: tcms/testplans/templates/testplans/mutable.html:133
#: tcms/testplans/templates/testplans/search.html:88
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:9
#: tcms/testruns/templates/testruns/get.html:66
#: tcms/testruns/templates/testruns/mutable.html:36
#: tcms/testruns/templates/testruns/search.html:73
#: tcms/testruns/templates/testruns/search.html:189
msgid "Default tester"
msgstr "crwdns429:0crwdne429:0"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
msgid "TC"
msgstr "crwdns1537:0crwdne1537:0"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
msgid "TR"
msgstr "crwdns1539:0crwdne1539:0"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:37
#: tcms/testruns/templates/testruns/get.html:268
#: tcms/testruns/templates/testruns/get.html:375
msgid "Tested by"
msgstr "crwdns820:0crwdne820:0"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:39
#: tcms/testruns/templates/testruns/get.html:96
#: tcms/testruns/templates/testruns/get.html:405
#: tcms/testruns/templates/testruns/mutable.html:140
msgid "Finished at"
msgstr "crwdns780:0crwdne780:0"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:5
msgid "Execution Trends"
msgstr "crwdns1479:0crwdne1479:0"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:21
msgid "Positive"
msgstr "crwdns1089:0crwdne1089:0"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:24
msgid "Neutral"
msgstr "crwdns1091:0crwdne1091:0"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:27
msgid "Negative"
msgstr "crwdns1093:0crwdne1093:0"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:5
msgid "Execution Matrix"
msgstr "crwdns1481:0crwdne1481:0"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:21
msgid "Order"
msgstr "crwdns1216:0crwdne1216:0"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Ascending"
msgstr "crwdns1218:0crwdne1218:0"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Descending"
msgstr "crwdns1220:0crwdne1220:0"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
msgid "Test case"
msgstr "crwdns976:0crwdne976:0"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:12
msgid "Most frequently failing test cases"
msgstr "crwdns1046:0crwdne1046:0"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:16
msgid "Test Case"
msgstr "crwdns1048:0crwdne1048:0"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:18
msgid "Failed executions"
msgstr "crwdns1050:0crwdne1050:0"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:19
#, python-format
msgid "%% of failed executions"
msgstr "crwdns1052:0crwdne1052:0"

#: tcms/templates/404.html:5 tcms/templates/404.html:16
msgid "Page not found"
msgstr "crwdns350:0crwdne350:0"

#: tcms/templates/500.html:5 tcms/templates/500.html:16
msgid "Internal Server Error"
msgstr "crwdns351:0crwdne351:0"

#: tcms/templates/attachments/add.html:3
msgid "Attachment upload error"
msgstr "crwdns1487:0crwdne1487:0"

#: tcms/templates/attachments/delete_link.html:3
msgid "Are you sure you want to delete this attachment?"
msgstr "crwdns914:0crwdne914:0"

#: tcms/templates/base.html:8
msgid "day"
msgstr "crwdns1362:0crwdne1362:0"

#: tcms/templates/base.html:8
msgid "days"
msgstr "crwdns1364:0crwdne1364:0"

#: tcms/templates/base.html:9
msgid "hour"
msgstr "crwdns1366:0crwdne1366:0"

#: tcms/templates/base.html:9
msgid "hours"
msgstr "crwdns1368:0crwdne1368:0"

#: tcms/templates/base.html:10
msgid "minute"
msgstr "crwdns1370:0crwdne1370:0"

#: tcms/templates/base.html:10
msgid "minutes"
msgstr "crwdns1372:0crwdne1372:0"

#: tcms/templates/base.html:11
msgid "second"
msgstr "crwdns1374:0crwdne1374:0"

#: tcms/templates/base.html:11
msgid "seconds"
msgstr "crwdns1376:0crwdne1376:0"

#: tcms/templates/base.html:16 tcms/templates/registration/login.html:46
msgid "the leading open source test case management system"
msgstr "crwdns352:0crwdne352:0"

#: tcms/templates/email/confirm_registration.txt:1
#, python-format
msgid "Welcome to Kiwi TCMS!\n\n"
"To confirm email address for username `%(user)s` and activate your account\n"
"please follow this URL:\n"
"%(confirm_url)s\n\n"
"Regards,\n"
"Kiwi TCMS"
msgstr "crwdns1516:0%(user)scrwdnd1516:0%(confirm_url)scrwdne1516:0"

#: tcms/templates/email/post_bug_save/email.txt:2
#, python-format
msgid "Bug %(pk)s has been updated.\n\n"
"Link: %(bug_url)s\n\n"
"Summary: %(summary)s\n"
"Created at: %(creation_date)s\n"
"Reporter: %(reporter)s\n"
"Assignee: %(assignee)s\n"
"Product: %(product)s\n"
"Version: %(version)s\n"
"Build: %(build)s\n"
"Last comment:\n"
"%(last_comment)s"
msgstr "crwdns1422:0%(pk)scrwdnd1422:0%(bug_url)scrwdnd1422:0%(summary)scrwdnd1422:0%(creation_date)scrwdnd1422:0%(reporter)scrwdnd1422:0%(assignee)scrwdnd1422:0%(product)scrwdnd1422:0%(version)scrwdnd1422:0%(build)scrwdnd1422:0%(last_comment)scrwdne1422:0"

#: tcms/templates/email/post_case_delete/email.txt:2
#, python-format
msgid "TestCase has been deleted by %(username)s!"
msgstr "crwdns1006:0%(username)scrwdne1006:0"

#: tcms/templates/email/post_run_save/email.txt:2
#, python-format
msgid "Test run %(pk)s has been created or updated for you.\n\n"
"### Links ###\n"
"Test run: %(run_url)s\n"
"Test plan: %(plan_url)s\n\n"
"### Basic run information ###\n"
"Summary: %(summary)s\n\n"
"Managed: %(manager)s.\n"
"Default tester: %(default_tester)s.\n\n"
"Product: %(product)s\n"
"Product version: %(version)s\n"
"Build: %(build)s\n\n"
"Notes:\n"
"%(notes)s"
msgstr "crwdns1008:0%(pk)scrwdnd1008:0%(run_url)scrwdnd1008:0%(plan_url)scrwdnd1008:0%(summary)scrwdnd1008:0%(manager)scrwdnd1008:0%(default_tester)scrwdnd1008:0%(product)scrwdnd1008:0%(version)scrwdnd1008:0%(build)scrwdnd1008:0%(notes)scrwdne1008:0"

#: tcms/templates/email/user_registered/notify_admins.txt:2
#, python-format
msgid "Dear Administrator,\n"
"somebody just registered an account with username %(username)s at your\n"
"Kiwi TCMS instance and is awaiting your approval!\n\n"
"Go to %(user_url)s to activate the account!"
msgstr "crwdns1010:0%(username)scrwdnd1010:0%(user_url)scrwdne1010:0"

#: tcms/templates/include/attachments.html:10
#: tcms/testplans/templates/testplans/get.html:437
#: tcms/testruns/templates/testruns/get.html:487
msgid "Attachments"
msgstr "crwdns500:0crwdne500:0"

#: tcms/templates/include/attachments.html:18
msgid "File"
msgstr "crwdns502:0crwdne502:0"

#: tcms/templates/include/attachments.html:19
msgid "Owner"
msgstr "crwdns504:0crwdne504:0"

#: tcms/templates/include/attachments.html:20
msgid "Date"
msgstr "crwdns506:0crwdne506:0"

#: tcms/templates/include/attachments.html:35
#: tcms/templates/include/bugs_table.html:4
#: tcms/testplans/templates/testplans/get.html:332
msgid "No records found"
msgstr "crwdns508:0crwdne508:0"

#: tcms/templates/include/bugs_table.html:15
#: tcms/testruns/templates/testruns/get.html:573
msgid "URL"
msgstr "crwdns228:0crwdne228:0"

#: tcms/templates/include/properties_card.html:9 tcms/testruns/admin.py:117
#: tcms/testruns/templates/testruns/get.html:345
msgid "Parameters"
msgstr "crwdns1446:0crwdne1446:0"

#: tcms/templates/include/properties_card.html:15
#: tcms/testruns/templates/testruns/mutable.html:170
msgid "This is a tech-preview feature!"
msgstr "crwdns1417:0crwdne1417:0"

#: tcms/templates/include/properties_card.html:68
msgid "name=value"
msgstr "crwdns1401:0crwdne1401:0"

#: tcms/templates/include/properties_card.html:69
#: tcms/templates/include/tags_card.html:28
#: tcms/testcases/templates/testcases/get.html:184
#: tcms/testcases/templates/testcases/get.html:229
#: tcms/testplans/templates/testplans/get.html:258
#: tcms/testruns/templates/testruns/get.html:292
#: tcms/testruns/templates/testruns/get.html:546
msgid "Add"
msgstr "crwdns496:0crwdne496:0"

#: tcms/templates/include/tags_card.html:13
#: tcms/testcases/templates/testcases/get.html:167
#: tcms/testcases/templates/testcases/get.html:215
#: tcms/testplans/templates/testplans/clone.html:14
#: tcms/testplans/templates/testplans/get.html:205
#: tcms/testplans/templates/testplans/mutable.html:24
#: tcms/testplans/templates/testplans/search.html:18
#: tcms/testruns/templates/testruns/get.html:579
msgid "Name"
msgstr "crwdns226:0crwdne226:0"

#: tcms/templates/initdb.html:5 tcms/templates/initdb.html:17
#: tcms/templates/initdb.html:29
msgid "Initialize database"
msgstr "crwdns1328:0crwdne1328:0"

#: tcms/templates/initdb.html:20
msgid "Your database has not been initialized yet. Click the button below to initialize it!"
msgstr "crwdns1352:0crwdne1352:0"

#: tcms/templates/initdb.html:22
msgid "WARNING: this operation will take a while! This page will redirect when done."
msgstr "crwdns1354:0crwdne1354:0"

#: tcms/templates/initdb.html:27
msgid "Please wait"
msgstr "crwdns1332:0crwdne1332:0"

#: tcms/templates/navbar.html:9
msgid "Toggle navigation"
msgstr "crwdns646:0crwdne646:0"

#: tcms/templates/navbar.html:15
msgid "DASHBOARD"
msgstr "crwdns330:0crwdne330:0"

#: tcms/templates/navbar.html:41
msgid "Language"
msgstr "crwdns1060:0crwdne1060:0"

#: tcms/templates/navbar.html:45
msgid "Change language"
msgstr "crwdns1442:0crwdne1442:0"

#: tcms/templates/navbar.html:46
msgid "Supported languages"
msgstr "crwdns1062:0crwdne1062:0"

#: tcms/templates/navbar.html:47
msgid "Request new language"
msgstr "crwdns1064:0crwdne1064:0"

#: tcms/templates/navbar.html:53
msgid "Translation mode"
msgstr "crwdns1066:0crwdne1066:0"

#: tcms/templates/navbar.html:57
msgid "Translation guide"
msgstr "crwdns1068:0crwdne1068:0"

#: tcms/templates/navbar.html:63
msgid "Help"
msgstr "crwdns1058:0crwdne1058:0"

#: tcms/templates/navbar.html:78
msgid "Welcome Guest"
msgstr "crwdns648:0crwdne648:0"

#: tcms/templates/navbar.html:84
msgid "My Test Runs"
msgstr "crwdns650:0crwdne650:0"

#: tcms/templates/navbar.html:88
msgid "My Test Plans"
msgstr "crwdns652:0crwdne652:0"

#: tcms/templates/navbar.html:94
msgid "My profile"
msgstr "crwdns654:0crwdne654:0"

#: tcms/templates/navbar.html:98
#: tcms/templates/registration/password_reset_confirm.html:29
msgid "Change password"
msgstr "crwdns371:0crwdne371:0"

#: tcms/templates/navbar.html:108
msgid "Logout"
msgstr "crwdns656:0crwdne656:0"

#: tcms/templates/navbar.html:113 tcms/templates/registration/login.html:4
msgid "Login"
msgstr "crwdns658:0crwdne658:0"

#: tcms/templates/navbar.html:120
#: tcms/templates/registration/registration_form.html:53
msgid "Register"
msgstr "crwdns378:0crwdne378:0"

#: tcms/templates/registration/login.html:24
#: tcms/templates/registration/password_reset_confirm.html:15
#: tcms/templates/registration/registration_form.html:23
msgid "Password"
msgstr "crwdns367:0crwdne367:0"

#: tcms/templates/registration/login.html:31
msgid "Forgot password"
msgstr "crwdns1444:0crwdne1444:0"

#: tcms/templates/registration/login.html:34
msgid "Log in"
msgstr "crwdns368:0crwdne368:0"

#: tcms/templates/registration/login.html:45
msgid "Welcome to Kiwi TCMS"
msgstr "crwdns369:0crwdne369:0"

#: tcms/templates/registration/login.html:50
msgid "Please login to get started"
msgstr "crwdns740:0crwdne740:0"

#: tcms/templates/registration/login.html:52
msgid "or"
msgstr "crwdns742:0crwdne742:0"

#: tcms/templates/registration/login.html:53
msgid "register an account"
msgstr "crwdns744:0crwdne744:0"

#: tcms/templates/registration/login.html:54
msgid "if you don't have one!"
msgstr "crwdns746:0crwdne746:0"

#: tcms/templates/registration/password_reset_complete.html:12
msgid "Your password has been set. You may go ahead and"
msgstr "crwdns748:0crwdne748:0"

#: tcms/templates/registration/password_reset_complete.html:13
msgid "now"
msgstr "crwdns750:0crwdne750:0"

#: tcms/templates/registration/password_reset_confirm.html:43
msgid "Please enter your new password twice so we can verify you typed it in correctly"
msgstr "crwdns372:0crwdne372:0"

#: tcms/templates/registration/password_reset_confirm.html:46
msgid "request a new password reset"
msgstr "crwdns752:0crwdne752:0"

#: tcms/templates/registration/password_reset_done.html:11
msgid "Password reset email was sent"
msgstr "crwdns373:0crwdne373:0"

#: tcms/templates/registration/password_reset_form.html:27
msgid "Password reset"
msgstr "crwdns375:0crwdne375:0"

#: tcms/templates/registration/password_reset_form.html:34
msgid "Kiwi TCMS password reset"
msgstr "crwdns376:0crwdne376:0"

#: tcms/templates/registration/registration_form.html:4
msgid "Register new account"
msgstr "crwdns377:0crwdne377:0"

#: tcms/testcases/admin.py:28
msgid "For more information about customizing test case statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-case-statuses\">\n"
"        the documentation</a>!"
msgstr "crwdns1262:0crwdne1262:0"

#: tcms/testcases/admin.py:56
msgid "1 confirmed & 1 uncomfirmed status required!"
msgstr "crwdns1264:0crwdne1264:0"

#: tcms/testcases/admin.py:131
msgid "Bug URL"
msgstr "crwdns1378:0crwdne1378:0"

#: tcms/testcases/admin.py:151
msgid "External Issue Tracker Integration"
msgstr "crwdns1382:0crwdne1382:0"

#: tcms/testcases/admin.py:161
msgid "<h1>Warning: read the\n"
"<a href=\"http://kiwitcms.readthedocs.io/en/latest/admin.html#configure-external-bug-trackers\">\n"
"Configure external bug trackers</a> section before editting the values below!</h1>"
msgstr "crwdns1384:0crwdne1384:0"

#: tcms/testcases/admin.py:168
msgid "Configuration health check"
msgstr "crwdns1386:0crwdne1386:0"

#: tcms/testcases/admin.py:172
msgid "Kiwi TCMS will try fetching details for the given bug URL using the integration defined above! Click the `Save and continue` button and watch out for messages at the top of the screen. <strong>WARNING:</strong> in case of failures some issue trackers will fall back to fetching details via the OpenGraph protocol. In that case the result will include field named `from_open_graph`."
msgstr "crwdns1380:0crwdne1380:0"

#: tcms/testcases/admin.py:192
msgid "Failed creating Issue Tracker"
msgstr "crwdns1388:0crwdne1388:0"

#: tcms/testcases/admin.py:201
msgid "Details extracted via OpenGraph. Issue Tracker may still be configured incorrectly!"
msgstr "crwdns1491:0crwdne1491:0"

#: tcms/testcases/admin.py:210
msgid "Details extracted via API. Issue Tracker configuration looks good!"
msgstr "crwdns1493:0crwdne1493:0"

#: tcms/testcases/admin.py:223
msgid "Issue Tracker configuration check failed"
msgstr "crwdns1392:0crwdne1392:0"

#: tcms/testcases/helpers/email.py:22
#, python-format
msgid "DELETED: TestCase #%(pk)d - %(summary)s"
msgstr "crwdns936:0%(pk)dcrwdnd936:0%(summary)scrwdne936:0"

#: tcms/testcases/models.py:23
msgid "Test case status"
msgstr "crwdns1266:0crwdne1266:0"

#: tcms/testcases/models.py:24
msgid "Test case statuses"
msgstr "crwdns1268:0crwdne1268:0"

#: tcms/testcases/models.py:379
#: tcms/testcases/templates/testcases/mutable.html:107
msgid "Template"
msgstr "crwdns1395:0crwdne1395:0"

#: tcms/testcases/models.py:380
msgid "Templates"
msgstr "crwdns1397:0crwdne1397:0"

#: tcms/testcases/templates/testcases/clone.html:5
msgid "Clone TestCase"
msgstr "crwdns990:0crwdne990:0"

#: tcms/testcases/templates/testcases/clone.html:15
msgid "Add new TC into TP"
msgstr "crwdns992:0crwdne992:0"

#: tcms/testcases/templates/testcases/clone.html:30
msgid "Selected TC"
msgstr "crwdns994:0crwdne994:0"

#: tcms/testcases/templates/testcases/clone.html:45 tcms/testcases/views.py:134
#: tcms/testplans/templates/testplans/clone.html:73
#: tcms/testplans/templates/testplans/get.html:138
#: tcms/testplans/templates/testplans/get.html:358 tcms/testplans/views.py:144
#: tcms/testruns/views.py:198
msgid "Clone"
msgstr "crwdns512:0crwdne512:0"

#: tcms/testcases/templates/testcases/get.html:28
#: tcms/testcases/templates/testcases/get.html:168
#: tcms/testcases/templates/testcases/mutable.html:184
#: tcms/testcases/templates/testcases/search.html:122
#: tcms/testcases/templates/testcases/search.html:169
#: tcms/testplans/templates/testplans/get.html:54
#: tcms/testplans/templates/testplans/get.html:412
#: tcms/testplans/templates/testplans/get.html:492
#: tcms/testplans/templates/testplans/mutable.html:121
#: tcms/testplans/templates/testplans/search.html:82
#: tcms/testplans/templates/testplans/search.html:120
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:8
#: tcms/testruns/templates/testruns/mutable.html:230
msgid "Author"
msgstr "crwdns385:0crwdne385:0"

#: tcms/testcases/templates/testcases/get.html:49
#: tcms/testcases/templates/testcases/mutable.html:58
#: tcms/testcases/templates/testcases/search.html:100
#: tcms/testcases/templates/testcases/search.html:164
#: tcms/testplans/templates/testplans/get.html:408
#: tcms/testplans/templates/testplans/get.html:491
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:7
#: tcms/testruns/templates/testruns/get.html:266
#: tcms/testruns/templates/testruns/get.html:367
#: tcms/testruns/templates/testruns/mutable.html:233
msgid "Category"
msgstr "crwdns412:0crwdne412:0"

#: tcms/testcases/templates/testcases/get.html:64
#: tcms/testcases/templates/testcases/mutable.html:86
#: tcms/testcases/templates/testcases/search.html:82
#: tcms/testcases/templates/testcases/search.html:166
#: tcms/testplans/templates/testplans/get.html:162
#: tcms/testplans/templates/testplans/get.html:371
#: tcms/testplans/templates/testplans/get.html:404
#: tcms/testplans/templates/testplans/get.html:490
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:6
#: tcms/testruns/templates/testruns/get.html:265
#: tcms/testruns/templates/testruns/get.html:363
#: tcms/testruns/templates/testruns/mutable.html:234
msgid "Priority"
msgstr "crwdns413:0crwdne413:0"

#: tcms/testcases/templates/testcases/get.html:73
#: tcms/testcases/templates/testcases/mutable.html:120
msgid "Setup duration"
msgstr "crwdns1334:0crwdne1334:0"

#: tcms/testcases/templates/testcases/get.html:78
#: tcms/testcases/templates/testcases/mutable.html:127
msgid "Testing duration"
msgstr "crwdns1336:0crwdne1336:0"

#: tcms/testcases/templates/testcases/get.html:83
msgid "Expected duration"
msgstr "crwdns1338:0crwdne1338:0"

#: tcms/testcases/templates/testcases/get.html:98
#: tcms/testcases/templates/testcases/mutable.html:143
msgid "Script"
msgstr "crwdns482:0crwdne482:0"

#: tcms/testcases/templates/testcases/get.html:103
#: tcms/testcases/templates/testcases/mutable.html:149
msgid "Arguments"
msgstr "crwdns484:0crwdne484:0"

#: tcms/testcases/templates/testcases/get.html:108
#: tcms/testcases/templates/testcases/mutable.html:157
msgid "Requirements"
msgstr "crwdns486:0crwdne486:0"

#: tcms/testcases/templates/testcases/get.html:113
#: tcms/testcases/templates/testcases/mutable.html:162
#: tcms/testplans/templates/testplans/get.html:80
#: tcms/testplans/templates/testplans/mutable.html:102
msgid "Reference link"
msgstr "crwdns458:0crwdne458:0"

#: tcms/testcases/templates/testcases/get.html:133
#: tcms/testcases/templates/testcases/mutable.html:170
#: tcms/testplans/templates/testplans/get.html:433
#: tcms/testplans/templates/testplans/get.html:434
#: tcms/testruns/templates/testruns/get.html:400
#: tcms/testruns/templates/testruns/mutable.html:202
msgid "Notes"
msgstr "crwdns406:0crwdne406:0"

#: tcms/testcases/templates/testcases/get.html:152
msgid "Bugs"
msgstr "crwdns488:0crwdne488:0"

#: tcms/testcases/templates/testcases/get.html:159
msgid "Test plans"
msgstr "crwdns492:0crwdne492:0"

#: tcms/testcases/templates/testcases/mutable.html:10
msgid "Edit TestCase"
msgstr "crwdns1110:0crwdne1110:0"

#: tcms/testcases/templates/testcases/mutable.html:59
msgid "add new Category"
msgstr "crwdns524:0crwdne524:0"

#: tcms/testcases/templates/testcases/mutable.html:108
msgid "add new Template"
msgstr "crwdns1469:0crwdne1469:0"

#: tcms/testcases/templates/testcases/mutable.html:180
#: tcms/testruns/templates/testruns/get.html:517
msgid "Notify"
msgstr "crwdns894:0crwdne894:0"

#: tcms/testcases/templates/testcases/mutable.html:191
msgid "Manager of runs"
msgstr "crwdns896:0crwdne896:0"

#: tcms/testcases/templates/testcases/mutable.html:198
msgid "Asignees"
msgstr "crwdns898:0crwdne898:0"

#: tcms/testcases/templates/testcases/mutable.html:214
msgid "Default tester of runs"
msgstr "crwdns900:0crwdne900:0"

#: tcms/testcases/templates/testcases/mutable.html:223
msgid "Notify when"
msgstr "crwdns902:0crwdne902:0"

#: tcms/testcases/templates/testcases/mutable.html:224
#: tcms/testplans/templates/testplans/mutable.html:142
msgid "applies only for changes made by somebody else"
msgstr "crwdns1489:0crwdne1489:0"

#: tcms/testcases/templates/testcases/mutable.html:229
msgid "TestCase is updated"
msgstr "crwdns904:0crwdne904:0"

#: tcms/testcases/templates/testcases/mutable.html:236
msgid "TestCase is deleted"
msgstr "crwdns906:0crwdne906:0"

#: tcms/testcases/templates/testcases/mutable.html:245
msgid "CC to"
msgstr "crwdns908:0crwdne908:0"

#: tcms/testcases/templates/testcases/mutable.html:250
msgid "Email addresses separated by comma. A notification email will be sent to each Email address within CC list."
msgstr "crwdns910:0crwdne910:0"

#: tcms/testcases/templates/testcases/search.html:15
msgid "Test case summary"
msgstr "crwdns848:0crwdne848:0"

#: tcms/testcases/templates/testcases/search.html:45
msgid "Both"
msgstr "crwdns532:0crwdne532:0"

#: tcms/testcases/templates/testcases/search.html:61
msgid "include in search request"
msgstr "crwdns1543:0crwdne1543:0"

#: tcms/testcases/templates/testcases/search.html:74
msgid "include child test plans"
msgstr "crwdns1545:0crwdne1545:0"

#: tcms/testcases/templates/testcases/search.html:112
#: tcms/testcases/templates/testcases/search.html:165
#: tcms/testplans/templates/testplans/get.html:216
#: tcms/testruns/templates/testruns/get.html:262
msgid "Component"
msgstr "crwdns427:0crwdne427:0"

#: tcms/testcases/templates/testcases/search.html:128
msgid "Text"
msgstr "crwdns526:0crwdne526:0"

#: tcms/testcases/templates/testcases/search.html:139
#: tcms/testplans/templates/testplans/search.html:97
#: tcms/testruns/templates/testruns/search.html:30
msgid "Separate multiple values with comma (,)"
msgstr "crwdns389:0crwdne389:0"

#: tcms/testcases/templates/testcases/search.html:178
msgid "Select"
msgstr "crwdns1242:0crwdne1242:0"

#: tcms/testcases/views.py:138 tcms/testplans/views.py:146
#: tcms/testruns/templates/testruns/get.html:458 tcms/testruns/views.py:202
msgid "History"
msgstr "crwdns514:0crwdne514:0"

#: tcms/testcases/views.py:250
msgid "TestCase cloning was successful"
msgstr "crwdns304:0crwdne304:0"

#: tcms/testcases/views.py:281
msgid "At least one TestCase is required"
msgstr "crwdns303:0crwdne303:0"

#: tcms/testplans/templates/testplans/clone.html:5
msgid "Clone TestPlan"
msgstr "crwdns664:0crwdne664:0"

#: tcms/testplans/templates/testplans/clone.html:55
msgid "Clone TCs"
msgstr "crwdns982:0crwdne982:0"

#: tcms/testplans/templates/testplans/clone.html:59
msgid "Clone or link existing TCs into new TP"
msgstr "crwdns984:0crwdne984:0"

#: tcms/testplans/templates/testplans/clone.html:63
msgid "Parent TP"
msgstr "crwdns986:0crwdne986:0"

#: tcms/testplans/templates/testplans/clone.html:67
msgid "Set the source TP as parent of new TP"
msgstr "crwdns988:0crwdne988:0"

#: tcms/testplans/templates/testplans/get.html:25
#: tcms/testruns/templates/testruns/get.html:23
msgid "Enter username, email or user ID:"
msgstr "crwdns1244:0crwdne1244:0"

#: tcms/testplans/templates/testplans/get.html:26
#: tcms/testruns/templates/testruns/get.html:22
msgid "No rows selected! Please select at least one!"
msgstr "crwdns1246:0crwdne1246:0"

#: tcms/testplans/templates/testplans/get.html:27
#: tcms/testruns/templates/testruns/get.html:24
msgid "Are you sure?"
msgstr "crwdns1248:0crwdne1248:0"

#: tcms/testplans/templates/testplans/get.html:28
msgid "Cannot create TestRun with unconfirmed test cases"
msgstr "crwdns1250:0crwdne1250:0"

#: tcms/testplans/templates/testplans/get.html:29
msgid "Error adding test cases"
msgstr "crwdns1270:0crwdne1270:0"

#: tcms/testplans/templates/testplans/get.html:43
msgid "Show more"
msgstr "crwdns888:0crwdne888:0"

#: tcms/testplans/templates/testplans/get.html:75
msgid "Plan Type"
msgstr "crwdns562:0crwdne562:0"

#: tcms/testplans/templates/testplans/get.html:100
msgid "Test cases"
msgstr "crwdns1252:0crwdne1252:0"

#: tcms/testplans/templates/testplans/get.html:182
#: tcms/testplans/templates/testplans/get.html:421
#: tcms/testplans/templates/testplans/get.html:494
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:10
msgid "Reviewer"
msgstr "crwdns712:0crwdne712:0"

#: tcms/testplans/templates/testplans/get.html:231
#: tcms/testplans/templates/testplans/get.html:234
msgid "Sort key"
msgstr "crwdns1254:0crwdne1254:0"

#: tcms/testplans/templates/testplans/get.html:244
msgid "Re-order cases"
msgstr "crwdns700:0crwdne700:0"

#: tcms/testplans/templates/testplans/get.html:254
#: tcms/testruns/templates/testruns/get.html:288
msgid "Search and add test cases"
msgstr "crwdns1256:0crwdne1256:0"

#: tcms/testplans/templates/testplans/get.html:263
#: tcms/testruns/templates/testruns/get.html:298
msgid "Advanced search"
msgstr "crwdns1258:0crwdne1258:0"

#: tcms/testplans/templates/testplans/get.html:286
msgid "Active test runs"
msgstr "crwdns1296:0crwdne1296:0"

#: tcms/testplans/templates/testplans/get.html:308
msgid "More"
msgstr "crwdns1298:0crwdne1298:0"

#: tcms/testplans/templates/testplans/get.html:310
msgid "Inactive"
msgstr "crwdns1300:0crwdne1300:0"

#: tcms/testplans/templates/testplans/get.html:444
#: tcms/testruns/templates/testruns/get.html:494
msgid "No attachments"
msgstr "crwdns692:0crwdne692:0"

#: tcms/testplans/templates/testplans/get.html:469
#: tcms/testruns/templates/testruns/get.html:483
msgid "Comments"
msgstr "crwdns614:0crwdne614:0"

#: tcms/testplans/templates/testplans/mutable.html:10
msgid "Edit TestPlan"
msgstr "crwdns518:0crwdne518:0"

#: tcms/testplans/templates/testplans/mutable.html:12
msgid "Create new TestPlan"
msgstr "crwdns450:0crwdne450:0"

#: tcms/testplans/templates/testplans/mutable.html:82
msgid "Parent ID"
msgstr "crwdns456:0crwdne456:0"

#: tcms/testplans/templates/testplans/mutable.html:100
msgid "Enter to assign; Backspace + Enter to clear"
msgstr "crwdns1532:0crwdne1532:0"

#: tcms/testplans/templates/testplans/mutable.html:110
msgid "Test plan document:"
msgstr "crwdns460:0crwdne460:0"

#: tcms/testplans/templates/testplans/mutable.html:119
msgid "Notify:"
msgstr "crwdns464:0crwdne464:0"

#: tcms/testplans/templates/testplans/mutable.html:127
msgid "TestCase author"
msgstr "crwdns466:0crwdne466:0"

#: tcms/testplans/templates/testplans/mutable.html:141
msgid "Notify when:"
msgstr "crwdns468:0crwdne468:0"

#: tcms/testplans/templates/testplans/mutable.html:145
msgid "TestPlan is updated"
msgstr "crwdns470:0crwdne470:0"

#: tcms/testplans/templates/testplans/mutable.html:151
msgid "Test cases are updated"
msgstr "crwdns472:0crwdne472:0"

#: tcms/testplans/templates/testplans/mutable.html:165
#: tcms/testplans/templates/testplans/search.html:41
msgid "Active"
msgstr "crwdns390:0crwdne390:0"

#: tcms/testplans/templates/testplans/search.html:12
msgid "Some child test plans do not match search criteria"
msgstr "crwdns1518:0crwdne1518:0"

#: tcms/testplans/templates/testplans/search.html:20
msgid "Test plan name"
msgstr "crwdns381:0crwdne381:0"

#: tcms/testruns/admin.py:32
msgid "Permission denied: TestRun does not belong to you"
msgstr "crwdns311:0crwdne311:0"

#: tcms/testruns/admin.py:39
msgid "For more information about customizing test execution statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-execution-statuses\">\n"
"        the documentation</a>!"
msgstr "crwdns1094:0crwdne1094:0"

#: tcms/testruns/admin.py:95
msgid "1 negative, 1 neutral & 1 positive status required!"
msgstr "crwdns1260:0crwdne1260:0"

#: tcms/testruns/admin.py:106
msgid "Edit parameters"
msgstr "crwdns1448:0crwdne1448:0"

#: tcms/testruns/forms.py:39
msgid "Full"
msgstr "crwdns1409:0crwdne1409:0"

#: tcms/testruns/forms.py:40
msgid "Pairwise"
msgstr "crwdns1411:0crwdne1411:0"

#: tcms/testruns/models.py:230
msgid "Test execution statuses"
msgstr "crwdns1087:0crwdne1087:0"

#: tcms/testruns/templates/testruns/get.html:25
msgid "Unconfirmed test cases were not added"
msgstr "crwdns1272:0crwdne1272:0"

#: tcms/testruns/templates/testruns/get.html:26
msgid "Type 0 or 1"
msgstr "crwdns1274:0crwdne1274:0"

#: tcms/testruns/templates/testruns/get.html:27
msgid "Comment"
msgstr "crwdns229:0crwdne229:0"

#: tcms/testruns/templates/testruns/get.html:60
#: tcms/testruns/templates/testruns/mutable.html:29
#: tcms/testruns/templates/testruns/search.html:67
#: tcms/testruns/templates/testruns/search.html:188
msgid "Manager"
msgstr "crwdns403:0crwdne403:0"

#: tcms/testruns/templates/testruns/get.html:73
#: tcms/testruns/templates/testruns/mutable.html:110
#: tcms/testruns/templates/testruns/search.html:127
msgid "Planned start"
msgstr "crwdns1340:0crwdne1340:0"

#: tcms/testruns/templates/testruns/get.html:84
msgid "Start"
msgstr "crwdns1342:0crwdne1342:0"

#: tcms/testruns/templates/testruns/get.html:91
#: tcms/testruns/templates/testruns/mutable.html:123
#: tcms/testruns/templates/testruns/search.html:147
msgid "Planned stop"
msgstr "crwdns1344:0crwdne1344:0"

#: tcms/testruns/templates/testruns/get.html:102
msgid "Stop"
msgstr "crwdns1346:0crwdne1346:0"

#: tcms/testruns/templates/testruns/get.html:139
#: tcms/testruns/templates/testruns/mutable.html:160
msgid "Environment"
msgstr "crwdns1421:0crwdne1421:0"

#: tcms/testruns/templates/testruns/get.html:182
msgid "Update text version"
msgstr "crwdns1276:0crwdne1276:0"

#: tcms/testruns/templates/testruns/get.html:217
msgid "Add comment"
msgstr "crwdns1278:0crwdne1278:0"

#: tcms/testruns/templates/testruns/get.html:228
#: tcms/testruns/templates/testruns/get.html:331
#: tcms/testruns/templates/testruns/get.html:568
msgid "Add hyperlink"
msgstr "crwdns1280:0crwdne1280:0"

#: tcms/testruns/templates/testruns/get.html:279
msgid "Mine"
msgstr "crwdns1436:0crwdne1436:0"

#: tcms/testruns/templates/testruns/get.html:280
msgid "All"
msgstr "crwdns1438:0crwdne1438:0"

#: tcms/testruns/templates/testruns/get.html:306
msgid "records"
msgstr "crwdns1282:0crwdne1282:0"

#: tcms/testruns/templates/testruns/get.html:332
#: tcms/testruns/templates/testruns/get.html:607
#: tcms/testruns/templates/testruns/get.html:624
msgid "Report bug"
msgstr "crwdns1056:0crwdne1056:0"

#: tcms/testruns/templates/testruns/get.html:339
msgid "Test case is not part of parent test plan"
msgstr "crwdns1284:0crwdne1284:0"

#: tcms/testruns/templates/testruns/get.html:371
msgid "Assigned to"
msgstr "crwdns1286:0crwdne1286:0"

#: tcms/testruns/templates/testruns/get.html:379
#: tcms/testruns/templates/testruns/get.html:381
msgid "Last bug"
msgstr "crwdns1548:0crwdne1548:0"

#: tcms/testruns/templates/testruns/get.html:423
msgid "Text version"
msgstr "crwdns1288:0crwdne1288:0"

#: tcms/testruns/templates/testruns/get.html:431
msgid "Bugs and hyperlinks"
msgstr "crwdns1054:0crwdne1054:0"

#: tcms/testruns/templates/testruns/get.html:585
msgid "Is a defect"
msgstr "crwdns1292:0crwdne1292:0"

#: tcms/testruns/templates/testruns/get.html:592
#: tcms/testruns/templates/testruns/get.html:623
msgid "Cancel"
msgstr "crwdns804:0crwdne804:0"

#: tcms/testruns/templates/testruns/get.html:612
msgid "Issue Tracker"
msgstr "crwdns1294:0crwdne1294:0"

#: tcms/testruns/templates/testruns/mutable.html:7
msgid "Edit TestRun"
msgstr "crwdns396:0crwdne396:0"

#: tcms/testruns/templates/testruns/mutable.html:9
msgid "Clone TestRun"
msgstr "crwdns397:0crwdne397:0"

#: tcms/testruns/templates/testruns/mutable.html:180
msgid "Affects only test cases with parameters"
msgstr "crwdns1450:0crwdne1450:0"

#: tcms/testruns/templates/testruns/mutable.html:194
msgid "more information"
msgstr "crwdns1440:0crwdne1440:0"

#: tcms/testruns/templates/testruns/mutable.html:218
msgid "Selected TestCase(s):"
msgstr "crwdns408:0crwdne408:0"

#: tcms/testruns/templates/testruns/mutable.html:221
#, python-format
msgid "%(count)s of the pre-selected test cases is not CONFIRMED and will not be cloned!\n"
"See test plan for more details!"
msgstr "crwdns1014:0%(count)scrwdne1014:0"

#: tcms/testruns/templates/testruns/search.html:21
msgid "Plan ID"
msgstr "crwdns432:0crwdne432:0"

#: tcms/testruns/templates/testruns/search.html:23
msgid "TestPlan ID"
msgstr "crwdns433:0crwdne433:0"

#: tcms/testruns/templates/testruns/search.html:79
msgid "Running"
msgstr "crwdns434:0crwdne434:0"

#: tcms/testruns/templates/testruns/search.html:86
#: tcms/testruns/templates/testruns/search.html:186
msgid "Start date"
msgstr "crwdns1348:0crwdne1348:0"

#: tcms/testruns/templates/testruns/search.html:106
#: tcms/testruns/templates/testruns/search.html:187
msgid "Stop date"
msgstr "crwdns1350:0crwdne1350:0"

#: tcms/testruns/views.py:273
msgid "Clone of "
msgstr "crwdns415:0crwdne415:0"

#: testcases.TestCaseStatus/name:1
msgid "PROPOSED"
msgstr "crwdns283:0crwdne283:0"

#: testcases.TestCaseStatus/name:3
msgid "DISABLED"
msgstr "crwdns285:0crwdne285:0"

#: testcases.TestCaseStatus/name:4
msgid "NEED_UPDATE"
msgstr "crwdns286:0crwdne286:0"

#: testruns.TestExecutionStatus/name:1
msgid "IDLE"
msgstr "crwdns275:0crwdne275:0"

#: testruns.TestExecutionStatus/name:2
msgid "RUNNING"
msgstr "crwdns276:0crwdne276:0"

#: testruns.TestExecutionStatus/name:3
msgid "PAUSED"
msgstr "crwdns277:0crwdne277:0"

#: testruns.TestExecutionStatus/name:4
msgid "PASSED"
msgstr "crwdns278:0crwdne278:0"

#: testruns.TestExecutionStatus/name:5
msgid "FAILED"
msgstr "crwdns279:0crwdne279:0"

#: testruns.TestExecutionStatus/name:6
msgid "BLOCKED"
msgstr "crwdns280:0crwdne280:0"

#: testruns.TestExecutionStatus/name:7
msgid "ERROR"
msgstr "crwdns281:0crwdne281:0"

#: testruns.TestExecutionStatus/name:8
msgid "WAIVED"
msgstr "crwdns282:0crwdne282:0"

#: tcms_github_app/admin.py:122
#, python-format
msgid "For additional configuration see\n"
"<a href=\"%s\">GitHub</a>"
msgstr "crwdns1230:0%scrwdne1230:0"

#: tcms_github_app/menu.py:11
msgid "GitHub integration"
msgstr "crwdns1116:0crwdne1116:0"

#: tcms_github_app/menu.py:12
msgid "Resync"
msgstr "crwdns1222:0crwdne1222:0"

#: tcms_github_app/menu.py:13
msgid "Settings"
msgstr "crwdns1118:0crwdne1118:0"

#: tcms_github_app/middleware.py:41
#, python-format
msgid "Unconfigured GitHub App %d"
msgstr "crwdns1120:0%dcrwdne1120:0"

#: tcms_github_app/utils.py:274
#, python-format
msgid "%s was imported from GitHub"
msgstr "crwdns1224:0%scrwdne1224:0"

#: tcms_github_app/utils.py:278
#, python-format
msgid "%s already exists"
msgstr "crwdns1226:0%scrwdne1226:0"

#: tcms_github_app/views.py:48
#, python-format
msgid "You have not logged-in via GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "crwdns1122:0%scrwdne1122:0"

#: tcms_github_app/views.py:62
#, python-format
msgid "You have not installed Kiwi TCMS into your GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "crwdns1124:0%scrwdne1124:0"

#: tcms_github_app/views.py:76
msgid "Multiple GitHub App installations detected! See below:"
msgstr "crwdns1126:0crwdne1126:0"

#: tcms_github_app/views.py:85
#, python-format
msgid "Edit GitHub App <a href=\"%s\">%s</a>"
msgstr "crwdns1128:0%scrwdnd1128:0%scrwdne1128:0"

#: tcms_github_app/views.py:102
#, python-format
msgid "Cannot find GitHub App installation for tenant \"%s\""
msgstr "crwdns1238:0%scrwdne1238:0"

#: tcms_github_app/views.py:111
msgid "Multiple GitHub App installations detected!"
msgstr "crwdns1228:0crwdne1228:0"

#: tcms_github_marketplace/menu.py:11
msgid "Subscriptions"
msgstr "crwdns1130:0crwdne1130:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/email/exit_poll.txt:1
msgid "Thank you for using Kiwi TCMS via a paid subscription.\n"
"We're sorry to see you go but we'd like to know why so we can improve in the future!\n\n"
"You can share your feedback at https://forms.gle/RJhFengbjN9C6wtUA\n\n"
"Thank you!"
msgstr "crwdns1520:0crwdne1520:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:6
msgid "Tenant subscriptions"
msgstr "crwdns1132:0crwdne1132:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:15
msgid "You can access the following tenants"
msgstr "crwdns1134:0crwdne1134:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:20
msgid "Tenant"
msgstr "crwdns1136:0crwdne1136:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:31
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:90
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:30
msgid "Organization"
msgstr "crwdns1138:0crwdne1138:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:44
msgid "Docker credentials"
msgstr "crwdns1424:0crwdne1424:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:61
msgid "Private containers instructions"
msgstr "crwdns1426:0crwdne1426:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:75
msgid "You own the following tenants"
msgstr "crwdns1140:0crwdne1140:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:97
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:98
msgid "Price"
msgstr "crwdns1142:0crwdne1142:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:102
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:103
msgid "Subscription type"
msgstr "crwdns1144:0crwdne1144:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:107
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:108
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:8
msgid "Paid until"
msgstr "crwdns1146:0crwdne1146:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:114
msgid "Cancel subscription"
msgstr "crwdns1148:0crwdne1148:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:127
msgid "You don't own any tenants"
msgstr "crwdns1150:0crwdne1150:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:131
msgid "Subscribe via FastSpring"
msgstr "crwdns1326:0crwdne1326:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:147
msgid "Transaction history"
msgstr "crwdns1154:0crwdne1154:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:169
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:170
msgid "Sender"
msgstr "crwdns1156:0crwdne1156:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:174
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:175
msgid "Vendor"
msgstr "crwdns1158:0crwdne1158:0"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:179
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:180
msgid "Received on"
msgstr "crwdns1160:0crwdne1160:0"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:4
msgid "Extra emails"
msgstr "crwdns1522:0crwdne1522:0"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:14
msgid "Kiwi TCMS will try to match recurring billing events against tenant.owner.email + tenant.extra_emails"
msgstr "crwdns1524:0crwdne1524:0"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:17
msgid "Separate by comma (,), semi-colon (;) or white space ( )"
msgstr "crwdns1526:0crwdne1526:0"

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:44
msgid "Private Tenant Warning"
msgstr "crwdns1162:0crwdne1162:0"

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:49
msgid "You are about to create a Private Tenant for Kiwi TCMS.\n"
"It will take a few minutes until your DB schema is ready!\n"
"After clicking the 'Save' button <strong>do not</strong> close or refresh this page!<br>\n"
"You will be redirected to your new tenant when the creation process is complete!\n"
"If you see a 500 Internal Server Error page please contact\n"
"<a href=\"mailto:<EMAIL>\"><EMAIL></a> immediately!"
msgstr "crwdns1196:0crwdne1196:0"

#: tcms_github_marketplace/utils.py:95
msgid "Kiwi TCMS Subscription Exit Poll"
msgstr "crwdns1528:0crwdne1528:0"

#: tcms_github_marketplace/views.py:567
msgid "Kiwi TCMS subscription notification"
msgstr "crwdns1530:0crwdne1530:0"

#: tcms_github_marketplace/views.py:749
msgid "mo"
msgstr "crwdns1166:0crwdne1166:0"

#: tcms_github_marketplace/views.py:752
msgid "yr"
msgstr "crwdns1168:0crwdne1168:0"

#: tcms_enterprise/pipeline.py:17
msgid "Email address is required"
msgstr "crwdns1170:0crwdne1170:0"

#: tcms_enterprise/templates/registration/custom_login.html:10
msgid "or Continue With"
msgstr "crwdns1208:0crwdne1208:0"

#: tcms_settings_dir/enterprise.py:19
msgid "Legal information"
msgstr "crwdns1210:0crwdne1210:0"

#: tcms_tenants/admin.py:55 tcms_tenants/admin.py:62
#: tcms_tenants/middleware.py:35
msgid "Unauthorized"
msgstr "crwdns1172:0crwdne1172:0"

#: tcms_tenants/admin.py:86
msgid "Existing username, email or user ID"
msgstr "crwdns1302:0crwdne1302:0"

#: tcms_tenants/admin.py:159
msgid "Full name"
msgstr "crwdns1174:0crwdne1174:0"

#: tcms_tenants/forms.py:30
msgid "Invalid string"
msgstr "crwdns1202:0crwdne1202:0"

#: tcms_tenants/menu.py:15
msgid "Create"
msgstr "crwdns1176:0crwdne1176:0"

#: tcms_tenants/menu.py:20
#: tcms_tenants/templates/tcms_tenants/invite_users.html:17
msgid "Invite users"
msgstr "crwdns1304:0crwdne1304:0"

#: tcms_tenants/menu.py:21
msgid "Authorized users"
msgstr "crwdns1178:0crwdne1178:0"

#: tcms_tenants/middleware.py:59
msgid "Unpaid"
msgstr "crwdns1180:0crwdne1180:0"

#: tcms_tenants/middleware.py:70
msgid "Tenant expires soon"
msgstr "crwdns1541:0crwdne1541:0"

#: tcms_tenants/templates/tcms_tenants/email/invite_user.txt:1
#, python-format
msgid "Dear tester,\n"
"%(invited_by)s has invited you to join their Kiwi TCMS tenant at\n"
"%(tenant_url)s\n\n"
"In case you have never logged in before an account was created for you\n"
"automatically. You can login with a social account which has the same email\n"
"address or go to %(password_reset_url)s to reset your password.\n"
"The password reset email message also contains your username!"
msgstr "crwdns1534:0%(invited_by)scrwdnd1534:0%(tenant_url)scrwdnd1534:0%(password_reset_url)scrwdne1534:0"

#: tcms_tenants/templates/tcms_tenants/email/new.txt:1
#, python-format
msgid "Your Kiwi TCMS tenant was created at:\n"
"%(tenant_url)s\n\n"
"If you have troubles please contact support!"
msgstr "crwdns1198:0%(tenant_url)scrwdne1198:0"

#: tcms_tenants/templates/tcms_tenants/invite_users.html:28
msgid "Email"
msgstr "crwdns1308:0crwdne1308:0"

#: tcms_tenants/templates/tcms_tenants/new.html:18
msgid "New tenant"
msgstr "crwdns1186:0crwdne1186:0"

#: tcms_tenants/templates/tcms_tenants/new.html:35
msgid "Company, team or project name"
msgstr "crwdns1188:0crwdne1188:0"

#: tcms_tenants/templates/tcms_tenants/new.html:43
msgid "Schema"
msgstr "crwdns1190:0crwdne1190:0"

#: tcms_tenants/templates/tcms_tenants/new.html:56
msgid "Validation pattern"
msgstr "crwdns1204:0crwdne1204:0"

#: tcms_tenants/templates/tcms_tenants/new.html:61
msgid "Publicly readable"
msgstr "crwdns1356:0crwdne1356:0"

#: tcms_tenants/templates/tcms_tenants/new.html:80
msgid "Tenant logo"
msgstr "crwdns1453:0crwdne1453:0"

#: tcms_tenants/utils.py:66
msgid "Schema name already in use"
msgstr "crwdns1192:0crwdne1192:0"

#: tcms_tenants/utils.py:170
msgid "New Kiwi TCMS tenant created"
msgstr "crwdns1194:0crwdne1194:0"

#: tcms_tenants/utils.py:230
#, python-brace-format
msgid "User {user.username} added to tenant group {group.name}"
msgstr "crwdns1455:0{user.username}crwdnd1455:0{group.name}crwdne1455:0"

#: tcms_tenants/utils.py:262
msgid "Invitation to join Kiwi TCMS"
msgstr "crwdns1314:0crwdne1314:0"

#: tcms_tenants/views.py:84
msgid "Only super-user and tenant owner are allowed to edit tenant properties"
msgstr "crwdns1358:0crwdne1358:0"

#: tcms_tenants/views.py:102
msgid "Edit tenant"
msgstr "crwdns1360:0crwdne1360:0"

#: tcms_tenants/views.py:153
msgid "Only users who are authorized for this tenant can invite others"
msgstr "crwdns1312:0crwdne1312:0"

#: tenant_groups/admin.py:30
msgid "users"
msgstr "crwdns1457:0crwdne1457:0"

#: tenant_groups/models.py:34
msgid "name"
msgstr "crwdns1459:0crwdne1459:0"

#: tenant_groups/models.py:37
msgid "permissions"
msgstr "crwdns1461:0crwdne1461:0"

#: tenant_groups/models.py:47
msgid "group"
msgstr "crwdns1463:0crwdne1463:0"

#: tenant_groups/models.py:48
msgid "groups"
msgstr "crwdns1465:0crwdne1465:0"

#: trackers_integration/menu.py:4
msgid "Personal API tokens"
msgstr "crwdns1498:0crwdne1498:0"
