msgid ""
msgstr ""
"Project-Id-Version: kiwitcms\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-30 12:22+0000\n"
"PO-Revision-Date: 2025-07-30 13:13\n"
"Last-Translator: \n"
"Language-Team: Chinese Traditional\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: kiwitcms\n"
"X-Crowdin-Project-ID: 295734\n"
"X-Crowdin-Language: zh-TW\n"
"X-Crowdin-File: /master/tcms/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 19\n"

#: tcms/bugs/forms.py:33
msgid "Description of problem:\n\n\n"
"How often reproducible:\n\n\n"
"Steps to Reproduce:\n"
"1.\n"
"2.\n"
"3.\n\n"
"Actual results:\n\n\n"
"Expected results:\n\n\n"
"Additional info:"
msgstr "問題描述：\n\n\n"
"重現頻率：\n\n\n"
"重現步驟：\n"
"1、\n"
"2、\n"
"3、\n\n"
"實際結果：\n\n\n"
"預期結果：\n\n\n"
"額外訊息："

#: tcms/bugs/models.py:24 tcms/bugs/templates/bugs/get.html:50
#: tcms/bugs/templates/bugs/mutable.html:27
#: tcms/bugs/templates/bugs/search.html:18
#: tcms/bugs/templates/bugs/search.html:108
msgid "Severity"
msgstr "嚴重性"

#: tcms/bugs/templates/bugs/get.html:37 tcms/bugs/templates/bugs/search.html:89
#: tcms/issuetracker/kiwitcms.py:49 tcms/templates/include/bug_details.html:3
msgid "Open"
msgstr "打開"

#: tcms/bugs/templates/bugs/get.html:39 tcms/issuetracker/kiwitcms.py:49
#: tcms/templates/include/bug_details.html:3
msgid "Closed"
msgstr "已關閉"

#: tcms/bugs/templates/bugs/get.html:59 tcms/bugs/templates/bugs/search.html:79
#: tcms/bugs/templates/bugs/search.html:114
#: tcms/templates/include/bug_details.html:7
msgid "Reporter"
msgstr "回報者"

#: tcms/bugs/templates/bugs/get.html:64
#: tcms/bugs/templates/bugs/mutable.html:94
#: tcms/bugs/templates/bugs/search.html:84
#: tcms/bugs/templates/bugs/search.html:115
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:36
#: tcms/templates/include/bug_details.html:10
#: tcms/testruns/templates/testruns/get.html:207
#: tcms/testruns/templates/testruns/get.html:267
msgid "Assignee"
msgstr "負責執行者"

#: tcms/bugs/templates/bugs/get.html:75
#: tcms/bugs/templates/bugs/mutable.html:45
#: tcms/bugs/templates/bugs/search.html:47
#: tcms/bugs/templates/bugs/search.html:111
#: tcms/core/templates/dashboard.html:53 tcms/management/admin.py:107
#: tcms/telemetry/templates/telemetry/include/filters.html:8
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:29
#: tcms/templates/include/bug_details.html:13
#: tcms/testcases/templates/testcases/get.html:44
#: tcms/testcases/templates/testcases/get.html:170
#: tcms/testcases/templates/testcases/mutable.html:43
#: tcms/testcases/templates/testcases/search.html:51
#: tcms/testplans/templates/testplans/clone.html:21
#: tcms/testplans/templates/testplans/get.html:64
#: tcms/testplans/templates/testplans/mutable.html:33
#: tcms/testplans/templates/testplans/search.html:50
#: tcms/testplans/templates/testplans/search.html:117
#: tcms/testruns/templates/testruns/get.html:45
#: tcms/testruns/templates/testruns/mutable.html:46
#: tcms/testruns/templates/testruns/search.html:35
#: tcms/testruns/templates/testruns/search.html:183
msgid "Product"
msgstr "產品"

#: tcms/bugs/templates/bugs/get.html:80
#: tcms/bugs/templates/bugs/mutable.html:60
#: tcms/bugs/templates/bugs/search.html:57
#: tcms/bugs/templates/bugs/search.html:112
#: tcms/telemetry/templates/telemetry/include/filters.html:17
#: tcms/templates/include/bug_details.html:15 tcms/templates/navbar.html:71
#: tcms/testplans/templates/testplans/clone.html:36
#: tcms/testplans/templates/testplans/get.html:70
#: tcms/testplans/templates/testplans/mutable.html:48
#: tcms/testplans/templates/testplans/search.html:60
#: tcms/testplans/templates/testplans/search.html:118
#: tcms/testruns/templates/testruns/get.html:50
#: tcms/testruns/templates/testruns/search.html:45
#: tcms/testruns/templates/testruns/search.html:184
msgid "Version"
msgstr "版本"

#: tcms/bugs/templates/bugs/get.html:85
#: tcms/bugs/templates/bugs/mutable.html:78
#: tcms/bugs/templates/bugs/search.html:67
#: tcms/bugs/templates/bugs/search.html:113 tcms/management/models.py:132
#: tcms/telemetry/templates/telemetry/include/filters.html:25
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:30
#: tcms/templates/include/bug_details.html:17
#: tcms/testruns/templates/testruns/get.html:55
#: tcms/testruns/templates/testruns/get.html:414
#: tcms/testruns/templates/testruns/mutable.html:89
#: tcms/testruns/templates/testruns/search.html:55
#: tcms/testruns/templates/testruns/search.html:185
msgid "Build"
msgstr "建置"

#: tcms/bugs/templates/bugs/get.html:119
msgid "commented on"
msgstr "評論於"

#: tcms/bugs/templates/bugs/get.html:149
msgid "Reopen"
msgstr "重新打開"

#: tcms/bugs/templates/bugs/get.html:151
#: tcms/bugs/templates/bugs/mutable.html:114
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:78
#: tcms/testcases/templates/testcases/mutable.html:263
#: tcms/testplans/templates/testplans/get.html:477
#: tcms/testplans/templates/testplans/mutable.html:173
#: tcms/testruns/templates/testruns/get.html:480
#: tcms/testruns/templates/testruns/get.html:593
#: tcms/testruns/templates/testruns/mutable.html:210
msgid "Save"
msgstr "儲存"

#: tcms/bugs/templates/bugs/get.html:153
msgid "Close"
msgstr "關閉"

#: tcms/bugs/templates/bugs/mutable.html:20
#: tcms/bugs/templates/bugs/search.html:13
#: tcms/bugs/templates/bugs/search.html:109
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:27
#: tcms/testcases/templates/testcases/mutable.html:24
#: tcms/testcases/templates/testcases/search.html:13
#: tcms/testcases/templates/testcases/search.html:162
#: tcms/testplans/templates/testplans/get.html:212
#: tcms/testplans/templates/testplans/get.html:487
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:3
#: tcms/testruns/templates/testruns/get.html:251
#: tcms/testruns/templates/testruns/get.html:258
#: tcms/testruns/templates/testruns/get.html:261
#: tcms/testruns/templates/testruns/mutable.html:23
#: tcms/testruns/templates/testruns/mutable.html:229
#: tcms/testruns/templates/testruns/search.html:14
#: tcms/testruns/templates/testruns/search.html:181
msgid "Summary"
msgstr "概要"

#: tcms/bugs/templates/bugs/mutable.html:28
msgid "add new Severity"
msgstr "新增嚴重性"

#: tcms/bugs/templates/bugs/mutable.html:46
#: tcms/testcases/templates/testcases/mutable.html:44
#: tcms/testplans/templates/testplans/clone.html:22
#: tcms/testplans/templates/testplans/mutable.html:34
#: tcms/testruns/templates/testruns/mutable.html:48
msgid "add new Product"
msgstr "加入新產品"

#: tcms/bugs/templates/bugs/mutable.html:62
#: tcms/testplans/templates/testplans/clone.html:38
#: tcms/testplans/templates/testplans/mutable.html:50
msgid "add new Version"
msgstr "建立新版本"

#: tcms/bugs/templates/bugs/mutable.html:81
#: tcms/bugs/templates/bugs/mutable.html:82
#: tcms/testruns/templates/testruns/mutable.html:93
#: tcms/testruns/templates/testruns/mutable.html:94
msgid "add new Build"
msgstr "建立新建置"

#: tcms/bugs/templates/bugs/mutable.html:98
#: tcms/testruns/templates/testruns/mutable.html:32
#: tcms/testruns/templates/testruns/mutable.html:39
msgid "Username or email"
msgstr "帳號或 Email"

#: tcms/bugs/templates/bugs/search.html:5 tcms/settings/common.py:414
msgid "Search Bugs"
msgstr "搜尋bug"

#: tcms/bugs/templates/bugs/search.html:28
#: tcms/testcases/templates/testcases/search.html:19
#: tcms/testplans/templates/testplans/search.html:24
msgid "Created"
msgstr "建立於"

#: tcms/bugs/templates/bugs/search.html:31
#: tcms/telemetry/templates/telemetry/include/filters.html:53
#: tcms/testcases/templates/testcases/search.html:22
#: tcms/testplans/templates/testplans/search.html:27
#: tcms/testruns/templates/testruns/search.html:90
#: tcms/testruns/templates/testruns/search.html:110
#: tcms/testruns/templates/testruns/search.html:131
#: tcms/testruns/templates/testruns/search.html:151
msgid "Before"
msgstr "之前"

#: tcms/bugs/templates/bugs/search.html:37
#: tcms/telemetry/templates/telemetry/include/filters.html:43
#: tcms/testcases/templates/testcases/search.html:28
#: tcms/testplans/templates/testplans/search.html:33
#: tcms/testruns/templates/testruns/search.html:97
#: tcms/testruns/templates/testruns/search.html:117
#: tcms/testruns/templates/testruns/search.html:138
#: tcms/testruns/templates/testruns/search.html:158
msgid "After"
msgstr "之後"

#: tcms/bugs/templates/bugs/search.html:81
#: tcms/bugs/templates/bugs/search.html:86
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:45
#: tcms/templates/registration/login.html:17
#: tcms/templates/registration/registration_form.html:15
#: tcms/testcases/templates/testcases/search.html:124
#: tcms/testplans/templates/testplans/search.html:84
#: tcms/testplans/templates/testplans/search.html:90
#: tcms/testruns/templates/testruns/get.html:524
#: tcms/testruns/templates/testruns/search.html:69
#: tcms/testruns/templates/testruns/search.html:75
msgid "Username"
msgstr "使用者名稱"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:39
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "Yes"
msgstr "是"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:42
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "No"
msgstr "否"

#: tcms/bugs/templates/bugs/search.html:97
#: tcms/testcases/templates/testcases/search.html:150
#: tcms/testplans/templates/testplans/get.html:221
#: tcms/testplans/templates/testplans/search.html:103
#: tcms/testruns/templates/testruns/get.html:273
#: tcms/testruns/templates/testruns/search.html:170
msgid "Search"
msgstr "搜尋"

#: tcms/bugs/templates/bugs/search.html:107
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:26
#: tcms/testcases/templates/testcases/get.html:166
#: tcms/testcases/templates/testcases/search.html:144
#: tcms/testcases/templates/testcases/search.html:161
#: tcms/testplans/templates/testplans/search.html:114
#: tcms/testruns/templates/testruns/search.html:180
msgid "ID"
msgstr "ID"

#: tcms/bugs/templates/bugs/search.html:110
#: tcms/templates/include/bug_details.html:5
#: tcms/testcases/templates/testcases/search.html:163
#: tcms/testplans/templates/testplans/search.html:116
#: tcms/testruns/templates/testruns/mutable.html:231
msgid "Created on"
msgstr "建立於"

#: tcms/bugs/views.py:42 tcms/testcases/views.py:130
#: tcms/testplans/templates/testplans/get.html:380 tcms/testplans/views.py:143
#: tcms/testruns/views.py:194 tcms/testruns/views.py:308
msgid "Edit"
msgstr "編輯"

#: tcms/bugs/views.py:45 tcms/testcases/views.py:143
#: tcms/testplans/views.py:151 tcms/testruns/views.py:207
#: tcms/testruns/views.py:316
msgid "Object permissions"
msgstr "物件權限"

#: tcms/bugs/views.py:50
#: tcms/templates/include/comments_for_object_template.html:10
#: tcms/templates/include/properties_card.html:32
#: tcms/templates/include/properties_card.html:47 tcms/testcases/views.py:151
#: tcms/testplans/templates/testplans/get.html:193
#: tcms/testplans/templates/testplans/get.html:384 tcms/testplans/views.py:159
#: tcms/testruns/templates/testruns/get.html:238
#: tcms/testruns/templates/testruns/get.html:450 tcms/testruns/views.py:215
#: tcms/testruns/views.py:324
msgid "Delete"
msgstr "刪除"

#: tcms/bugs/views.py:68 tcms/settings/common.py:400
msgid "New Bug"
msgstr "新增Bug"

#: tcms/bugs/views.py:188
msgid "Edit bug"
msgstr "編輯Bug"

#: tcms/bugs/views.py:231
msgid "*bug closed*"
msgstr "bug 已關閉"

#: tcms/bugs/views.py:235
msgid "*bug reopened*"
msgstr "bug 重新打開"

#: tcms/core/history.py:52
#, python-format
msgid "UPDATE: %(model_name)s #%(pk)d - %(title)s"
msgstr "更新: %(model_name)s #%(pk)d - %(title)s"

#: tcms/core/history.py:62
#, python-format
msgid "Updated on %(history_date)s\n"
"Updated by %(username)s\n\n"
"%(diff)s\n\n"
"For more information:\n"
"%(instance_url)s"
msgstr "更新於 %(history_date)s\n"
"由 %(username)s 更新\n\n"
"%(diff)s\n\n"
"取得更多訊息:\n"
"%(instance_url)s _ 321"

#: tcms/core/templates/dashboard.html:3 tcms/settings/common.py:430
msgid "Dashboard"
msgstr "儀表板"

#: tcms/core/templates/dashboard.html:8
#: tcms/testruns/templates/testruns/get.html:153
msgid "Test executions"
msgstr "測試執行"

#: tcms/core/templates/dashboard.html:15
#, python-format
msgid "%(amount)s%% complete"
msgstr "%(amount)s%% 完成"

#: tcms/core/templates/dashboard.html:24
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:38
#: tcms/testruns/templates/testruns/get.html:78
msgid "Started at"
msgstr "開始於"

#: tcms/core/templates/dashboard.html:32
#, python-format
msgid "%(total_count)s TestRun(s) or TestCase(s) assigned to you need to be executed.\n"
"Here are the latest %(count)s."
msgstr "分配給你的 %(total_count)s 測試運行(s) 或 測試案例(s) 需要執行。\n"
"這裡是最新的 %(count)s。"

#: tcms/core/templates/dashboard.html:36 tcms/core/templates/dashboard.html:80
msgid "SEE ALL"
msgstr "查看全部"

#: tcms/core/templates/dashboard.html:39
msgid "There are no TestRun(s) assigned to you"
msgstr "沒有分配給您的測試執行"

#: tcms/core/templates/dashboard.html:46
msgid "Your Test plans"
msgstr "你的測試計畫"

#: tcms/core/templates/dashboard.html:52
msgid "TestPlan"
msgstr "測試計畫"

#: tcms/core/templates/dashboard.html:54
#: tcms/testcases/templates/testcases/get.html:169
#: tcms/testplans/templates/testplans/mutable.html:66
#: tcms/testplans/templates/testplans/search.html:70
#: tcms/testplans/templates/testplans/search.html:119
msgid "Type"
msgstr "類型"

#: tcms/core/templates/dashboard.html:55
#: tcms/templates/include/tc_executions.html:7
msgid "Executions"
msgstr "執行"

#: tcms/core/templates/dashboard.html:76
#, python-format
msgid "You manage %(total_count)s TestPlan(s), %(disabled_count)s are disabled.\n"
"Here are the latest %(count)s."
msgstr "您管理 %(total_count)s 個測試計劃，其中 %(disabled_count)s 個已被停用。\n"
"以下是最新的 %(count)s 個。"

#: tcms/core/templates/dashboard.html:83
msgid "There are no TestPlan(s) that belong to you"
msgstr "沒有屬於您的測試計劃"

#: tcms/core/views.py:47
#, python-format
msgid "Base URL is not configured! See <a href=\"%(doc_url)s\">documentation</a> and <a href=\"%(admin_url)s\">change it</a>"
msgstr "基本網址尚未設定！請參考 <a href=\"%(doc_url)s\">文件說明</a>，並到 <a href=\"%(admin_url)s\">管理頁面更改設定</a>。"

#: tcms/core/views.py:71
#, python-format
msgid "You have %(unapplied_migration_count)s unapplied migration(s). See <a href=\"%(doc_url)s\">documentation</a>"
msgstr "您有 %(unapplied_migration_count)s 個未應用的遷移。詳情請參閱 <a href=\"%(doc_url)s\">文件說明</a>"

#: tcms/core/views.py:92
#, python-format
msgid "You are not using a secure connection. See <a href=\"%(doc_url)s\">documentation</a> and enable SSL."
msgstr "您目前未使用安全連線。請參考 <a href=\"%(doc_url)s\">文件說明</a> 並啟用 SSL。"

#: tcms/kiwi_attachments/validators.py:10
#, python-brace-format
msgid "File contains forbidden tag: <{tag_name}>"
msgstr "檔案包含禁止使用的標籤：<{tag_name}>"

#: tcms/kiwi_attachments/validators.py:92
#, python-brace-format
msgid "File contains forbidden attribute: `{attr_name}`"
msgstr "檔案包含禁止屬性：{attr_name}"

#: tcms/kiwi_attachments/validators.py:97
msgid "Uploading executable files is forbidden"
msgstr "禁止上傳可執行文件"

#: tcms/kiwi_auth/admin.py:36 tcms/settings/common.py:443
msgid "Users"
msgstr "使用者"

#: tcms/kiwi_auth/admin.py:82
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:5
#: tcms/templates/navbar.html:102
msgid "Reset email address"
msgstr "重置電子郵件地址"

#: tcms/kiwi_auth/admin.py:148
msgid "Personal info"
msgstr "個人信息"

#: tcms/kiwi_auth/admin.py:150
msgid "Permissions"
msgstr "使用權限"

#: tcms/kiwi_auth/admin.py:187
msgid "This is the last superuser, it cannot be deleted!"
msgstr "這是最後一個超級用戶，無法刪除！"

#: tcms/kiwi_auth/forms.py:28
msgid "A user with that email already exists."
msgstr "使用此電子郵件的用戶已經存在"

#: tcms/kiwi_auth/forms.py:48
msgid "Please confirm your Kiwi TCMS account email address"
msgstr "請確認您的 Kiwi TCMS 帳戶電子郵件地址"

#: tcms/kiwi_auth/forms.py:142
msgid "Email mismatch"
msgstr "電子郵件不符"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:14
msgid "Warning"
msgstr "警告"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:19
msgid "After clicking the 'Save' button your account will become <strong>inactive</strong>\n"
"and you will be <strong>logged out</strong>! A confirmation email will be sent to the newly specified address!<br>\n"
"Double check that your new email address is <strong>entered correctly</strong> otherwise\n"
"<strong>you may be left locked out</strong> of your account!\n"
"After following the activation link you will be able to log in as usual!"
msgstr "點選「儲存」按鈕後，您的帳號將變成<strong>非活動狀態</strong> 您將<strong>退出</strong>！確認電子郵件將發送到新指定的地址！ 仔細檢查您的新電子郵件地址是否<strong>輸入正確</strong>，否則 <strong>您的帳號可能被鎖定</strong>！ 點擊啟動連結後，您將可以像往常一樣登入！"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:51
msgid "NOT yourself"
msgstr "NOT yourself"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:58
#: tcms/templates/registration/password_reset_form.html:16
#: tcms/templates/registration/registration_form.html:39
msgid "E-mail"
msgstr "電子郵件"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:66
#: tcms/templates/registration/password_reset_confirm.html:22
#: tcms/templates/registration/registration_form.html:31
msgid "Confirm"
msgstr "確認"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:71
msgid "Please type! Do not copy-and-paste value from previous field!"
msgstr "請輸入！不要複製並貼上一個欄位中的值！"

#: tcms/kiwi_auth/views.py:70
msgid "Your account has been created, please check your mailbox for confirmation"
msgstr "您的帳戶已經新增完畢，請確認您的 E-mail 是否有啟動連結的信件。"

#: tcms/kiwi_auth/views.py:75
msgid "Your account has been created, but you need an administrator to activate it"
msgstr "您的帳戶已創建, 但您需要管理員來啟用它"

#: tcms/kiwi_auth/views.py:80
msgid "Following is the administrator list"
msgstr "以下是管理員清單"

#: tcms/kiwi_auth/views.py:123
msgid "This activation key no longer exists in the database"
msgstr "此啟用金鑰不存在於資料庫中"

#: tcms/kiwi_auth/views.py:129
msgid "This activation key has expired"
msgstr "此啟用金鑰已過期"

#: tcms/kiwi_auth/views.py:141
msgid "Your account has been activated successfully"
msgstr "您的帳戶已成功啟用"

#: tcms/kiwi_auth/views.py:168 tcms/kiwi_auth/views.py:177
#: tcms/kiwi_auth/views.py:196 tcms/kiwi_auth/views.py:205
#, python-format
msgid "You are viewing records from tenant '%s'"
msgstr "您正在查看租戶「%s」的記錄"

#: tcms/kiwi_auth/views.py:256
msgid "Email address has been reset, please check inbox for further instructions"
msgstr "電子郵件地址已重置，請檢查收件匣以取得進一步說明"

#: tcms/management/models.py:58
#: tcms/telemetry/templates/telemetry/testing/breakdown.html:34
msgid "Priorities"
msgstr "優先程度"

#: tcms/management/models.py:133
msgid "Builds"
msgstr "建置"

#: tcms/management/models.py:144
#: tcms/testcases/templates/testcases/search.html:136
#: tcms/testplans/templates/testplans/get.html:217
#: tcms/testplans/templates/testplans/search.html:94
#: tcms/testruns/templates/testruns/get.html:263
#: tcms/testruns/templates/testruns/search.html:27
msgid "Tag"
msgstr "標籤"

#: tcms/management/models.py:145 tcms/templates/include/tags_card.html:6
#: tcms/testcases/templates/testcases/search.html:171
#: tcms/testplans/templates/testplans/get.html:456
#: tcms/testplans/templates/testplans/search.html:121
#: tcms/testruns/templates/testruns/get.html:353
#: tcms/testruns/templates/testruns/search.html:190
msgid "Tags"
msgstr "標籤"

#: tcms/rpc/api/bug.py:69
msgid "Enable reporting to this Issue Tracker by configuring its base_url!"
msgstr "透過設置base_url 來啟用對此問題追蹤器的回報"

#: tcms/rpc/api/forms/__init__.py:9
msgid "Invalid date format. Expected YYYY-MM-DD [HH:MM:SS]."
msgstr "無效的日期格式。預設為 YYYY-MM-DD [HH:MM:SS]。"

#: tcms/settings/common.py:391
msgid "TESTING"
msgstr "測試"

#: tcms/settings/common.py:393 tcms/testruns/templates/testruns/mutable.html:70
msgid "New Test Plan"
msgstr "新增測試計畫"

#: tcms/settings/common.py:395
#: tcms/testcases/templates/testcases/mutable.html:12
#: tcms/testplans/templates/testplans/get.html:131
msgid "New Test Case"
msgstr "新增測試案例"

#: tcms/settings/common.py:397 tcms/testplans/templates/testplans/get.html:121
#: tcms/testruns/templates/testruns/get.html:172
#: tcms/testruns/templates/testruns/mutable.html:11
msgid "New Test Run"
msgstr "新增測試執行"

#: tcms/settings/common.py:407
msgid "SEARCH"
msgstr "搜尋"

#: tcms/settings/common.py:409 tcms/testplans/templates/testplans/search.html:5
msgid "Search Test Plans"
msgstr "搜尋測試計畫"

#: tcms/settings/common.py:410 tcms/testcases/templates/testcases/search.html:5
msgid "Search Test Cases"
msgstr "搜尋測試案例"

#: tcms/settings/common.py:411 tcms/testruns/templates/testruns/search.html:5
msgid "Search Test Runs"
msgstr "搜尋測試執行"

#: tcms/settings/common.py:412
msgid "Search Test Executions"
msgstr "搜尋測試執行紀錄"

#: tcms/settings/common.py:421
msgid "TELEMETRY"
msgstr "測試報告"

#: tcms/settings/common.py:424
msgid "Testing"
msgstr "測試中"

#: tcms/settings/common.py:426
msgid "Breakdown"
msgstr "分解"

#: tcms/settings/common.py:428
msgid "Execution"
msgstr "執行"

#: tcms/settings/common.py:431
#: tcms/testruns/templates/testruns/mutable.html:177
msgid "Matrix"
msgstr "矩陣"

#: tcms/settings/common.py:432
msgid "Trends"
msgstr "趨勢"

#: tcms/settings/common.py:435
#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:5
msgid "TestCase health"
msgstr "測試案例健康度"

#: tcms/settings/common.py:441
msgid "ADMIN"
msgstr "管理者"

#: tcms/settings/common.py:444
msgid "Groups"
msgstr "群組"

#: tcms/settings/common.py:446
msgid "Everything else"
msgstr "管理其他事項"

#: tcms/settings/common.py:449
msgid "MORE"
msgstr ""

#: tcms/settings/common.py:459
msgid "Report an Issue"
msgstr "回報問題"

#: tcms/settings/common.py:462
msgid "Ask for help on StackOverflow"
msgstr "在StackOflow上求援"

#: tcms/settings/common.py:466
msgid "Donate €5 via Open Collective"
msgstr "透過開放群體捐贈5歐元"

#: tcms/settings/common.py:468
msgid "Administration Guide"
msgstr "管理者指南"

#: tcms/settings/common.py:469
msgid "User Guide"
msgstr "使用者指南"

#: tcms/settings/common.py:470
msgid "API Help"
msgstr "API 說明"

#: tcms/signals.py:85
msgid "New user awaiting approval"
msgstr "等待審批的新使用者"

#: tcms/signals.py:163
#, python-format
msgid "NEW: TestRun #%(pk)d - %(summary)s"
msgstr "NEW: 測試執行 #%(pk)d - - %(summary)s"

#: tcms/signals.py:235
#, python-format
msgid "Bug #%(pk)d - %(summary)s"
msgstr "缺陷 #%(pk)d - %(summary)s"

#: tcms/telemetry/api.py:60 testcases.TestCaseStatus/name:2
msgid "CONFIRMED"
msgstr "已驗證"

#: tcms/telemetry/api.py:61
msgid "OTHER"
msgstr "其他"

#: tcms/telemetry/api.py:133 tcms/telemetry/api.py:185
#: tcms/telemetry/api.py:191
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:9
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:13
#: tcms/testruns/templates/testruns/get.html:129
msgid "TOTAL"
msgstr "總共"

#: tcms/telemetry/templates/telemetry/include/filters.html:36
#: tcms/testcases/templates/testcases/search.html:68
#: tcms/testplans/templates/testplans/search.html:115
#: tcms/testruns/templates/testruns/get.html:40
#: tcms/testruns/templates/testruns/mutable.html:67
#: tcms/testruns/templates/testruns/search.html:182
msgid "Test plan"
msgstr "測試計畫"

#: tcms/telemetry/templates/telemetry/include/filters.html:66
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
#: tcms/testcases/templates/testcases/search.html:142
msgid "Test run"
msgstr "測試運行"

#: tcms/telemetry/templates/telemetry/include/filters.html:70
#: tcms/testruns/templates/testruns/search.html:17
msgid "Test run summary"
msgstr "測試執行總結"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:5
msgid "Testing Breakdown"
msgstr "測試分類"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:14
msgid "Total"
msgstr "總共"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:24
#: tcms/testcases/templates/testcases/get.html:93
#: tcms/testcases/templates/testcases/mutable.html:98
#: tcms/testcases/templates/testcases/search.html:36
#: tcms/testcases/templates/testcases/search.html:168
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testplans/templates/testplans/get.html:489
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:5
#: tcms/testruns/templates/testruns/get.html:264
#: tcms/testruns/templates/testruns/get.html:361
msgid "Automated"
msgstr "自動化"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:28
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testruns/templates/testruns/get.html:361
msgid "Manual"
msgstr "手動"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:39
#: tcms/testcases/models.py:42
msgid "Categories"
msgstr "分類"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:5
msgid "Execution Dashboard"
msgstr "執行儀表板"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:15
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:15
msgid "Child TPs"
msgstr "子測試計劃"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:28
#: tcms/templates/include/bug_details.html:3
#: tcms/testcases/templates/testcases/get.html:59
#: tcms/testcases/templates/testcases/mutable.html:74
#: tcms/testcases/templates/testcases/search.html:91
#: tcms/testcases/templates/testcases/search.html:167
#: tcms/testplans/templates/testplans/get.html:149
#: tcms/testplans/templates/testplans/get.html:363
#: tcms/testplans/templates/testplans/get.html:396
#: tcms/testplans/templates/testplans/get.html:488
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:4
#: tcms/testruns/templates/testruns/get.html:189
#: tcms/testruns/templates/testruns/get.html:269
#: tcms/testruns/templates/testruns/get.html:385
#: tcms/testruns/templates/testruns/mutable.html:232
msgid "Status"
msgstr "狀態"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:31
#: tcms/testcases/templates/testcases/get.html:208
#: tcms/testplans/templates/testplans/get.html:447
#: tcms/testruns/templates/testruns/get.html:349
msgid "Components"
msgstr "元件/組件"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
#: tcms/testcases/templates/testcases/get.html:33
#: tcms/testcases/templates/testcases/mutable.html:36
#: tcms/testcases/templates/testcases/mutable.html:207
#: tcms/testcases/templates/testcases/search.html:170
#: tcms/testplans/templates/testplans/get.html:175
#: tcms/testplans/templates/testplans/get.html:378
#: tcms/testplans/templates/testplans/get.html:416
#: tcms/testplans/templates/testplans/get.html:493
#: tcms/testplans/templates/testplans/mutable.html:133
#: tcms/testplans/templates/testplans/search.html:88
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:9
#: tcms/testruns/templates/testruns/get.html:66
#: tcms/testruns/templates/testruns/mutable.html:36
#: tcms/testruns/templates/testruns/search.html:73
#: tcms/testruns/templates/testruns/search.html:189
msgid "Default tester"
msgstr "預設測試者"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
msgid "TC"
msgstr "TC(測試案例)"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
msgid "TR"
msgstr "TR(測試執行)"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:37
#: tcms/testruns/templates/testruns/get.html:268
#: tcms/testruns/templates/testruns/get.html:375
msgid "Tested by"
msgstr "測試由"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:39
#: tcms/testruns/templates/testruns/get.html:96
#: tcms/testruns/templates/testruns/get.html:405
#: tcms/testruns/templates/testruns/mutable.html:140
msgid "Finished at"
msgstr "完成於："

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:5
msgid "Execution Trends"
msgstr "執行趨勢"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:21
msgid "Positive"
msgstr "正向"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:24
msgid "Neutral"
msgstr "中性"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:27
msgid "Negative"
msgstr "負數"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:5
msgid "Execution Matrix"
msgstr "執行矩陣"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:21
msgid "Order"
msgstr "順序"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Ascending"
msgstr "遞增"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Descending"
msgstr "遞減"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
msgid "Test case"
msgstr "測試案例"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:12
msgid "Most frequently failing test cases"
msgstr "最常失敗的測試案例"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:16
msgid "Test Case"
msgstr "測試案例"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:18
msgid "Failed executions"
msgstr "執行失敗的次數"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:19
#, python-format
msgid "%% of failed executions"
msgstr "%% 執行失敗"

#: tcms/templates/404.html:5 tcms/templates/404.html:16
msgid "Page not found"
msgstr "找不到頁面"

#: tcms/templates/500.html:5 tcms/templates/500.html:16
msgid "Internal Server Error"
msgstr "內部伺服器錯誤"

#: tcms/templates/attachments/add.html:3
msgid "Attachment upload error"
msgstr "附件上傳錯誤"

#: tcms/templates/attachments/delete_link.html:3
msgid "Are you sure you want to delete this attachment?"
msgstr "確定要刪除此附件嗎？"

#: tcms/templates/base.html:8
msgid "day"
msgstr "天"

#: tcms/templates/base.html:8
msgid "days"
msgstr "天"

#: tcms/templates/base.html:9
msgid "hour"
msgstr "小時"

#: tcms/templates/base.html:9
msgid "hours"
msgstr "小時"

#: tcms/templates/base.html:10
msgid "minute"
msgstr "分鐘"

#: tcms/templates/base.html:10
msgid "minutes"
msgstr "分鐘"

#: tcms/templates/base.html:11
msgid "second"
msgstr "秒"

#: tcms/templates/base.html:11
msgid "seconds"
msgstr "秒"

#: tcms/templates/base.html:16 tcms/templates/registration/login.html:46
msgid "the leading open source test case management system"
msgstr "先進的開源測試管理系統"

#: tcms/templates/email/confirm_registration.txt:1
#, python-format
msgid "Welcome to Kiwi TCMS!\n\n"
"To confirm email address for username `%(user)s` and activate your account\n"
"please follow this URL:\n"
"%(confirm_url)s\n\n"
"Regards,\n"
"Kiwi TCMS"
msgstr "Welcome to Kiwi TCMS!\n\n"
"確認使用者名稱「%(user)s」\n"
"的電子郵件地址並啟動您的帳戶 請關注此網址：\n"
" %(confirm_url)s \n\n"
"Regards,\n"
"Kiwi TCMS"

#: tcms/templates/email/post_bug_save/email.txt:2
#, python-format
msgid "Bug %(pk)s has been updated.\n\n"
"Link: %(bug_url)s\n\n"
"Summary: %(summary)s\n"
"Created at: %(creation_date)s\n"
"Reporter: %(reporter)s\n"
"Assignee: %(assignee)s\n"
"Product: %(product)s\n"
"Version: %(version)s\n"
"Build: %(build)s\n"
"Last comment:\n"
"%(last_comment)s"
msgstr ""

#: tcms/templates/email/post_case_delete/email.txt:2
#, python-format
msgid "TestCase has been deleted by %(username)s!"
msgstr "測試案例已被 %(username)s 刪除！"

#: tcms/templates/email/post_run_save/email.txt:2
#, python-format
msgid "Test run %(pk)s has been created or updated for you.\n\n"
"### Links ###\n"
"Test run: %(run_url)s\n"
"Test plan: %(plan_url)s\n\n"
"### Basic run information ###\n"
"Summary: %(summary)s\n\n"
"Managed: %(manager)s.\n"
"Default tester: %(default_tester)s.\n\n"
"Product: %(product)s\n"
"Product version: %(version)s\n"
"Build: %(build)s\n\n"
"Notes:\n"
"%(notes)s"
msgstr "已為你新建或更新測試執行 %(pk)s 。\n\n"
"### 連結 ###\n"
"測試運行: %(run_url)s\n"
"測試計畫: %(plan_url)s\n\n"
"### 基本運行資訊 ###\n"
"摘要: %(summary)s\n\n"
"管理: %(manager)s.\n"
"預設測試者: %(default_tester)s.\n\n"
"產品: %(product)s\n"
"產品版本: %(version)s\n"
"建置版本: %(build)s\n\n"
"備註:\n"
"%(notes)s"

#: tcms/templates/email/user_registered/notify_admins.txt:2
#, python-format
msgid "Dear Administrator,\n"
"somebody just registered an account with username %(username)s at your\n"
"Kiwi TCMS instance and is awaiting your approval!\n\n"
"Go to %(user_url)s to activate the account!"
msgstr "給管理者，\n"
"有人剛剛在此\n"
"Kiwi TCMS註冊了一個 %(username)s 的使用者，正等待你的許可！\n\n"
"點擊 %(user_url)s 啟用帳號！"

#: tcms/templates/include/attachments.html:10
#: tcms/testplans/templates/testplans/get.html:437
#: tcms/testruns/templates/testruns/get.html:487
msgid "Attachments"
msgstr "附件"

#: tcms/templates/include/attachments.html:18
msgid "File"
msgstr "檔案"

#: tcms/templates/include/attachments.html:19
msgid "Owner"
msgstr "擁有者"

#: tcms/templates/include/attachments.html:20
msgid "Date"
msgstr "日期"

#: tcms/templates/include/attachments.html:35
#: tcms/templates/include/bugs_table.html:4
#: tcms/testplans/templates/testplans/get.html:332
msgid "No records found"
msgstr "沒有紀錄"

#: tcms/templates/include/bugs_table.html:15
#: tcms/testruns/templates/testruns/get.html:573
msgid "URL"
msgstr "網址"

#: tcms/templates/include/properties_card.html:9 tcms/testruns/admin.py:117
#: tcms/testruns/templates/testruns/get.html:345
msgid "Parameters"
msgstr "參數"

#: tcms/templates/include/properties_card.html:15
#: tcms/testruns/templates/testruns/mutable.html:170
msgid "This is a tech-preview feature!"
msgstr "這是技術預覽功能！"

#: tcms/templates/include/properties_card.html:68
msgid "name=value"
msgstr "名稱=值"

#: tcms/templates/include/properties_card.html:69
#: tcms/templates/include/tags_card.html:28
#: tcms/testcases/templates/testcases/get.html:184
#: tcms/testcases/templates/testcases/get.html:229
#: tcms/testplans/templates/testplans/get.html:258
#: tcms/testruns/templates/testruns/get.html:292
#: tcms/testruns/templates/testruns/get.html:546
msgid "Add"
msgstr "新增"

#: tcms/templates/include/tags_card.html:13
#: tcms/testcases/templates/testcases/get.html:167
#: tcms/testcases/templates/testcases/get.html:215
#: tcms/testplans/templates/testplans/clone.html:14
#: tcms/testplans/templates/testplans/get.html:205
#: tcms/testplans/templates/testplans/mutable.html:24
#: tcms/testplans/templates/testplans/search.html:18
#: tcms/testruns/templates/testruns/get.html:579
msgid "Name"
msgstr "名稱"

#: tcms/templates/initdb.html:5 tcms/templates/initdb.html:17
#: tcms/templates/initdb.html:29
msgid "Initialize database"
msgstr "初始化資料庫"

#: tcms/templates/initdb.html:20
msgid "Your database has not been initialized yet. Click the button below to initialize it!"
msgstr "您的資料庫尚未初始化。請點擊下方按鈕以初始化"

#: tcms/templates/initdb.html:22
msgid "WARNING: this operation will take a while! This page will redirect when done."
msgstr "警告：此操作可能需要一段時間！完成後本頁將自動重新導向。"

#: tcms/templates/initdb.html:27
msgid "Please wait"
msgstr "請稍候"

#: tcms/templates/navbar.html:9
msgid "Toggle navigation"
msgstr "切換導覽"

#: tcms/templates/navbar.html:15
msgid "DASHBOARD"
msgstr "儀表板"

#: tcms/templates/navbar.html:41
msgid "Language"
msgstr "語言"

#: tcms/templates/navbar.html:45
msgid "Change language"
msgstr "變更語言"

#: tcms/templates/navbar.html:46
msgid "Supported languages"
msgstr "支援的語言"

#: tcms/templates/navbar.html:47
msgid "Request new language"
msgstr "要求一個新的語言"

#: tcms/templates/navbar.html:53
msgid "Translation mode"
msgstr "進入翻譯模式"

#: tcms/templates/navbar.html:57
msgid "Translation guide"
msgstr "翻譯指南"

#: tcms/templates/navbar.html:63
msgid "Help"
msgstr "幫助"

#: tcms/templates/navbar.html:78
msgid "Welcome Guest"
msgstr "歡迎訪客"

#: tcms/templates/navbar.html:84
msgid "My Test Runs"
msgstr "我的測試執行"

#: tcms/templates/navbar.html:88
msgid "My Test Plans"
msgstr "我的測試計畫"

#: tcms/templates/navbar.html:94
msgid "My profile"
msgstr "我的檔案"

#: tcms/templates/navbar.html:98
#: tcms/templates/registration/password_reset_confirm.html:29
msgid "Change password"
msgstr "變更密碼"

#: tcms/templates/navbar.html:108
msgid "Logout"
msgstr "登出"

#: tcms/templates/navbar.html:113 tcms/templates/registration/login.html:4
msgid "Login"
msgstr "登入"

#: tcms/templates/navbar.html:120
#: tcms/templates/registration/registration_form.html:53
msgid "Register"
msgstr "註冊"

#: tcms/templates/registration/login.html:24
#: tcms/templates/registration/password_reset_confirm.html:15
#: tcms/templates/registration/registration_form.html:23
msgid "Password"
msgstr "密碼"

#: tcms/templates/registration/login.html:31
msgid "Forgot password"
msgstr "忘記密碼"

#: tcms/templates/registration/login.html:34
msgid "Log in"
msgstr "登入"

#: tcms/templates/registration/login.html:45
msgid "Welcome to Kiwi TCMS"
msgstr "歡迎來到 Kiwi 測試案例管理系統"

#: tcms/templates/registration/login.html:50
msgid "Please login to get started"
msgstr "請先登入"

#: tcms/templates/registration/login.html:52
msgid "or"
msgstr "或"

#: tcms/templates/registration/login.html:53
msgid "register an account"
msgstr "註冊帳號"

#: tcms/templates/registration/login.html:54
msgid "if you don't have one!"
msgstr "如果你沒有的話"

#: tcms/templates/registration/password_reset_complete.html:12
msgid "Your password has been set. You may go ahead and"
msgstr "您的密碼已被設置。您現在可以登入"

#: tcms/templates/registration/password_reset_complete.html:13
msgid "now"
msgstr "現在"

#: tcms/templates/registration/password_reset_confirm.html:43
msgid "Please enter your new password twice so we can verify you typed it in correctly"
msgstr "請輸入您的新密碼兩次，讓我們能夠驗證是否正確"

#: tcms/templates/registration/password_reset_confirm.html:46
msgid "request a new password reset"
msgstr "請求密碼重置"

#: tcms/templates/registration/password_reset_done.html:11
msgid "Password reset email was sent"
msgstr "已傳送密碼重設電子郵件。"

#: tcms/templates/registration/password_reset_form.html:27
msgid "Password reset"
msgstr "密碼重設"

#: tcms/templates/registration/password_reset_form.html:34
msgid "Kiwi TCMS password reset"
msgstr "Kiwi TCMS 密碼重設"

#: tcms/templates/registration/registration_form.html:4
msgid "Register new account"
msgstr "註冊新帳號"

#: tcms/testcases/admin.py:28
msgid "For more information about customizing test case statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-case-statuses\">\n"
"        the documentation</a>!"
msgstr "欲了解更多關於自訂測試案例狀態的資訊，請參考\n"
"<a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-case-statuses\">官方文件說明</a>！"

#: tcms/testcases/admin.py:56
msgid "1 confirmed & 1 uncomfirmed status required!"
msgstr "需要一個已確認以及一個未確認的狀態！"

#: tcms/testcases/admin.py:131
msgid "Bug URL"
msgstr "缺陷 URL"

#: tcms/testcases/admin.py:151
msgid "External Issue Tracker Integration"
msgstr "整合外部問題管理系統"

#: tcms/testcases/admin.py:161
msgid "<h1>Warning: read the\n"
"<a href=\"http://kiwitcms.readthedocs.io/en/latest/admin.html#configure-external-bug-trackers\">\n"
"Configure external bug trackers</a> section before editting the values below!</h1>"
msgstr "<h1>警告：請先閱讀\n"
"<a href=\"http://kiwitcms.readthedocs.io/en/latest/admin.html#configure-external-bug-trackers\">\n"
"設定外部錯誤追蹤系統</a> 區段，然後再編輯下方數值！</h1>"

#: tcms/testcases/admin.py:168
msgid "Configuration health check"
msgstr "檢查設定"

#: tcms/testcases/admin.py:172
msgid "Kiwi TCMS will try fetching details for the given bug URL using the integration defined above! Click the `Save and continue` button and watch out for messages at the top of the screen. <strong>WARNING:</strong> in case of failures some issue trackers will fall back to fetching details via the OpenGraph protocol. In that case the result will include field named `from_open_graph`."
msgstr "Kiwi TCMS 將嘗試使用上述設定的整合方式，擷取指定錯誤連結的詳細資訊！請點擊「儲存並繼續」按鈕，並注意螢幕頂端的訊息。\n"
"<strong>警告</strong>： 若發生錯誤，一些問題追蹤系統會改以 OpenGraph 協議擷取資訊，此時結果中會包含名為 from_open_graph 的欄位。"

#: tcms/testcases/admin.py:192
msgid "Failed creating Issue Tracker"
msgstr "建立問題管理系統失敗"

#: tcms/testcases/admin.py:201
msgid "Details extracted via OpenGraph. Issue Tracker may still be configured incorrectly!"
msgstr "透過 OpenGraph 擷取的詳細資訊。問題追蹤系統可能仍設定不正確！"

#: tcms/testcases/admin.py:210
msgid "Details extracted via API. Issue Tracker configuration looks good!"
msgstr "透過 API 擷取的詳細資訊。問題追蹤系統設定看起來正常。"

#: tcms/testcases/admin.py:223
msgid "Issue Tracker configuration check failed"
msgstr "問題管理系統設定檢查失敗"

#: tcms/testcases/helpers/email.py:22
#, python-format
msgid "DELETED: TestCase #%(pk)d - %(summary)s"
msgstr "已刪除: 測試案例 #%(pk)d - %(summary)s"

#: tcms/testcases/models.py:23
msgid "Test case status"
msgstr "測試案例狀態"

#: tcms/testcases/models.py:24
msgid "Test case statuses"
msgstr "測試案例狀態"

#: tcms/testcases/models.py:379
#: tcms/testcases/templates/testcases/mutable.html:107
msgid "Template"
msgstr "範本"

#: tcms/testcases/models.py:380
msgid "Templates"
msgstr "範本"

#: tcms/testcases/templates/testcases/clone.html:5
msgid "Clone TestCase"
msgstr "複製測試案例"

#: tcms/testcases/templates/testcases/clone.html:15
msgid "Add new TC into TP"
msgstr "新增新的TC到TP"

#: tcms/testcases/templates/testcases/clone.html:30
msgid "Selected TC"
msgstr "已選擇TC"

#: tcms/testcases/templates/testcases/clone.html:45 tcms/testcases/views.py:134
#: tcms/testplans/templates/testplans/clone.html:73
#: tcms/testplans/templates/testplans/get.html:138
#: tcms/testplans/templates/testplans/get.html:358 tcms/testplans/views.py:144
#: tcms/testruns/views.py:198
msgid "Clone"
msgstr "克隆"

#: tcms/testcases/templates/testcases/get.html:28
#: tcms/testcases/templates/testcases/get.html:168
#: tcms/testcases/templates/testcases/mutable.html:184
#: tcms/testcases/templates/testcases/search.html:122
#: tcms/testcases/templates/testcases/search.html:169
#: tcms/testplans/templates/testplans/get.html:54
#: tcms/testplans/templates/testplans/get.html:412
#: tcms/testplans/templates/testplans/get.html:492
#: tcms/testplans/templates/testplans/mutable.html:121
#: tcms/testplans/templates/testplans/search.html:82
#: tcms/testplans/templates/testplans/search.html:120
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:8
#: tcms/testruns/templates/testruns/mutable.html:230
msgid "Author"
msgstr "作者"

#: tcms/testcases/templates/testcases/get.html:49
#: tcms/testcases/templates/testcases/mutable.html:58
#: tcms/testcases/templates/testcases/search.html:100
#: tcms/testcases/templates/testcases/search.html:164
#: tcms/testplans/templates/testplans/get.html:408
#: tcms/testplans/templates/testplans/get.html:491
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:7
#: tcms/testruns/templates/testruns/get.html:266
#: tcms/testruns/templates/testruns/get.html:367
#: tcms/testruns/templates/testruns/mutable.html:233
msgid "Category"
msgstr "類別"

#: tcms/testcases/templates/testcases/get.html:64
#: tcms/testcases/templates/testcases/mutable.html:86
#: tcms/testcases/templates/testcases/search.html:82
#: tcms/testcases/templates/testcases/search.html:166
#: tcms/testplans/templates/testplans/get.html:162
#: tcms/testplans/templates/testplans/get.html:371
#: tcms/testplans/templates/testplans/get.html:404
#: tcms/testplans/templates/testplans/get.html:490
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:6
#: tcms/testruns/templates/testruns/get.html:265
#: tcms/testruns/templates/testruns/get.html:363
#: tcms/testruns/templates/testruns/mutable.html:234
msgid "Priority"
msgstr "優先級"

#: tcms/testcases/templates/testcases/get.html:73
#: tcms/testcases/templates/testcases/mutable.html:120
msgid "Setup duration"
msgstr "設置時間"

#: tcms/testcases/templates/testcases/get.html:78
#: tcms/testcases/templates/testcases/mutable.html:127
msgid "Testing duration"
msgstr "測試時間"

#: tcms/testcases/templates/testcases/get.html:83
msgid "Expected duration"
msgstr "預計時間"

#: tcms/testcases/templates/testcases/get.html:98
#: tcms/testcases/templates/testcases/mutable.html:143
msgid "Script"
msgstr "腳本"

#: tcms/testcases/templates/testcases/get.html:103
#: tcms/testcases/templates/testcases/mutable.html:149
msgid "Arguments"
msgstr "參數"

#: tcms/testcases/templates/testcases/get.html:108
#: tcms/testcases/templates/testcases/mutable.html:157
msgid "Requirements"
msgstr "請求"

#: tcms/testcases/templates/testcases/get.html:113
#: tcms/testcases/templates/testcases/mutable.html:162
#: tcms/testplans/templates/testplans/get.html:80
#: tcms/testplans/templates/testplans/mutable.html:102
msgid "Reference link"
msgstr "參考連結"

#: tcms/testcases/templates/testcases/get.html:133
#: tcms/testcases/templates/testcases/mutable.html:170
#: tcms/testplans/templates/testplans/get.html:433
#: tcms/testplans/templates/testplans/get.html:434
#: tcms/testruns/templates/testruns/get.html:400
#: tcms/testruns/templates/testruns/mutable.html:202
msgid "Notes"
msgstr "備註"

#: tcms/testcases/templates/testcases/get.html:152
msgid "Bugs"
msgstr "Bug"

#: tcms/testcases/templates/testcases/get.html:159
msgid "Test plans"
msgstr "測試計畫"

#: tcms/testcases/templates/testcases/mutable.html:10
msgid "Edit TestCase"
msgstr "編輯測試案例"

#: tcms/testcases/templates/testcases/mutable.html:59
msgid "add new Category"
msgstr "新增新的類別"

#: tcms/testcases/templates/testcases/mutable.html:108
msgid "add new Template"
msgstr "新增範本"

#: tcms/testcases/templates/testcases/mutable.html:180
#: tcms/testruns/templates/testruns/get.html:517
msgid "Notify"
msgstr "通知"

#: tcms/testcases/templates/testcases/mutable.html:191
msgid "Manager of runs"
msgstr "運行管理器"

#: tcms/testcases/templates/testcases/mutable.html:198
msgid "Asignees"
msgstr "代理人"

#: tcms/testcases/templates/testcases/mutable.html:214
msgid "Default tester of runs"
msgstr "預設測試人員"

#: tcms/testcases/templates/testcases/mutable.html:223
msgid "Notify when"
msgstr "以下情況通知我"

#: tcms/testcases/templates/testcases/mutable.html:224
#: tcms/testplans/templates/testplans/mutable.html:142
msgid "applies only for changes made by somebody else"
msgstr "僅適用於他人所做的變更"

#: tcms/testcases/templates/testcases/mutable.html:229
msgid "TestCase is updated"
msgstr "測試案例已更新"

#: tcms/testcases/templates/testcases/mutable.html:236
msgid "TestCase is deleted"
msgstr "測試案例已刪除"

#: tcms/testcases/templates/testcases/mutable.html:245
msgid "CC to"
msgstr "副本給"

#: tcms/testcases/templates/testcases/mutable.html:250
msgid "Email addresses separated by comma. A notification email will be sent to each Email address within CC list."
msgstr "用逗號分隔的電子郵件地址，通知電子郵件將在 CC 列表中發送給每個電子郵件位址。"

#: tcms/testcases/templates/testcases/search.html:15
msgid "Test case summary"
msgstr "測試案例總結"

#: tcms/testcases/templates/testcases/search.html:45
msgid "Both"
msgstr "都"

#: tcms/testcases/templates/testcases/search.html:61
msgid "include in search request"
msgstr "包含於搜尋請求"

#: tcms/testcases/templates/testcases/search.html:74
msgid "include child test plans"
msgstr "包含子測試計劃"

#: tcms/testcases/templates/testcases/search.html:112
#: tcms/testcases/templates/testcases/search.html:165
#: tcms/testplans/templates/testplans/get.html:216
#: tcms/testruns/templates/testruns/get.html:262
msgid "Component"
msgstr "元件/組件"

#: tcms/testcases/templates/testcases/search.html:128
msgid "Text"
msgstr "文字"

#: tcms/testcases/templates/testcases/search.html:139
#: tcms/testplans/templates/testplans/search.html:97
#: tcms/testruns/templates/testruns/search.html:30
msgid "Separate multiple values with comma (,)"
msgstr "用逗號分隔變數"

#: tcms/testcases/templates/testcases/search.html:178
msgid "Select"
msgstr "選擇"

#: tcms/testcases/views.py:138 tcms/testplans/views.py:146
#: tcms/testruns/templates/testruns/get.html:458 tcms/testruns/views.py:202
msgid "History"
msgstr "歷史紀錄"

#: tcms/testcases/views.py:250
msgid "TestCase cloning was successful"
msgstr "TestCase 複製成功"

#: tcms/testcases/views.py:281
msgid "At least one TestCase is required"
msgstr "至少需要一個 TestCase"

#: tcms/testplans/templates/testplans/clone.html:5
msgid "Clone TestPlan"
msgstr "複製測試計畫"

#: tcms/testplans/templates/testplans/clone.html:55
msgid "Clone TCs"
msgstr "複製 TCs"

#: tcms/testplans/templates/testplans/clone.html:59
msgid "Clone or link existing TCs into new TP"
msgstr "複製或者將現有 TCs 連結到新的 TP"

#: tcms/testplans/templates/testplans/clone.html:63
msgid "Parent TP"
msgstr "上層 TP"

#: tcms/testplans/templates/testplans/clone.html:67
msgid "Set the source TP as parent of new TP"
msgstr "將原 TP 設置為新 TP 的根目錄"

#: tcms/testplans/templates/testplans/get.html:25
#: tcms/testruns/templates/testruns/get.html:23
msgid "Enter username, email or user ID:"
msgstr "輸入使用者名稱、郵件或 ID:"

#: tcms/testplans/templates/testplans/get.html:26
#: tcms/testruns/templates/testruns/get.html:22
msgid "No rows selected! Please select at least one!"
msgstr "沒有選擇任何列！請至少選擇一列！"

#: tcms/testplans/templates/testplans/get.html:27
#: tcms/testruns/templates/testruns/get.html:24
msgid "Are you sure?"
msgstr "是否確定？"

#: tcms/testplans/templates/testplans/get.html:28
msgid "Cannot create TestRun with unconfirmed test cases"
msgstr "無法使用未確認的測試案例建立測試執行（TestRun）"

#: tcms/testplans/templates/testplans/get.html:29
msgid "Error adding test cases"
msgstr "新增測試案例時發生錯誤"

#: tcms/testplans/templates/testplans/get.html:43
msgid "Show more"
msgstr "顯示更多"

#: tcms/testplans/templates/testplans/get.html:75
msgid "Plan Type"
msgstr "計畫類型"

#: tcms/testplans/templates/testplans/get.html:100
msgid "Test cases"
msgstr "測試案例"

#: tcms/testplans/templates/testplans/get.html:182
#: tcms/testplans/templates/testplans/get.html:421
#: tcms/testplans/templates/testplans/get.html:494
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:10
msgid "Reviewer"
msgstr "審查員"

#: tcms/testplans/templates/testplans/get.html:231
#: tcms/testplans/templates/testplans/get.html:234
msgid "Sort key"
msgstr "排序關鍵字"

#: tcms/testplans/templates/testplans/get.html:244
msgid "Re-order cases"
msgstr "重新排序案例"

#: tcms/testplans/templates/testplans/get.html:254
#: tcms/testruns/templates/testruns/get.html:288
msgid "Search and add test cases"
msgstr "搜尋並新增測試案例"

#: tcms/testplans/templates/testplans/get.html:263
#: tcms/testruns/templates/testruns/get.html:298
msgid "Advanced search"
msgstr "進階搜尋"

#: tcms/testplans/templates/testplans/get.html:286
msgid "Active test runs"
msgstr "已啟用的測試執行"

#: tcms/testplans/templates/testplans/get.html:308
msgid "More"
msgstr "更多"

#: tcms/testplans/templates/testplans/get.html:310
msgid "Inactive"
msgstr "未啟用"

#: tcms/testplans/templates/testplans/get.html:444
#: tcms/testruns/templates/testruns/get.html:494
msgid "No attachments"
msgstr "沒有附件。"

#: tcms/testplans/templates/testplans/get.html:469
#: tcms/testruns/templates/testruns/get.html:483
msgid "Comments"
msgstr "評論"

#: tcms/testplans/templates/testplans/mutable.html:10
msgid "Edit TestPlan"
msgstr "編輯測試計畫"

#: tcms/testplans/templates/testplans/mutable.html:12
msgid "Create new TestPlan"
msgstr "建立新的測試計畫"

#: tcms/testplans/templates/testplans/mutable.html:82
msgid "Parent ID"
msgstr "父層ID:"

#: tcms/testplans/templates/testplans/mutable.html:100
msgid "Enter to assign; Backspace + Enter to clear"
msgstr "按 Enter 指派；按 Backspace + Enter 清除"

#: tcms/testplans/templates/testplans/mutable.html:110
msgid "Test plan document:"
msgstr "測試計畫文件:"

#: tcms/testplans/templates/testplans/mutable.html:119
msgid "Notify:"
msgstr "通知:"

#: tcms/testplans/templates/testplans/mutable.html:127
msgid "TestCase author"
msgstr "測試案例作者"

#: tcms/testplans/templates/testplans/mutable.html:141
msgid "Notify when:"
msgstr "以下情況通知我:"

#: tcms/testplans/templates/testplans/mutable.html:145
msgid "TestPlan is updated"
msgstr "測試計畫已更新"

#: tcms/testplans/templates/testplans/mutable.html:151
msgid "Test cases are updated"
msgstr "測試案例已更新"

#: tcms/testplans/templates/testplans/mutable.html:165
#: tcms/testplans/templates/testplans/search.html:41
msgid "Active"
msgstr "啟動"

#: tcms/testplans/templates/testplans/search.html:12
msgid "Some child test plans do not match search criteria"
msgstr "部分子測試計劃不符合搜尋條件"

#: tcms/testplans/templates/testplans/search.html:20
msgid "Test plan name"
msgstr "測試計畫名稱"

#: tcms/testruns/admin.py:32
msgid "Permission denied: TestRun does not belong to you"
msgstr "許可權被拒絕: 此TestRun 不屬於您"

#: tcms/testruns/admin.py:39
msgid "For more information about customizing test execution statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-execution-statuses\">\n"
"        the documentation</a>!"
msgstr "要了解更多關於自定義測試執行狀態的資訊，請參考\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-execution-statuses\">\n"
"        文檔</a>！"

#: tcms/testruns/admin.py:95
msgid "1 negative, 1 neutral & 1 positive status required!"
msgstr "需要 1 個負面、1 個中立及 1 個正面狀態！"

#: tcms/testruns/admin.py:106
msgid "Edit parameters"
msgstr "編輯參數"

#: tcms/testruns/forms.py:39
msgid "Full"
msgstr "Full"

#: tcms/testruns/forms.py:40
msgid "Pairwise"
msgstr "成對的"

#: tcms/testruns/models.py:230
msgid "Test execution statuses"
msgstr "測試執行狀態"

#: tcms/testruns/templates/testruns/get.html:25
msgid "Unconfirmed test cases were not added"
msgstr "未確認的測試案例未被新增"

#: tcms/testruns/templates/testruns/get.html:26
msgid "Type 0 or 1"
msgstr "輸入 0 或 1"

#: tcms/testruns/templates/testruns/get.html:27
msgid "Comment"
msgstr "附註"

#: tcms/testruns/templates/testruns/get.html:60
#: tcms/testruns/templates/testruns/mutable.html:29
#: tcms/testruns/templates/testruns/search.html:67
#: tcms/testruns/templates/testruns/search.html:188
msgid "Manager"
msgstr "管理者"

#: tcms/testruns/templates/testruns/get.html:73
#: tcms/testruns/templates/testruns/mutable.html:110
#: tcms/testruns/templates/testruns/search.html:127
msgid "Planned start"
msgstr "預計開始時間"

#: tcms/testruns/templates/testruns/get.html:84
msgid "Start"
msgstr "開始"

#: tcms/testruns/templates/testruns/get.html:91
#: tcms/testruns/templates/testruns/mutable.html:123
#: tcms/testruns/templates/testruns/search.html:147
msgid "Planned stop"
msgstr "預計結束時間"

#: tcms/testruns/templates/testruns/get.html:102
msgid "Stop"
msgstr "停止"

#: tcms/testruns/templates/testruns/get.html:139
#: tcms/testruns/templates/testruns/mutable.html:160
msgid "Environment"
msgstr "環境"

#: tcms/testruns/templates/testruns/get.html:182
msgid "Update text version"
msgstr "更新文件版本"

#: tcms/testruns/templates/testruns/get.html:217
msgid "Add comment"
msgstr "新增評論"

#: tcms/testruns/templates/testruns/get.html:228
#: tcms/testruns/templates/testruns/get.html:331
#: tcms/testruns/templates/testruns/get.html:568
msgid "Add hyperlink"
msgstr "新增超連結"

#: tcms/testruns/templates/testruns/get.html:279
msgid "Mine"
msgstr "我的"

#: tcms/testruns/templates/testruns/get.html:280
msgid "All"
msgstr "全部"

#: tcms/testruns/templates/testruns/get.html:306
msgid "records"
msgstr "紀錄"

#: tcms/testruns/templates/testruns/get.html:332
#: tcms/testruns/templates/testruns/get.html:607
#: tcms/testruns/templates/testruns/get.html:624
msgid "Report bug"
msgstr "回報Bug"

#: tcms/testruns/templates/testruns/get.html:339
msgid "Test case is not part of parent test plan"
msgstr "測試案例不屬於父測試計劃"

#: tcms/testruns/templates/testruns/get.html:371
msgid "Assigned to"
msgstr "已指派給"

#: tcms/testruns/templates/testruns/get.html:379
#: tcms/testruns/templates/testruns/get.html:381
msgid "Last bug"
msgstr ""

#: tcms/testruns/templates/testruns/get.html:423
msgid "Text version"
msgstr "文件版本"

#: tcms/testruns/templates/testruns/get.html:431
msgid "Bugs and hyperlinks"
msgstr "Bug與超連結"

#: tcms/testruns/templates/testruns/get.html:585
msgid "Is a defect"
msgstr "是一個缺陷?"

#: tcms/testruns/templates/testruns/get.html:592
#: tcms/testruns/templates/testruns/get.html:623
msgid "Cancel"
msgstr "取消"

#: tcms/testruns/templates/testruns/get.html:612
msgid "Issue Tracker"
msgstr "問題追蹤"

#: tcms/testruns/templates/testruns/mutable.html:7
msgid "Edit TestRun"
msgstr "編輯測試執行"

#: tcms/testruns/templates/testruns/mutable.html:9
msgid "Clone TestRun"
msgstr "複製測試執行"

#: tcms/testruns/templates/testruns/mutable.html:180
msgid "Affects only test cases with parameters"
msgstr "只影響有參數的測試案例"

#: tcms/testruns/templates/testruns/mutable.html:194
msgid "more information"
msgstr "更多資訊"

#: tcms/testruns/templates/testruns/mutable.html:218
msgid "Selected TestCase(s):"
msgstr "已選擇測試案例"

#: tcms/testruns/templates/testruns/mutable.html:221
#, python-format
msgid "%(count)s of the pre-selected test cases is not CONFIRMED and will not be cloned!\n"
"See test plan for more details!"
msgstr "預先選定測試案例中的%(count)s 不是 CONFIRMED，將不會被複製！\n"
"查看測試計畫以了解更多詳情！"

#: tcms/testruns/templates/testruns/search.html:21
msgid "Plan ID"
msgstr "計畫ID"

#: tcms/testruns/templates/testruns/search.html:23
msgid "TestPlan ID"
msgstr "測試計畫ID"

#: tcms/testruns/templates/testruns/search.html:79
msgid "Running"
msgstr "運行中"

#: tcms/testruns/templates/testruns/search.html:86
#: tcms/testruns/templates/testruns/search.html:186
msgid "Start date"
msgstr "開始日期"

#: tcms/testruns/templates/testruns/search.html:106
#: tcms/testruns/templates/testruns/search.html:187
msgid "Stop date"
msgstr "結束日期"

#: tcms/testruns/views.py:273
msgid "Clone of "
msgstr "複製於 "

#: testcases.TestCaseStatus/name:1
msgid "PROPOSED"
msgstr "提出"

#: testcases.TestCaseStatus/name:3
msgid "DISABLED"
msgstr "禁用"

#: testcases.TestCaseStatus/name:4
msgid "NEED_UPDATE"
msgstr "需要更新"

#: testruns.TestExecutionStatus/name:1
msgid "IDLE"
msgstr "閒置"

#: testruns.TestExecutionStatus/name:2
msgid "RUNNING"
msgstr "運行"

#: testruns.TestExecutionStatus/name:3
msgid "PAUSED"
msgstr "暫停"

#: testruns.TestExecutionStatus/name:4
msgid "PASSED"
msgstr "通過"

#: testruns.TestExecutionStatus/name:5
msgid "FAILED"
msgstr "失敗"

#: testruns.TestExecutionStatus/name:6
msgid "BLOCKED"
msgstr "封鎖"

#: testruns.TestExecutionStatus/name:7
msgid "ERROR"
msgstr "錯誤"

#: testruns.TestExecutionStatus/name:8
msgid "WAIVED"
msgstr "放棄"

#: tcms_github_app/admin.py:122
#, python-format
msgid "For additional configuration see\n"
"<a href=\"%s\">GitHub</a>"
msgstr "更多配置請參考\n"
"<a href=\"%s\">GitHub</a>"

#: tcms_github_app/menu.py:11
msgid "GitHub integration"
msgstr "GitHub 整合"

#: tcms_github_app/menu.py:12
msgid "Resync"
msgstr "重新同步"

#: tcms_github_app/menu.py:13
msgid "Settings"
msgstr "設定"

#: tcms_github_app/middleware.py:41
#, python-format
msgid "Unconfigured GitHub App %d"
msgstr "未設置 GitHub 應用 %d"

#: tcms_github_app/utils.py:274
#, python-format
msgid "%s was imported from GitHub"
msgstr "%s 已從 GitHub 匯入"

#: tcms_github_app/utils.py:278
#, python-format
msgid "%s already exists"
msgstr "%s 已存在"

#: tcms_github_app/views.py:48
#, python-format
msgid "You have not logged-in via GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "你尚未通過 GitHub 帳號登入！ <a href=\"%s\">點擊此處</a>！"

#: tcms_github_app/views.py:62
#, python-format
msgid "You have not installed Kiwi TCMS into your GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "你還沒有在 GitHub 帳號中安裝 Kiwi TCMS ！ <a href=\"%s\">點擊此處</a>！"

#: tcms_github_app/views.py:76
msgid "Multiple GitHub App installations detected! See below:"
msgstr "檢查到多個 GitHub 應用程式安裝！請參考下列："

#: tcms_github_app/views.py:85
#, python-format
msgid "Edit GitHub App <a href=\"%s\">%s</a>"
msgstr "Edit GitHub App <a href=\"%s\">%s</a>"

#: tcms_github_app/views.py:102
#, python-format
msgid "Cannot find GitHub App installation for tenant \"%s\""
msgstr "找不到用戶「%s」的 GitHub App 安裝資料"

#: tcms_github_app/views.py:111
msgid "Multiple GitHub App installations detected!"
msgstr "檢查到多個 GitHub 應用程式安裝！"

#: tcms_github_marketplace/menu.py:11
msgid "Subscriptions"
msgstr "訂閱"

#: tcms_github_marketplace/templates/tcms_github_marketplace/email/exit_poll.txt:1
msgid "Thank you for using Kiwi TCMS via a paid subscription.\n"
"We're sorry to see you go but we'd like to know why so we can improve in the future!\n\n"
"You can share your feedback at https://forms.gle/RJhFengbjN9C6wtUA\n\n"
"Thank you!"
msgstr "感謝您使用付費訂閱的 Kiwi TCMS。\n"
"很遺憾看到您要離開，但我們希望了解原因，以便未來能持續改進！\n\n"
"您可以在以下連結分享您的意見：\n"
"https://forms.gle/RJhFengbjN9C6wtUA\n\n"
"謝謝您！"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:6
msgid "Tenant subscriptions"
msgstr "租用戶訂閱"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:15
msgid "You can access the following tenants"
msgstr "你可以訪問以下租用戶"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:20
msgid "Tenant"
msgstr "租用戶"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:31
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:90
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:30
msgid "Organization"
msgstr "組織"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:44
msgid "Docker credentials"
msgstr "Docker 憑證"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:61
msgid "Private containers instructions"
msgstr "私有容器指令"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:75
msgid "You own the following tenants"
msgstr "你擁有下列租用戶"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:97
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:98
msgid "Price"
msgstr "價格"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:102
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:103
msgid "Subscription type"
msgstr "訂閱類型"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:107
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:108
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:8
msgid "Paid until"
msgstr "付費至"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:114
msgid "Cancel subscription"
msgstr "取消訂閱"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:127
msgid "You don't own any tenants"
msgstr "你沒擁有任何租用戶"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:131
msgid "Subscribe via FastSpring"
msgstr "透過 FastSpring 訂閱"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:147
msgid "Transaction history"
msgstr "交易記錄"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:169
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:170
msgid "Sender"
msgstr "寄件者"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:174
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:175
msgid "Vendor"
msgstr "供應商"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:179
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:180
msgid "Received on"
msgstr "已接收"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:4
msgid "Extra emails"
msgstr "附加電子郵件"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:14
msgid "Kiwi TCMS will try to match recurring billing events against tenant.owner.email + tenant.extra_emails"
msgstr "Kiwi TCMS 將嘗試將定期帳單事件與租戶擁有者的電子郵件（tenant.owner.email）及額外電子郵件（tenant.extra_emails）進行比對"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:17
msgid "Separate by comma (,), semi-colon (;) or white space ( )"
msgstr "請以逗號（,）、分號（;）或空白（ ）分隔"

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:44
msgid "Private Tenant Warning"
msgstr "私人租用戶警告"

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:49
msgid "You are about to create a Private Tenant for Kiwi TCMS.\n"
"It will take a few minutes until your DB schema is ready!\n"
"After clicking the 'Save' button <strong>do not</strong> close or refresh this page!<br>\n"
"You will be redirected to your new tenant when the creation process is complete!\n"
"If you see a 500 Internal Server Error page please contact\n"
"<a href=\"mailto:<EMAIL>\"><EMAIL></a> immediately!"
msgstr "你將為Kiwi TCMS建立一個私人租用戶\n"
"你的資料庫架構準備就緒將需要幾分鐘！\n"
"點擊“存擋”按鈕後，<strong>請勿</strong>關閉或更新此頁面！<br>\n"
"建立過程完成後，你將重新導向到新的租用戶！\n"
"如果看到500 Internal Server Erro頁面，請聯繫\n"
"立即<a href=\"mailto:<EMAIL>\"> <EMAIL> </a>！"

#: tcms_github_marketplace/utils.py:95
msgid "Kiwi TCMS Subscription Exit Poll"
msgstr "Kiwi TCMS 訂閱退出調查"

#: tcms_github_marketplace/views.py:567
msgid "Kiwi TCMS subscription notification"
msgstr "Kiwi TCMS 訂閱通知"

#: tcms_github_marketplace/views.py:749
msgid "mo"
msgstr "月"

#: tcms_github_marketplace/views.py:752
msgid "yr"
msgstr "年"

#: tcms_enterprise/pipeline.py:17
msgid "Email address is required"
msgstr "電子郵件位址為必填"

#: tcms_enterprise/templates/registration/custom_login.html:10
msgid "or Continue With"
msgstr "或者繼續"

#: tcms_settings_dir/enterprise.py:19
msgid "Legal information"
msgstr "法律資訊"

#: tcms_tenants/admin.py:55 tcms_tenants/admin.py:62
#: tcms_tenants/middleware.py:35
msgid "Unauthorized"
msgstr "未授權"

#: tcms_tenants/admin.py:86
msgid "Existing username, email or user ID"
msgstr "已存在的使用者名稱、郵件或 ID"

#: tcms_tenants/admin.py:159
msgid "Full name"
msgstr "全名"

#: tcms_tenants/forms.py:30
msgid "Invalid string"
msgstr "無效的字串"

#: tcms_tenants/menu.py:15
msgid "Create"
msgstr "建立"

#: tcms_tenants/menu.py:20
#: tcms_tenants/templates/tcms_tenants/invite_users.html:17
msgid "Invite users"
msgstr "邀請使用者"

#: tcms_tenants/menu.py:21
msgid "Authorized users"
msgstr "已授權的使用者"

#: tcms_tenants/middleware.py:59
msgid "Unpaid"
msgstr "未付款"

#: tcms_tenants/middleware.py:70
msgid "Tenant expires soon"
msgstr "租戶即將到期"

#: tcms_tenants/templates/tcms_tenants/email/invite_user.txt:1
#, python-format
msgid "Dear tester,\n"
"%(invited_by)s has invited you to join their Kiwi TCMS tenant at\n"
"%(tenant_url)s\n\n"
"In case you have never logged in before an account was created for you\n"
"automatically. You can login with a social account which has the same email\n"
"address or go to %(password_reset_url)s to reset your password.\n"
"The password reset email message also contains your username!"
msgstr "親愛的測試者，\n"
"%(invited_by)s 邀請您加入他們的 Kiwi TCMS 租戶，網址為：\n"
"%(tenant_url)s\n\n"
"如果您之前從未登入過，系統已自動為您建立帳號。\n"
"您可以使用與該電子郵件相同的社交帳號登入，\n"
"或前往 %(password_reset_url)s 重設密碼。\n"
"密碼重設的郵件內容也會包含您的使用者名稱！"

#: tcms_tenants/templates/tcms_tenants/email/new.txt:1
#, python-format
msgid "Your Kiwi TCMS tenant was created at:\n"
"%(tenant_url)s\n\n"
"If you have troubles please contact support!"
msgstr "你的 Kiwi TCMS 訂閱用戶建立於：\n"
"%(tenant_url)s\n\n"
"如果你遇到問題，請聯絡技術支援！"

#: tcms_tenants/templates/tcms_tenants/invite_users.html:28
msgid "Email"
msgstr "郵件"

#: tcms_tenants/templates/tcms_tenants/new.html:18
msgid "New tenant"
msgstr "新訂閱用戶"

#: tcms_tenants/templates/tcms_tenants/new.html:35
msgid "Company, team or project name"
msgstr "公司、團隊或項目名稱"

#: tcms_tenants/templates/tcms_tenants/new.html:43
msgid "Schema"
msgstr "方案"

#: tcms_tenants/templates/tcms_tenants/new.html:56
msgid "Validation pattern"
msgstr "驗證模式"

#: tcms_tenants/templates/tcms_tenants/new.html:61
msgid "Publicly readable"
msgstr "公開可讀"

#: tcms_tenants/templates/tcms_tenants/new.html:80
msgid "Tenant logo"
msgstr "租戶商標"

#: tcms_tenants/utils.py:66
msgid "Schema name already in use"
msgstr "方案名稱已被使用"

#: tcms_tenants/utils.py:170
msgid "New Kiwi TCMS tenant created"
msgstr "新的 Kiwi TCMS 訂閱用戶已建立"

#: tcms_tenants/utils.py:230
#, python-brace-format
msgid "User {user.username} added to tenant group {group.name}"
msgstr "使用者 {user.username} 已被加入租戶群組 {group.name}"

#: tcms_tenants/utils.py:262
msgid "Invitation to join Kiwi TCMS"
msgstr "邀請加入 Kiwi TCMS"

#: tcms_tenants/views.py:84
msgid "Only super-user and tenant owner are allowed to edit tenant properties"
msgstr "只有超級使用者和租戶擁有者可以編輯租戶屬性"

#: tcms_tenants/views.py:102
msgid "Edit tenant"
msgstr "編輯租戶"

#: tcms_tenants/views.py:153
msgid "Only users who are authorized for this tenant can invite others"
msgstr "只有被授權使用此租戶的使用者才能邀請他人"

#: tenant_groups/admin.py:30
msgid "users"
msgstr "使用者"

#: tenant_groups/models.py:34
msgid "name"
msgstr "名稱"

#: tenant_groups/models.py:37
msgid "permissions"
msgstr "權限"

#: tenant_groups/models.py:47
msgid "group"
msgstr "群組"

#: tenant_groups/models.py:48
msgid "groups"
msgstr "群組"

#: trackers_integration/menu.py:4
msgid "Personal API tokens"
msgstr "個人 API Tokens"
