# Generated by Django 3.1.4 on 2020-12-25 12:37

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("testcases", "0017_rename_related_names"),
        ("testruns", "0009_remove_autofield"),
    ]

    operations = [
        migrations.AlterField(
            model_name="testexecution",
            name="assignee",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=models.deletion.CASCADE,
                related_name="execution_assignee",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="testexecution",
            name="case",
            field=models.ForeignKey(
                on_delete=models.deletion.CASCADE,
                related_name="executions",
                to="testcases.testcase",
            ),
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="run",
            field=models.ForeignKey(
                on_delete=models.deletion.CASCADE,
                related_name="executions",
                to="testruns.testrun",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="testexecution",
            name="tested_by",
            field=models.Foreign<PERSON>ey(
                blank=True,
                null=True,
                on_delete=models.deletion.CASCADE,
                related_name="execution_tester",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
