msgid ""
msgstr ""
"Project-Id-Version: kiwitcms\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-30 12:22+0000\n"
"PO-Revision-Date: 2025-07-30 13:13\n"
"Last-Translator: \n"
"Language-Team: Chinese Simplified\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: kiwitcms\n"
"X-Crowdin-Project-ID: 295734\n"
"X-Crowdin-Language: zh-CN\n"
"X-Crowdin-File: /master/tcms/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 19\n"

#: tcms/bugs/forms.py:33
msgid "Description of problem:\n\n\n"
"How often reproducible:\n\n\n"
"Steps to Reproduce:\n"
"1.\n"
"2.\n"
"3.\n\n"
"Actual results:\n\n\n"
"Expected results:\n\n\n"
"Additional info:"
msgstr "问题描述：\n\n\n"
"重现频率：\n\n\n"
"重现步骤：\n"
"1、\n"
"2、\n"
"3、\n\n"
"实际结果：\n\n\n"
"预期结果：\n\n\n"
"附加信息："

#: tcms/bugs/models.py:24 tcms/bugs/templates/bugs/get.html:50
#: tcms/bugs/templates/bugs/mutable.html:27
#: tcms/bugs/templates/bugs/search.html:18
#: tcms/bugs/templates/bugs/search.html:108
msgid "Severity"
msgstr "严重程度"

#: tcms/bugs/templates/bugs/get.html:37 tcms/bugs/templates/bugs/search.html:89
#: tcms/issuetracker/kiwitcms.py:49 tcms/templates/include/bug_details.html:3
msgid "Open"
msgstr "打开"

#: tcms/bugs/templates/bugs/get.html:39 tcms/issuetracker/kiwitcms.py:49
#: tcms/templates/include/bug_details.html:3
msgid "Closed"
msgstr "已关闭"

#: tcms/bugs/templates/bugs/get.html:59 tcms/bugs/templates/bugs/search.html:79
#: tcms/bugs/templates/bugs/search.html:114
#: tcms/templates/include/bug_details.html:7
msgid "Reporter"
msgstr "报告人"

#: tcms/bugs/templates/bugs/get.html:64
#: tcms/bugs/templates/bugs/mutable.html:94
#: tcms/bugs/templates/bugs/search.html:84
#: tcms/bugs/templates/bugs/search.html:115
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:36
#: tcms/templates/include/bug_details.html:10
#: tcms/testruns/templates/testruns/get.html:207
#: tcms/testruns/templates/testruns/get.html:267
msgid "Assignee"
msgstr "经办人"

#: tcms/bugs/templates/bugs/get.html:75
#: tcms/bugs/templates/bugs/mutable.html:45
#: tcms/bugs/templates/bugs/search.html:47
#: tcms/bugs/templates/bugs/search.html:111
#: tcms/core/templates/dashboard.html:53 tcms/management/admin.py:107
#: tcms/telemetry/templates/telemetry/include/filters.html:8
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:29
#: tcms/templates/include/bug_details.html:13
#: tcms/testcases/templates/testcases/get.html:44
#: tcms/testcases/templates/testcases/get.html:170
#: tcms/testcases/templates/testcases/mutable.html:43
#: tcms/testcases/templates/testcases/search.html:51
#: tcms/testplans/templates/testplans/clone.html:21
#: tcms/testplans/templates/testplans/get.html:64
#: tcms/testplans/templates/testplans/mutable.html:33
#: tcms/testplans/templates/testplans/search.html:50
#: tcms/testplans/templates/testplans/search.html:117
#: tcms/testruns/templates/testruns/get.html:45
#: tcms/testruns/templates/testruns/mutable.html:46
#: tcms/testruns/templates/testruns/search.html:35
#: tcms/testruns/templates/testruns/search.html:183
msgid "Product"
msgstr "产品"

#: tcms/bugs/templates/bugs/get.html:80
#: tcms/bugs/templates/bugs/mutable.html:60
#: tcms/bugs/templates/bugs/search.html:57
#: tcms/bugs/templates/bugs/search.html:112
#: tcms/telemetry/templates/telemetry/include/filters.html:17
#: tcms/templates/include/bug_details.html:15 tcms/templates/navbar.html:71
#: tcms/testplans/templates/testplans/clone.html:36
#: tcms/testplans/templates/testplans/get.html:70
#: tcms/testplans/templates/testplans/mutable.html:48
#: tcms/testplans/templates/testplans/search.html:60
#: tcms/testplans/templates/testplans/search.html:118
#: tcms/testruns/templates/testruns/get.html:50
#: tcms/testruns/templates/testruns/search.html:45
#: tcms/testruns/templates/testruns/search.html:184
msgid "Version"
msgstr "版本"

#: tcms/bugs/templates/bugs/get.html:85
#: tcms/bugs/templates/bugs/mutable.html:78
#: tcms/bugs/templates/bugs/search.html:67
#: tcms/bugs/templates/bugs/search.html:113 tcms/management/models.py:132
#: tcms/telemetry/templates/telemetry/include/filters.html:25
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:30
#: tcms/templates/include/bug_details.html:17
#: tcms/testruns/templates/testruns/get.html:55
#: tcms/testruns/templates/testruns/get.html:414
#: tcms/testruns/templates/testruns/mutable.html:89
#: tcms/testruns/templates/testruns/search.html:55
#: tcms/testruns/templates/testruns/search.html:185
msgid "Build"
msgstr "构建"

#: tcms/bugs/templates/bugs/get.html:119
msgid "commented on"
msgstr "评论于"

#: tcms/bugs/templates/bugs/get.html:149
msgid "Reopen"
msgstr "重新打开"

#: tcms/bugs/templates/bugs/get.html:151
#: tcms/bugs/templates/bugs/mutable.html:114
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:78
#: tcms/testcases/templates/testcases/mutable.html:263
#: tcms/testplans/templates/testplans/get.html:477
#: tcms/testplans/templates/testplans/mutable.html:173
#: tcms/testruns/templates/testruns/get.html:480
#: tcms/testruns/templates/testruns/get.html:593
#: tcms/testruns/templates/testruns/mutable.html:210
msgid "Save"
msgstr "保存"

#: tcms/bugs/templates/bugs/get.html:153
msgid "Close"
msgstr "关闭"

#: tcms/bugs/templates/bugs/mutable.html:20
#: tcms/bugs/templates/bugs/search.html:13
#: tcms/bugs/templates/bugs/search.html:109
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:27
#: tcms/testcases/templates/testcases/mutable.html:24
#: tcms/testcases/templates/testcases/search.html:13
#: tcms/testcases/templates/testcases/search.html:162
#: tcms/testplans/templates/testplans/get.html:212
#: tcms/testplans/templates/testplans/get.html:487
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:3
#: tcms/testruns/templates/testruns/get.html:251
#: tcms/testruns/templates/testruns/get.html:258
#: tcms/testruns/templates/testruns/get.html:261
#: tcms/testruns/templates/testruns/mutable.html:23
#: tcms/testruns/templates/testruns/mutable.html:229
#: tcms/testruns/templates/testruns/search.html:14
#: tcms/testruns/templates/testruns/search.html:181
msgid "Summary"
msgstr "总结"

#: tcms/bugs/templates/bugs/mutable.html:28
msgid "add new Severity"
msgstr "添加新的严重程度"

#: tcms/bugs/templates/bugs/mutable.html:46
#: tcms/testcases/templates/testcases/mutable.html:44
#: tcms/testplans/templates/testplans/clone.html:22
#: tcms/testplans/templates/testplans/mutable.html:34
#: tcms/testruns/templates/testruns/mutable.html:48
msgid "add new Product"
msgstr "添加新产品"

#: tcms/bugs/templates/bugs/mutable.html:62
#: tcms/testplans/templates/testplans/clone.html:38
#: tcms/testplans/templates/testplans/mutable.html:50
msgid "add new Version"
msgstr "添加新版本"

#: tcms/bugs/templates/bugs/mutable.html:81
#: tcms/bugs/templates/bugs/mutable.html:82
#: tcms/testruns/templates/testruns/mutable.html:93
#: tcms/testruns/templates/testruns/mutable.html:94
msgid "add new Build"
msgstr "添加新的构建"

#: tcms/bugs/templates/bugs/mutable.html:98
#: tcms/testruns/templates/testruns/mutable.html:32
#: tcms/testruns/templates/testruns/mutable.html:39
msgid "Username or email"
msgstr "用户名或邮箱"

#: tcms/bugs/templates/bugs/search.html:5 tcms/settings/common.py:414
msgid "Search Bugs"
msgstr "搜索缺陷"

#: tcms/bugs/templates/bugs/search.html:28
#: tcms/testcases/templates/testcases/search.html:19
#: tcms/testplans/templates/testplans/search.html:24
msgid "Created"
msgstr "创建时间："

#: tcms/bugs/templates/bugs/search.html:31
#: tcms/telemetry/templates/telemetry/include/filters.html:53
#: tcms/testcases/templates/testcases/search.html:22
#: tcms/testplans/templates/testplans/search.html:27
#: tcms/testruns/templates/testruns/search.html:90
#: tcms/testruns/templates/testruns/search.html:110
#: tcms/testruns/templates/testruns/search.html:131
#: tcms/testruns/templates/testruns/search.html:151
msgid "Before"
msgstr "在之前"

#: tcms/bugs/templates/bugs/search.html:37
#: tcms/telemetry/templates/telemetry/include/filters.html:43
#: tcms/testcases/templates/testcases/search.html:28
#: tcms/testplans/templates/testplans/search.html:33
#: tcms/testruns/templates/testruns/search.html:97
#: tcms/testruns/templates/testruns/search.html:117
#: tcms/testruns/templates/testruns/search.html:138
#: tcms/testruns/templates/testruns/search.html:158
msgid "After"
msgstr "在之后"

#: tcms/bugs/templates/bugs/search.html:81
#: tcms/bugs/templates/bugs/search.html:86
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:45
#: tcms/templates/registration/login.html:17
#: tcms/templates/registration/registration_form.html:15
#: tcms/testcases/templates/testcases/search.html:124
#: tcms/testplans/templates/testplans/search.html:84
#: tcms/testplans/templates/testplans/search.html:90
#: tcms/testruns/templates/testruns/get.html:524
#: tcms/testruns/templates/testruns/search.html:69
#: tcms/testruns/templates/testruns/search.html:75
msgid "Username"
msgstr "用户名"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:39
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "Yes"
msgstr "是的"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:42
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "No"
msgstr "不"

#: tcms/bugs/templates/bugs/search.html:97
#: tcms/testcases/templates/testcases/search.html:150
#: tcms/testplans/templates/testplans/get.html:221
#: tcms/testplans/templates/testplans/search.html:103
#: tcms/testruns/templates/testruns/get.html:273
#: tcms/testruns/templates/testruns/search.html:170
msgid "Search"
msgstr "搜索"

#: tcms/bugs/templates/bugs/search.html:107
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:26
#: tcms/testcases/templates/testcases/get.html:166
#: tcms/testcases/templates/testcases/search.html:144
#: tcms/testcases/templates/testcases/search.html:161
#: tcms/testplans/templates/testplans/search.html:114
#: tcms/testruns/templates/testruns/search.html:180
msgid "ID"
msgstr "ID"

#: tcms/bugs/templates/bugs/search.html:110
#: tcms/templates/include/bug_details.html:5
#: tcms/testcases/templates/testcases/search.html:163
#: tcms/testplans/templates/testplans/search.html:116
#: tcms/testruns/templates/testruns/mutable.html:231
msgid "Created on"
msgstr "创建于"

#: tcms/bugs/views.py:42 tcms/testcases/views.py:130
#: tcms/testplans/templates/testplans/get.html:380 tcms/testplans/views.py:143
#: tcms/testruns/views.py:194 tcms/testruns/views.py:308
msgid "Edit"
msgstr "编辑"

#: tcms/bugs/views.py:45 tcms/testcases/views.py:143
#: tcms/testplans/views.py:151 tcms/testruns/views.py:207
#: tcms/testruns/views.py:316
msgid "Object permissions"
msgstr "对象权限"

#: tcms/bugs/views.py:50
#: tcms/templates/include/comments_for_object_template.html:10
#: tcms/templates/include/properties_card.html:32
#: tcms/templates/include/properties_card.html:47 tcms/testcases/views.py:151
#: tcms/testplans/templates/testplans/get.html:193
#: tcms/testplans/templates/testplans/get.html:384 tcms/testplans/views.py:159
#: tcms/testruns/templates/testruns/get.html:238
#: tcms/testruns/templates/testruns/get.html:450 tcms/testruns/views.py:215
#: tcms/testruns/views.py:324
msgid "Delete"
msgstr "删除"

#: tcms/bugs/views.py:68 tcms/settings/common.py:400
msgid "New Bug"
msgstr "新建缺陷"

#: tcms/bugs/views.py:188
msgid "Edit bug"
msgstr "编辑缺陷"

#: tcms/bugs/views.py:231
msgid "*bug closed*"
msgstr "*缺陷已关闭*"

#: tcms/bugs/views.py:235
msgid "*bug reopened*"
msgstr "*缺陷重新打开*"

#: tcms/core/history.py:52
#, python-format
msgid "UPDATE: %(model_name)s #%(pk)d - %(title)s"
msgstr "更新: %(model_name)s #%(pk)d - %(title)s"

#: tcms/core/history.py:62
#, python-format
msgid "Updated on %(history_date)s\n"
"Updated by %(username)s\n\n"
"%(diff)s\n\n"
"For more information:\n"
"%(instance_url)s"
msgstr "更新于 %(history_date)s\n"
"由 %(username)s 更新\n\n"
"%(diff)s\n\n"
"获取更多信息:\n"
"%(instance_url)s _ 321"

#: tcms/core/templates/dashboard.html:3 tcms/settings/common.py:430
msgid "Dashboard"
msgstr "控制面板"

#: tcms/core/templates/dashboard.html:8
#: tcms/testruns/templates/testruns/get.html:153
msgid "Test executions"
msgstr "测试执行"

#: tcms/core/templates/dashboard.html:15
#, python-format
msgid "%(amount)s%% complete"
msgstr "%(amount)s%% 完毕"

#: tcms/core/templates/dashboard.html:24
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:38
#: tcms/testruns/templates/testruns/get.html:78
msgid "Started at"
msgstr "开始于"

#: tcms/core/templates/dashboard.html:32
#, python-format
msgid "%(total_count)s TestRun(s) or TestCase(s) assigned to you need to be executed.\n"
"Here are the latest %(count)s."
msgstr "分配给您的 %(total_count)s 测试运行(s) 或 TestCase(s) 需要执行。\n"
"这里是最新的 %(count)s。"

#: tcms/core/templates/dashboard.html:36 tcms/core/templates/dashboard.html:80
msgid "SEE ALL"
msgstr "查看全部"

#: tcms/core/templates/dashboard.html:39
msgid "There are no TestRun(s) assigned to you"
msgstr "没有分配给你的测试runs"

#: tcms/core/templates/dashboard.html:46
msgid "Your Test plans"
msgstr "你的测试计划"

#: tcms/core/templates/dashboard.html:52
msgid "TestPlan"
msgstr "测试计划"

#: tcms/core/templates/dashboard.html:54
#: tcms/testcases/templates/testcases/get.html:169
#: tcms/testplans/templates/testplans/mutable.html:66
#: tcms/testplans/templates/testplans/search.html:70
#: tcms/testplans/templates/testplans/search.html:119
msgid "Type"
msgstr "类型"

#: tcms/core/templates/dashboard.html:55
#: tcms/templates/include/tc_executions.html:7
msgid "Executions"
msgstr "执行"

#: tcms/core/templates/dashboard.html:76
#, python-format
msgid "You manage %(total_count)s TestPlan(s), %(disabled_count)s are disabled.\n"
"Here are the latest %(count)s."
msgstr "您管理 %(total_count)s 测试计划，%(disabled_count)s 已禁用。\n"
"这里是最新的 %(count)s。"

#: tcms/core/templates/dashboard.html:83
msgid "There are no TestPlan(s) that belong to you"
msgstr "没有属于你的测试计划(们)"

#: tcms/core/views.py:47
#, python-format
msgid "Base URL is not configured! See <a href=\"%(doc_url)s\">documentation</a> and <a href=\"%(admin_url)s\">change it</a>"
msgstr "基本URL未配置！参见 <a href=\"%(doc_url)s\">documentation</a>  和 <a href=\"%(admin_url)s\">change it</a>"

#: tcms/core/views.py:71
#, python-format
msgid "You have %(unapplied_migration_count)s unapplied migration(s). See <a href=\"%(doc_url)s\">documentation</a>"
msgstr "您有 %(unapplied_migration_count)s 个未应用的迁移(s)。请参阅 <a href=\"%(doc_url)s\">文档</a>"

#: tcms/core/views.py:92
#, python-format
msgid "You are not using a secure connection. See <a href=\"%(doc_url)s\">documentation</a> and enable SSL."
msgstr "您使用的是非安全连接。查看 <a href=\"%(doc_url)s\">文档</a> 并启用 SSL。"

#: tcms/kiwi_attachments/validators.py:10
#, python-brace-format
msgid "File contains forbidden tag: <{tag_name}>"
msgstr "文件包含被禁止的标签<{tag_name}>"

#: tcms/kiwi_attachments/validators.py:92
#, python-brace-format
msgid "File contains forbidden attribute: `{attr_name}`"
msgstr "文件包含被禁止的属性名`{attr_name}`"

#: tcms/kiwi_attachments/validators.py:97
msgid "Uploading executable files is forbidden"
msgstr "被禁止上传可执行文件"

#: tcms/kiwi_auth/admin.py:36 tcms/settings/common.py:443
msgid "Users"
msgstr "用户"

#: tcms/kiwi_auth/admin.py:82
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:5
#: tcms/templates/navbar.html:102
msgid "Reset email address"
msgstr "重置电子邮件地址"

#: tcms/kiwi_auth/admin.py:148
msgid "Personal info"
msgstr "个人信息"

#: tcms/kiwi_auth/admin.py:150
msgid "Permissions"
msgstr "权限"

#: tcms/kiwi_auth/admin.py:187
msgid "This is the last superuser, it cannot be deleted!"
msgstr "这是最后一个超级用户，无法删除！"

#: tcms/kiwi_auth/forms.py:28
msgid "A user with that email already exists."
msgstr "Email已存在，请直接登录或更换Email."

#: tcms/kiwi_auth/forms.py:48
msgid "Please confirm your Kiwi TCMS account email address"
msgstr "请确认您的 Kiwi TCMS 帐户电子邮件地址"

#: tcms/kiwi_auth/forms.py:142
msgid "Email mismatch"
msgstr "电子邮件不匹配"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:14
msgid "Warning"
msgstr "警告"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:19
msgid "After clicking the 'Save' button your account will become <strong>inactive</strong>\n"
"and you will be <strong>logged out</strong>! A confirmation email will be sent to the newly specified address!<br>\n"
"Double check that your new email address is <strong>entered correctly</strong> otherwise\n"
"<strong>you may be left locked out</strong> of your account!\n"
"After following the activation link you will be able to log in as usual!"
msgstr "点击“保存”按钮后，您的账户将会成为 <strong>非激活</strong>\n"
"您将被 <strong>注销</strong>！ 确认邮件将发送到新指定的地址！<br>\n"
"请仔细检查您的新电子邮件地址是 <strong>输入正确的</strong> 否则\n"
"<strong>您的账户会被锁定</strong> ！ \n"
"在关注激活链接后，您将能够像往常一样登录！"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:51
msgid "NOT yourself"
msgstr "不是您本人"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:58
#: tcms/templates/registration/password_reset_form.html:16
#: tcms/templates/registration/registration_form.html:39
msgid "E-mail"
msgstr "电子邮箱"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:66
#: tcms/templates/registration/password_reset_confirm.html:22
#: tcms/templates/registration/registration_form.html:31
msgid "Confirm"
msgstr "确认"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:71
msgid "Please type! Do not copy-and-paste value from previous field!"
msgstr "请输入！不要复制并粘贴上一个字段的值！"

#: tcms/kiwi_auth/views.py:70
msgid "Your account has been created, please check your mailbox for confirmation"
msgstr "您的账户已创建，请检查您的收件箱进行确认"

#: tcms/kiwi_auth/views.py:75
msgid "Your account has been created, but you need an administrator to activate it"
msgstr "您的账户已创建，但您需要管理员来激活它"

#: tcms/kiwi_auth/views.py:80
msgid "Following is the administrator list"
msgstr "以下是管理员列表"

#: tcms/kiwi_auth/views.py:123
msgid "This activation key no longer exists in the database"
msgstr "此激活密钥不再存于数据库中"

#: tcms/kiwi_auth/views.py:129
msgid "This activation key has expired"
msgstr "激活密钥已过期"

#: tcms/kiwi_auth/views.py:141
msgid "Your account has been activated successfully"
msgstr "您的账户已成功激活"

#: tcms/kiwi_auth/views.py:168 tcms/kiwi_auth/views.py:177
#: tcms/kiwi_auth/views.py:196 tcms/kiwi_auth/views.py:205
#, python-format
msgid "You are viewing records from tenant '%s'"
msgstr "您正在从租户%s 查看记录"

#: tcms/kiwi_auth/views.py:256
msgid "Email address has been reset, please check inbox for further instructions"
msgstr "电子邮件地址已重置，请检查收件箱以获取进一步的说明"

#: tcms/management/models.py:58
#: tcms/telemetry/templates/telemetry/testing/breakdown.html:34
msgid "Priorities"
msgstr "优先级"

#: tcms/management/models.py:133
msgid "Builds"
msgstr "构建"

#: tcms/management/models.py:144
#: tcms/testcases/templates/testcases/search.html:136
#: tcms/testplans/templates/testplans/get.html:217
#: tcms/testplans/templates/testplans/search.html:94
#: tcms/testruns/templates/testruns/get.html:263
#: tcms/testruns/templates/testruns/search.html:27
msgid "Tag"
msgstr "标签"

#: tcms/management/models.py:145 tcms/templates/include/tags_card.html:6
#: tcms/testcases/templates/testcases/search.html:171
#: tcms/testplans/templates/testplans/get.html:456
#: tcms/testplans/templates/testplans/search.html:121
#: tcms/testruns/templates/testruns/get.html:353
#: tcms/testruns/templates/testruns/search.html:190
msgid "Tags"
msgstr "标签"

#: tcms/rpc/api/bug.py:69
msgid "Enable reporting to this Issue Tracker by configuring its base_url!"
msgstr "通过配置 base_url 来启用报告到此问题追踪器！"

#: tcms/rpc/api/forms/__init__.py:9
msgid "Invalid date format. Expected YYYY-MM-DD [HH:MM:SS]."
msgstr "日期格式无效。预期为 YYYY-MM-DD [HH:MM:SS]。"

#: tcms/settings/common.py:391
msgid "TESTING"
msgstr "测试"

#: tcms/settings/common.py:393 tcms/testruns/templates/testruns/mutable.html:70
msgid "New Test Plan"
msgstr "新建测试计划"

#: tcms/settings/common.py:395
#: tcms/testcases/templates/testcases/mutable.html:12
#: tcms/testplans/templates/testplans/get.html:131
msgid "New Test Case"
msgstr "新建测试用例"

#: tcms/settings/common.py:397 tcms/testplans/templates/testplans/get.html:121
#: tcms/testruns/templates/testruns/get.html:172
#: tcms/testruns/templates/testruns/mutable.html:11
msgid "New Test Run"
msgstr "新的测试执行"

#: tcms/settings/common.py:407
msgid "SEARCH"
msgstr "搜索"

#: tcms/settings/common.py:409 tcms/testplans/templates/testplans/search.html:5
msgid "Search Test Plans"
msgstr "搜索测试计划"

#: tcms/settings/common.py:410 tcms/testcases/templates/testcases/search.html:5
msgid "Search Test Cases"
msgstr "搜索测试用例"

#: tcms/settings/common.py:411 tcms/testruns/templates/testruns/search.html:5
msgid "Search Test Runs"
msgstr "搜索测试执行"

#: tcms/settings/common.py:412
msgid "Search Test Executions"
msgstr "搜索测试执行"

#: tcms/settings/common.py:421
msgid "TELEMETRY"
msgstr "度量"

#: tcms/settings/common.py:424
msgid "Testing"
msgstr "测试中"

#: tcms/settings/common.py:426
msgid "Breakdown"
msgstr "分解"

#: tcms/settings/common.py:428
msgid "Execution"
msgstr "执行"

#: tcms/settings/common.py:431
#: tcms/testruns/templates/testruns/mutable.html:177
msgid "Matrix"
msgstr "网格视图"

#: tcms/settings/common.py:432
msgid "Trends"
msgstr "趋势"

#: tcms/settings/common.py:435
#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:5
msgid "TestCase health"
msgstr "测试案例健康"

#: tcms/settings/common.py:441
msgid "ADMIN"
msgstr "管理员"

#: tcms/settings/common.py:444
msgid "Groups"
msgstr "组"

#: tcms/settings/common.py:446
msgid "Everything else"
msgstr "管理其他事项"

#: tcms/settings/common.py:449
msgid "MORE"
msgstr ""

#: tcms/settings/common.py:459
msgid "Report an Issue"
msgstr "反馈问题"

#: tcms/settings/common.py:462
msgid "Ask for help on StackOverflow"
msgstr "在StackOverflow上寻求帮助"

#: tcms/settings/common.py:466
msgid "Donate €5 via Open Collective"
msgstr "通过开放集体捐赠5欧元"

#: tcms/settings/common.py:468
msgid "Administration Guide"
msgstr "管理员指南"

#: tcms/settings/common.py:469
msgid "User Guide"
msgstr "用户指南"

#: tcms/settings/common.py:470
msgid "API Help"
msgstr "API 参考"

#: tcms/signals.py:85
msgid "New user awaiting approval"
msgstr "等待审批通过的新用户"

#: tcms/signals.py:163
#, python-format
msgid "NEW: TestRun #%(pk)d - %(summary)s"
msgstr "NEW: 测试执行 #%(pk)d - - %(summary)s"

#: tcms/signals.py:235
#, python-format
msgid "Bug #%(pk)d - %(summary)s"
msgstr "缺陷 #%(pk)d - %(summary)s"

#: tcms/telemetry/api.py:60 testcases.TestCaseStatus/name:2
msgid "CONFIRMED"
msgstr "已确认"

#: tcms/telemetry/api.py:61
msgid "OTHER"
msgstr "其他"

#: tcms/telemetry/api.py:133 tcms/telemetry/api.py:185
#: tcms/telemetry/api.py:191
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:9
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:13
#: tcms/testruns/templates/testruns/get.html:129
msgid "TOTAL"
msgstr "总计"

#: tcms/telemetry/templates/telemetry/include/filters.html:36
#: tcms/testcases/templates/testcases/search.html:68
#: tcms/testplans/templates/testplans/search.html:115
#: tcms/testruns/templates/testruns/get.html:40
#: tcms/testruns/templates/testruns/mutable.html:67
#: tcms/testruns/templates/testruns/search.html:182
msgid "Test plan"
msgstr "测试计划"

#: tcms/telemetry/templates/telemetry/include/filters.html:66
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
#: tcms/testcases/templates/testcases/search.html:142
msgid "Test run"
msgstr "测试运行"

#: tcms/telemetry/templates/telemetry/include/filters.html:70
#: tcms/testruns/templates/testruns/search.html:17
msgid "Test run summary"
msgstr "测试执行总结"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:5
msgid "Testing Breakdown"
msgstr "测试分类"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:14
msgid "Total"
msgstr "总计"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:24
#: tcms/testcases/templates/testcases/get.html:93
#: tcms/testcases/templates/testcases/mutable.html:98
#: tcms/testcases/templates/testcases/search.html:36
#: tcms/testcases/templates/testcases/search.html:168
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testplans/templates/testplans/get.html:489
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:5
#: tcms/testruns/templates/testruns/get.html:264
#: tcms/testruns/templates/testruns/get.html:361
msgid "Automated"
msgstr "自动化的"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:28
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testruns/templates/testruns/get.html:361
msgid "Manual"
msgstr "手动"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:39
#: tcms/testcases/models.py:42
msgid "Categories"
msgstr "分类"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:5
msgid "Execution Dashboard"
msgstr "管理展示仪表盘"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:15
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:15
msgid "Child TPs"
msgstr "子TPs"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:28
#: tcms/templates/include/bug_details.html:3
#: tcms/testcases/templates/testcases/get.html:59
#: tcms/testcases/templates/testcases/mutable.html:74
#: tcms/testcases/templates/testcases/search.html:91
#: tcms/testcases/templates/testcases/search.html:167
#: tcms/testplans/templates/testplans/get.html:149
#: tcms/testplans/templates/testplans/get.html:363
#: tcms/testplans/templates/testplans/get.html:396
#: tcms/testplans/templates/testplans/get.html:488
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:4
#: tcms/testruns/templates/testruns/get.html:189
#: tcms/testruns/templates/testruns/get.html:269
#: tcms/testruns/templates/testruns/get.html:385
#: tcms/testruns/templates/testruns/mutable.html:232
msgid "Status"
msgstr "状态"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:31
#: tcms/testcases/templates/testcases/get.html:208
#: tcms/testplans/templates/testplans/get.html:447
#: tcms/testruns/templates/testruns/get.html:349
msgid "Components"
msgstr "组件"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
#: tcms/testcases/templates/testcases/get.html:33
#: tcms/testcases/templates/testcases/mutable.html:36
#: tcms/testcases/templates/testcases/mutable.html:207
#: tcms/testcases/templates/testcases/search.html:170
#: tcms/testplans/templates/testplans/get.html:175
#: tcms/testplans/templates/testplans/get.html:378
#: tcms/testplans/templates/testplans/get.html:416
#: tcms/testplans/templates/testplans/get.html:493
#: tcms/testplans/templates/testplans/mutable.html:133
#: tcms/testplans/templates/testplans/search.html:88
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:9
#: tcms/testruns/templates/testruns/get.html:66
#: tcms/testruns/templates/testruns/mutable.html:36
#: tcms/testruns/templates/testruns/search.html:73
#: tcms/testruns/templates/testruns/search.html:189
msgid "Default tester"
msgstr "默认测试者"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
msgid "TC"
msgstr "TC"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
msgid "TR"
msgstr "TR"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:37
#: tcms/testruns/templates/testruns/get.html:268
#: tcms/testruns/templates/testruns/get.html:375
msgid "Tested by"
msgstr "测试由"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:39
#: tcms/testruns/templates/testruns/get.html:96
#: tcms/testruns/templates/testruns/get.html:405
#: tcms/testruns/templates/testruns/mutable.html:140
msgid "Finished at"
msgstr "完成于"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:5
msgid "Execution Trends"
msgstr "执行趋势"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:21
msgid "Positive"
msgstr "正向"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:24
msgid "Neutral"
msgstr "中性"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:27
msgid "Negative"
msgstr "负数"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:5
msgid "Execution Matrix"
msgstr "执行矩阵表"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:21
msgid "Order"
msgstr "顺序"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Ascending"
msgstr "升序排列"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Descending"
msgstr "降序排序"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
msgid "Test case"
msgstr "测试情况"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:12
msgid "Most frequently failing test cases"
msgstr "最经常失败的测试实例"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:16
msgid "Test Case"
msgstr "测试用例"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:18
msgid "Failed executions"
msgstr "执行失败的次数"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:19
#, python-format
msgid "%% of failed executions"
msgstr "%% 执行失败"

#: tcms/templates/404.html:5 tcms/templates/404.html:16
msgid "Page not found"
msgstr "页面没找到"

#: tcms/templates/500.html:5 tcms/templates/500.html:16
msgid "Internal Server Error"
msgstr "内部服务器错误"

#: tcms/templates/attachments/add.html:3
msgid "Attachment upload error"
msgstr "附件上传错误"

#: tcms/templates/attachments/delete_link.html:3
msgid "Are you sure you want to delete this attachment?"
msgstr "确定删除这个附件？"

#: tcms/templates/base.html:8
msgid "day"
msgstr "天"

#: tcms/templates/base.html:8
msgid "days"
msgstr "天"

#: tcms/templates/base.html:9
msgid "hour"
msgstr "时"

#: tcms/templates/base.html:9
msgid "hours"
msgstr "时"

#: tcms/templates/base.html:10
msgid "minute"
msgstr "分"

#: tcms/templates/base.html:10
msgid "minutes"
msgstr "分"

#: tcms/templates/base.html:11
msgid "second"
msgstr "秒"

#: tcms/templates/base.html:11
msgid "seconds"
msgstr "秒"

#: tcms/templates/base.html:16 tcms/templates/registration/login.html:46
msgid "the leading open source test case management system"
msgstr "领先的开源测试用例管理系统"

#: tcms/templates/email/confirm_registration.txt:1
#, python-format
msgid "Welcome to Kiwi TCMS!\n\n"
"To confirm email address for username `%(user)s` and activate your account\n"
"please follow this URL:\n"
"%(confirm_url)s\n\n"
"Regards,\n"
"Kiwi TCMS"
msgstr "欢迎使用Kiwi TCMS！为确认用户名%(user)s的电子邮件地址并激活您的账户，请访问此链接：%(confirm_url)s\n\n"
"此致， Kiwi TCMS团队"

#: tcms/templates/email/post_bug_save/email.txt:2
#, python-format
msgid "Bug %(pk)s has been updated.\n\n"
"Link: %(bug_url)s\n\n"
"Summary: %(summary)s\n"
"Created at: %(creation_date)s\n"
"Reporter: %(reporter)s\n"
"Assignee: %(assignee)s\n"
"Product: %(product)s\n"
"Version: %(version)s\n"
"Build: %(build)s\n"
"Last comment:\n"
"%(last_comment)s"
msgstr "缺陷 %(pk)s has been updated.\n\n"
"链接: %(bug_url)s\n\n"
"摘要: %(summary)s\n"
"创建于: %(creation_date)s\n"
"报告人: %(reporter)s\n"
"创建人: %(assignee)s\n"
"产品: %(product)s\n"
"版本: %(version)s\n"
"构建: %(build)s\n"
"最后注解: %(last_comment)s"

#: tcms/templates/email/post_case_delete/email.txt:2
#, python-format
msgid "TestCase has been deleted by %(username)s!"
msgstr "测试用例已被 %(username)s 更新！"

#: tcms/templates/email/post_run_save/email.txt:2
#, python-format
msgid "Test run %(pk)s has been created or updated for you.\n\n"
"### Links ###\n"
"Test run: %(run_url)s\n"
"Test plan: %(plan_url)s\n\n"
"### Basic run information ###\n"
"Summary: %(summary)s\n\n"
"Managed: %(manager)s.\n"
"Default tester: %(default_tester)s.\n\n"
"Product: %(product)s\n"
"Product version: %(version)s\n"
"Build: %(build)s\n\n"
"Notes:\n"
"%(notes)s"
msgstr "已为您新建或更新测试运行 %(pk)s 。\n\n"
"### 链接 ###\n"
"测试运行: %(run_url)s\n"
"测试计划: %(plan_url)s\n\n"
"### 基本运行信息 ###\n"
"摘要: %(summary)s\n\n"
"管理: %(manager)s.\n"
"默认测试者: %(default_tester)s.\n\n"
"产品: %(product)s\n"
"产品版本: %(version)s\n"
"构建版本: %(build)s\n\n"
"备注:\n"
"%(notes)s"

#: tcms/templates/email/user_registered/notify_admins.txt:2
#, python-format
msgid "Dear Administrator,\n"
"somebody just registered an account with username %(username)s at your\n"
"Kiwi TCMS instance and is awaiting your approval!\n\n"
"Go to %(user_url)s to activate the account!"
msgstr "尊敬的管理员，\n"
"有人刚刚在您的\n"
"Kiwi TCMS实例注册了一个用户名为 %(username)s 的帐户，正在等待您的批准！\n\n"
"访问 %(user_url)s 激活帐户！"

#: tcms/templates/include/attachments.html:10
#: tcms/testplans/templates/testplans/get.html:437
#: tcms/testruns/templates/testruns/get.html:487
msgid "Attachments"
msgstr "附件"

#: tcms/templates/include/attachments.html:18
msgid "File"
msgstr "文件"

#: tcms/templates/include/attachments.html:19
msgid "Owner"
msgstr "所有者"

#: tcms/templates/include/attachments.html:20
msgid "Date"
msgstr "日期"

#: tcms/templates/include/attachments.html:35
#: tcms/templates/include/bugs_table.html:4
#: tcms/testplans/templates/testplans/get.html:332
msgid "No records found"
msgstr "没有找到记录"

#: tcms/templates/include/bugs_table.html:15
#: tcms/testruns/templates/testruns/get.html:573
msgid "URL"
msgstr "网址"

#: tcms/templates/include/properties_card.html:9 tcms/testruns/admin.py:117
#: tcms/testruns/templates/testruns/get.html:345
msgid "Parameters"
msgstr "参数"

#: tcms/templates/include/properties_card.html:15
#: tcms/testruns/templates/testruns/mutable.html:170
msgid "This is a tech-preview feature!"
msgstr "这是一个技术预览功能！"

#: tcms/templates/include/properties_card.html:68
msgid "name=value"
msgstr "名称=值"

#: tcms/templates/include/properties_card.html:69
#: tcms/templates/include/tags_card.html:28
#: tcms/testcases/templates/testcases/get.html:184
#: tcms/testcases/templates/testcases/get.html:229
#: tcms/testplans/templates/testplans/get.html:258
#: tcms/testruns/templates/testruns/get.html:292
#: tcms/testruns/templates/testruns/get.html:546
msgid "Add"
msgstr "添加"

#: tcms/templates/include/tags_card.html:13
#: tcms/testcases/templates/testcases/get.html:167
#: tcms/testcases/templates/testcases/get.html:215
#: tcms/testplans/templates/testplans/clone.html:14
#: tcms/testplans/templates/testplans/get.html:205
#: tcms/testplans/templates/testplans/mutable.html:24
#: tcms/testplans/templates/testplans/search.html:18
#: tcms/testruns/templates/testruns/get.html:579
msgid "Name"
msgstr "名称"

#: tcms/templates/initdb.html:5 tcms/templates/initdb.html:17
#: tcms/templates/initdb.html:29
msgid "Initialize database"
msgstr "初始化数据库"

#: tcms/templates/initdb.html:20
msgid "Your database has not been initialized yet. Click the button below to initialize it!"
msgstr "您的数据库尚未初始化。点击下面的按钮初始化它！"

#: tcms/templates/initdb.html:22
msgid "WARNING: this operation will take a while! This page will redirect when done."
msgstr "警告：此操作将花费一段时间！完成后将重定向此页面。"

#: tcms/templates/initdb.html:27
msgid "Please wait"
msgstr "请稍等"

#: tcms/templates/navbar.html:9
msgid "Toggle navigation"
msgstr "切换导航"

#: tcms/templates/navbar.html:15
msgid "DASHBOARD"
msgstr "显示板"

#: tcms/templates/navbar.html:41
msgid "Language"
msgstr "语言"

#: tcms/templates/navbar.html:45
msgid "Change language"
msgstr "更改语言是中文"

#: tcms/templates/navbar.html:46
msgid "Supported languages"
msgstr "支持的语言"

#: tcms/templates/navbar.html:47
msgid "Request new language"
msgstr "请求新语言"

#: tcms/templates/navbar.html:53
msgid "Translation mode"
msgstr "翻译模式"

#: tcms/templates/navbar.html:57
msgid "Translation guide"
msgstr "翻译指南"

#: tcms/templates/navbar.html:63
msgid "Help"
msgstr "帮助"

#: tcms/templates/navbar.html:78
msgid "Welcome Guest"
msgstr "欢迎游客"

#: tcms/templates/navbar.html:84
msgid "My Test Runs"
msgstr "我的测试执行"

#: tcms/templates/navbar.html:88
msgid "My Test Plans"
msgstr "我的测试计划"

#: tcms/templates/navbar.html:94
msgid "My profile"
msgstr "我的资料"

#: tcms/templates/navbar.html:98
#: tcms/templates/registration/password_reset_confirm.html:29
msgid "Change password"
msgstr "更改密码"

#: tcms/templates/navbar.html:108
msgid "Logout"
msgstr "登出"

#: tcms/templates/navbar.html:113 tcms/templates/registration/login.html:4
msgid "Login"
msgstr "登录"

#: tcms/templates/navbar.html:120
#: tcms/templates/registration/registration_form.html:53
msgid "Register"
msgstr "注册"

#: tcms/templates/registration/login.html:24
#: tcms/templates/registration/password_reset_confirm.html:15
#: tcms/templates/registration/registration_form.html:23
msgid "Password"
msgstr "密码"

#: tcms/templates/registration/login.html:31
msgid "Forgot password"
msgstr "忘记了密码"

#: tcms/templates/registration/login.html:34
msgid "Log in"
msgstr "登录"

#: tcms/templates/registration/login.html:45
msgid "Welcome to Kiwi TCMS"
msgstr "欢迎使用 Kiwi TCMS"

#: tcms/templates/registration/login.html:50
msgid "Please login to get started"
msgstr "请登录"

#: tcms/templates/registration/login.html:52
msgid "or"
msgstr "或"

#: tcms/templates/registration/login.html:53
msgid "register an account"
msgstr "注册一个帐户"

#: tcms/templates/registration/login.html:54
msgid "if you don't have one!"
msgstr "如果你没有的话！"

#: tcms/templates/registration/password_reset_complete.html:12
msgid "Your password has been set. You may go ahead and"
msgstr "您的密码已设置。您可以现在登录"

#: tcms/templates/registration/password_reset_complete.html:13
msgid "now"
msgstr "现在"

#: tcms/templates/registration/password_reset_confirm.html:43
msgid "Please enter your new password twice so we can verify you typed it in correctly"
msgstr "请输入你的密码两遍，这样我们可以验证你的输入"

#: tcms/templates/registration/password_reset_confirm.html:46
msgid "request a new password reset"
msgstr "请求密码重置"

#: tcms/templates/registration/password_reset_done.html:11
msgid "Password reset email was sent"
msgstr "密码重置邮件已经发送"

#: tcms/templates/registration/password_reset_form.html:27
msgid "Password reset"
msgstr "重置密码"

#: tcms/templates/registration/password_reset_form.html:34
msgid "Kiwi TCMS password reset"
msgstr "Kiwi 测试用例管理系统 密码重置"

#: tcms/templates/registration/registration_form.html:4
msgid "Register new account"
msgstr "注册新账户"

#: tcms/testcases/admin.py:28
msgid "For more information about customizing test case statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-case-statuses\">\n"
"        the documentation</a>!"
msgstr "欲了解更多关于自定义测试执行状态的信息，请参阅文档\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-case-statuses\">\n"
"        </a>！"

#: tcms/testcases/admin.py:56
msgid "1 confirmed & 1 uncomfirmed status required!"
msgstr "1 已确认 & 1 个未确认的状态！"

#: tcms/testcases/admin.py:131
msgid "Bug URL"
msgstr "缺陷链接"

#: tcms/testcases/admin.py:151
msgid "External Issue Tracker Integration"
msgstr "集成第三方缺陷跟踪系统"

#: tcms/testcases/admin.py:161
msgid "<h1>Warning: read the\n"
"<a href=\"http://kiwitcms.readthedocs.io/en/latest/admin.html#configure-external-bug-trackers\">\n"
"Configure external bug trackers</a> section before editting the values below!</h1>"
msgstr "<h1>警告：在编辑值前阅读\n"
"<a href=\"http://kiwitcms.readthedocs.io/en/latest/admin.html#configure-external-bug-trackers\">\n"
"配置第三方缺陷跟踪系统</a> 版块！</h1>"

#: tcms/testcases/admin.py:168
msgid "Configuration health check"
msgstr "配置健康检查"

#: tcms/testcases/admin.py:172
msgid "Kiwi TCMS will try fetching details for the given bug URL using the integration defined above! Click the `Save and continue` button and watch out for messages at the top of the screen. <strong>WARNING:</strong> in case of failures some issue trackers will fall back to fetching details via the OpenGraph protocol. In that case the result will include field named `from_open_graph`."
msgstr "TCMS将尝试使用上面定义的集成获取给定的 缺陷URL 的详细信息！ 点击“保存并继续”按钮，然后在屏幕顶部看看消息。 <strong>警告：</strong> 如果出现故障，一些问题跟踪器将返回通过 OpenGraph协议获取详细信息。 在这种情况下，结果将包括名为`from_open_graph`的字段。"

#: tcms/testcases/admin.py:192
msgid "Failed creating Issue Tracker"
msgstr "创建问题跟踪程序失败"

#: tcms/testcases/admin.py:201
msgid "Details extracted via OpenGraph. Issue Tracker may still be configured incorrectly!"
msgstr "通过 OpenGraph提取的详细信息。问题跟踪器的配置可能仍然不正确！"

#: tcms/testcases/admin.py:210
msgid "Details extracted via API. Issue Tracker configuration looks good!"
msgstr "通过 API 提取的详细信息。问题追踪器配置看起来很好！"

#: tcms/testcases/admin.py:223
msgid "Issue Tracker configuration check failed"
msgstr "Issue Tracker 配置检查失败"

#: tcms/testcases/helpers/email.py:22
#, python-format
msgid "DELETED: TestCase #%(pk)d - %(summary)s"
msgstr "已删除: 测试用例 #%(pk)d - %(summary)s"

#: tcms/testcases/models.py:23
msgid "Test case status"
msgstr "测试案例状态"

#: tcms/testcases/models.py:24
msgid "Test case statuses"
msgstr "测试案例状态"

#: tcms/testcases/models.py:379
#: tcms/testcases/templates/testcases/mutable.html:107
msgid "Template"
msgstr "模板"

#: tcms/testcases/models.py:380
msgid "Templates"
msgstr "模板集"

#: tcms/testcases/templates/testcases/clone.html:5
msgid "Clone TestCase"
msgstr "克隆测试用例"

#: tcms/testcases/templates/testcases/clone.html:15
msgid "Add new TC into TP"
msgstr "添加新的 TC 到 TP"

#: tcms/testcases/templates/testcases/clone.html:30
msgid "Selected TC"
msgstr "选定的 TC"

#: tcms/testcases/templates/testcases/clone.html:45 tcms/testcases/views.py:134
#: tcms/testplans/templates/testplans/clone.html:73
#: tcms/testplans/templates/testplans/get.html:138
#: tcms/testplans/templates/testplans/get.html:358 tcms/testplans/views.py:144
#: tcms/testruns/views.py:198
msgid "Clone"
msgstr "克隆"

#: tcms/testcases/templates/testcases/get.html:28
#: tcms/testcases/templates/testcases/get.html:168
#: tcms/testcases/templates/testcases/mutable.html:184
#: tcms/testcases/templates/testcases/search.html:122
#: tcms/testcases/templates/testcases/search.html:169
#: tcms/testplans/templates/testplans/get.html:54
#: tcms/testplans/templates/testplans/get.html:412
#: tcms/testplans/templates/testplans/get.html:492
#: tcms/testplans/templates/testplans/mutable.html:121
#: tcms/testplans/templates/testplans/search.html:82
#: tcms/testplans/templates/testplans/search.html:120
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:8
#: tcms/testruns/templates/testruns/mutable.html:230
msgid "Author"
msgstr "作者"

#: tcms/testcases/templates/testcases/get.html:49
#: tcms/testcases/templates/testcases/mutable.html:58
#: tcms/testcases/templates/testcases/search.html:100
#: tcms/testcases/templates/testcases/search.html:164
#: tcms/testplans/templates/testplans/get.html:408
#: tcms/testplans/templates/testplans/get.html:491
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:7
#: tcms/testruns/templates/testruns/get.html:266
#: tcms/testruns/templates/testruns/get.html:367
#: tcms/testruns/templates/testruns/mutable.html:233
msgid "Category"
msgstr "类别"

#: tcms/testcases/templates/testcases/get.html:64
#: tcms/testcases/templates/testcases/mutable.html:86
#: tcms/testcases/templates/testcases/search.html:82
#: tcms/testcases/templates/testcases/search.html:166
#: tcms/testplans/templates/testplans/get.html:162
#: tcms/testplans/templates/testplans/get.html:371
#: tcms/testplans/templates/testplans/get.html:404
#: tcms/testplans/templates/testplans/get.html:490
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:6
#: tcms/testruns/templates/testruns/get.html:265
#: tcms/testruns/templates/testruns/get.html:363
#: tcms/testruns/templates/testruns/mutable.html:234
msgid "Priority"
msgstr "优先级"

#: tcms/testcases/templates/testcases/get.html:73
#: tcms/testcases/templates/testcases/mutable.html:120
msgid "Setup duration"
msgstr "设置持续时间"

#: tcms/testcases/templates/testcases/get.html:78
#: tcms/testcases/templates/testcases/mutable.html:127
msgid "Testing duration"
msgstr "测试持续时间"

#: tcms/testcases/templates/testcases/get.html:83
msgid "Expected duration"
msgstr "预计持续时间"

#: tcms/testcases/templates/testcases/get.html:98
#: tcms/testcases/templates/testcases/mutable.html:143
msgid "Script"
msgstr "脚本"

#: tcms/testcases/templates/testcases/get.html:103
#: tcms/testcases/templates/testcases/mutable.html:149
msgid "Arguments"
msgstr "参数"

#: tcms/testcases/templates/testcases/get.html:108
#: tcms/testcases/templates/testcases/mutable.html:157
msgid "Requirements"
msgstr "需求"

#: tcms/testcases/templates/testcases/get.html:113
#: tcms/testcases/templates/testcases/mutable.html:162
#: tcms/testplans/templates/testplans/get.html:80
#: tcms/testplans/templates/testplans/mutable.html:102
msgid "Reference link"
msgstr "参考链接"

#: tcms/testcases/templates/testcases/get.html:133
#: tcms/testcases/templates/testcases/mutable.html:170
#: tcms/testplans/templates/testplans/get.html:433
#: tcms/testplans/templates/testplans/get.html:434
#: tcms/testruns/templates/testruns/get.html:400
#: tcms/testruns/templates/testruns/mutable.html:202
msgid "Notes"
msgstr "备注"

#: tcms/testcases/templates/testcases/get.html:152
msgid "Bugs"
msgstr "软件Bug"

#: tcms/testcases/templates/testcases/get.html:159
msgid "Test plans"
msgstr "测试计划"

#: tcms/testcases/templates/testcases/mutable.html:10
msgid "Edit TestCase"
msgstr "编辑 TestCase"

#: tcms/testcases/templates/testcases/mutable.html:59
msgid "add new Category"
msgstr "添加新分类"

#: tcms/testcases/templates/testcases/mutable.html:108
msgid "add new Template"
msgstr "添加新模板"

#: tcms/testcases/templates/testcases/mutable.html:180
#: tcms/testruns/templates/testruns/get.html:517
msgid "Notify"
msgstr "提醒"

#: tcms/testcases/templates/testcases/mutable.html:191
msgid "Manager of runs"
msgstr "管理执行器"

#: tcms/testcases/templates/testcases/mutable.html:198
msgid "Asignees"
msgstr "受配执行者"

#: tcms/testcases/templates/testcases/mutable.html:214
msgid "Default tester of runs"
msgstr "默认的测试者"

#: tcms/testcases/templates/testcases/mutable.html:223
msgid "Notify when"
msgstr "以下情况通知我"

#: tcms/testcases/templates/testcases/mutable.html:224
#: tcms/testplans/templates/testplans/mutable.html:142
msgid "applies only for changes made by somebody else"
msgstr "仅适用于别人所做的更改"

#: tcms/testcases/templates/testcases/mutable.html:229
msgid "TestCase is updated"
msgstr "测试用例已更新"

#: tcms/testcases/templates/testcases/mutable.html:236
msgid "TestCase is deleted"
msgstr "测试用例已删除"

#: tcms/testcases/templates/testcases/mutable.html:245
msgid "CC to"
msgstr "抄送给"

#: tcms/testcases/templates/testcases/mutable.html:250
msgid "Email addresses separated by comma. A notification email will be sent to each Email address within CC list."
msgstr "用逗号分隔的电子邮件地址。通知电子邮件将在 CC 列表中发送给每个电子邮件地址。"

#: tcms/testcases/templates/testcases/search.html:15
msgid "Test case summary"
msgstr "测试用例总结"

#: tcms/testcases/templates/testcases/search.html:45
msgid "Both"
msgstr "都"

#: tcms/testcases/templates/testcases/search.html:61
msgid "include in search request"
msgstr "包含在搜索请求"

#: tcms/testcases/templates/testcases/search.html:74
msgid "include child test plans"
msgstr "包括子测试计划"

#: tcms/testcases/templates/testcases/search.html:112
#: tcms/testcases/templates/testcases/search.html:165
#: tcms/testplans/templates/testplans/get.html:216
#: tcms/testruns/templates/testruns/get.html:262
msgid "Component"
msgstr "组件"

#: tcms/testcases/templates/testcases/search.html:128
msgid "Text"
msgstr "文字"

#: tcms/testcases/templates/testcases/search.html:139
#: tcms/testplans/templates/testplans/search.html:97
#: tcms/testruns/templates/testruns/search.html:30
msgid "Separate multiple values with comma (,)"
msgstr "使用逗号(,)隔开多个值"

#: tcms/testcases/templates/testcases/search.html:178
msgid "Select"
msgstr "选择"

#: tcms/testcases/views.py:138 tcms/testplans/views.py:146
#: tcms/testruns/templates/testruns/get.html:458 tcms/testruns/views.py:202
msgid "History"
msgstr "历史"

#: tcms/testcases/views.py:250
msgid "TestCase cloning was successful"
msgstr "测试用例复制成功"

#: tcms/testcases/views.py:281
msgid "At least one TestCase is required"
msgstr "需要至少一个测试用例"

#: tcms/testplans/templates/testplans/clone.html:5
msgid "Clone TestPlan"
msgstr "克隆测试计划"

#: tcms/testplans/templates/testplans/clone.html:55
msgid "Clone TCs"
msgstr "克隆TCs"

#: tcms/testplans/templates/testplans/clone.html:59
msgid "Clone or link existing TCs into new TP"
msgstr "克隆或将现有 TCs 连接到新的 TP"

#: tcms/testplans/templates/testplans/clone.html:63
msgid "Parent TP"
msgstr "上级TP"

#: tcms/testplans/templates/testplans/clone.html:67
msgid "Set the source TP as parent of new TP"
msgstr "将源TP设置为新TP的父目录"

#: tcms/testplans/templates/testplans/get.html:25
#: tcms/testruns/templates/testruns/get.html:23
msgid "Enter username, email or user ID:"
msgstr "输入用户名、电子邮件或用户ID："

#: tcms/testplans/templates/testplans/get.html:26
#: tcms/testruns/templates/testruns/get.html:22
msgid "No rows selected! Please select at least one!"
msgstr "没有选择任何行！请选择至少一个 ！"

#: tcms/testplans/templates/testplans/get.html:27
#: tcms/testruns/templates/testruns/get.html:24
msgid "Are you sure?"
msgstr "您确定吗？"

#: tcms/testplans/templates/testplans/get.html:28
msgid "Cannot create TestRun with unconfirmed test cases"
msgstr "无法以未经确认的测试实例创建测试执行"

#: tcms/testplans/templates/testplans/get.html:29
msgid "Error adding test cases"
msgstr "添加测试案例时出错"

#: tcms/testplans/templates/testplans/get.html:43
msgid "Show more"
msgstr "显示更多"

#: tcms/testplans/templates/testplans/get.html:75
msgid "Plan Type"
msgstr "计划类型"

#: tcms/testplans/templates/testplans/get.html:100
msgid "Test cases"
msgstr "测试用例"

#: tcms/testplans/templates/testplans/get.html:182
#: tcms/testplans/templates/testplans/get.html:421
#: tcms/testplans/templates/testplans/get.html:494
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:10
msgid "Reviewer"
msgstr "评审人"

#: tcms/testplans/templates/testplans/get.html:231
#: tcms/testplans/templates/testplans/get.html:234
msgid "Sort key"
msgstr "排序关键字"

#: tcms/testplans/templates/testplans/get.html:244
msgid "Re-order cases"
msgstr "重新排序的用例"

#: tcms/testplans/templates/testplans/get.html:254
#: tcms/testruns/templates/testruns/get.html:288
msgid "Search and add test cases"
msgstr "搜索并添加测试用例"

#: tcms/testplans/templates/testplans/get.html:263
#: tcms/testruns/templates/testruns/get.html:298
msgid "Advanced search"
msgstr "高级搜索"

#: tcms/testplans/templates/testplans/get.html:286
msgid "Active test runs"
msgstr "活动的测试执行"

#: tcms/testplans/templates/testplans/get.html:308
msgid "More"
msgstr "更多"

#: tcms/testplans/templates/testplans/get.html:310
msgid "Inactive"
msgstr "未激活"

#: tcms/testplans/templates/testplans/get.html:444
#: tcms/testruns/templates/testruns/get.html:494
msgid "No attachments"
msgstr "无附件"

#: tcms/testplans/templates/testplans/get.html:469
#: tcms/testruns/templates/testruns/get.html:483
msgid "Comments"
msgstr "评论"

#: tcms/testplans/templates/testplans/mutable.html:10
msgid "Edit TestPlan"
msgstr "编辑测试计划"

#: tcms/testplans/templates/testplans/mutable.html:12
msgid "Create new TestPlan"
msgstr "创建新的测试计划"

#: tcms/testplans/templates/testplans/mutable.html:82
msgid "Parent ID"
msgstr "父ID"

#: tcms/testplans/templates/testplans/mutable.html:100
msgid "Enter to assign; Backspace + Enter to clear"
msgstr "输入要分配的位置；按退后加enter以清除"

#: tcms/testplans/templates/testplans/mutable.html:110
msgid "Test plan document:"
msgstr "测试计划文档随便写的啊"

#: tcms/testplans/templates/testplans/mutable.html:119
msgid "Notify:"
msgstr "方又松："

#: tcms/testplans/templates/testplans/mutable.html:127
msgid "TestCase author"
msgstr "测试用例作者"

#: tcms/testplans/templates/testplans/mutable.html:141
msgid "Notify when:"
msgstr "以下情况通知我:"

#: tcms/testplans/templates/testplans/mutable.html:145
msgid "TestPlan is updated"
msgstr "测试计划已更新"

#: tcms/testplans/templates/testplans/mutable.html:151
msgid "Test cases are updated"
msgstr "测试用例已更新"

#: tcms/testplans/templates/testplans/mutable.html:165
#: tcms/testplans/templates/testplans/search.html:41
msgid "Active"
msgstr "活动"

#: tcms/testplans/templates/testplans/search.html:12
msgid "Some child test plans do not match search criteria"
msgstr "一些子测试计划与搜索条件不匹配"

#: tcms/testplans/templates/testplans/search.html:20
msgid "Test plan name"
msgstr "测试计划名"

#: tcms/testruns/admin.py:32
msgid "Permission denied: TestRun does not belong to you"
msgstr "拒绝访问: 测试运行不属于您"

#: tcms/testruns/admin.py:39
msgid "For more information about customizing test execution statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-execution-statuses\">\n"
"        the documentation</a>!"
msgstr "欲了解更多关于自定义测试执行状态的信息，请参阅\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-execution-statuses\">\n"
"        文档</a>！"

#: tcms/testruns/admin.py:95
msgid "1 negative, 1 neutral & 1 positive status required!"
msgstr "1个消极, 1个中性和 1个正面状态必须填写！"

#: tcms/testruns/admin.py:106
msgid "Edit parameters"
msgstr "编辑参数"

#: tcms/testruns/forms.py:39
msgid "Full"
msgstr "完整"

#: tcms/testruns/forms.py:40
msgid "Pairwise"
msgstr "对偶"

#: tcms/testruns/models.py:230
msgid "Test execution statuses"
msgstr "测试执行状态"

#: tcms/testruns/templates/testruns/get.html:25
msgid "Unconfirmed test cases were not added"
msgstr "未添加未经确认的测试用例"

#: tcms/testruns/templates/testruns/get.html:26
msgid "Type 0 or 1"
msgstr "类型 0 或 1"

#: tcms/testruns/templates/testruns/get.html:27
msgid "Comment"
msgstr "评论"

#: tcms/testruns/templates/testruns/get.html:60
#: tcms/testruns/templates/testruns/mutable.html:29
#: tcms/testruns/templates/testruns/search.html:67
#: tcms/testruns/templates/testruns/search.html:188
msgid "Manager"
msgstr "管理者"

#: tcms/testruns/templates/testruns/get.html:73
#: tcms/testruns/templates/testruns/mutable.html:110
#: tcms/testruns/templates/testruns/search.html:127
msgid "Planned start"
msgstr "计划开始日期"

#: tcms/testruns/templates/testruns/get.html:84
msgid "Start"
msgstr "开始"

#: tcms/testruns/templates/testruns/get.html:91
#: tcms/testruns/templates/testruns/mutable.html:123
#: tcms/testruns/templates/testruns/search.html:147
msgid "Planned stop"
msgstr "计划结束日期"

#: tcms/testruns/templates/testruns/get.html:102
msgid "Stop"
msgstr "停止"

#: tcms/testruns/templates/testruns/get.html:139
#: tcms/testruns/templates/testruns/mutable.html:160
msgid "Environment"
msgstr "环境"

#: tcms/testruns/templates/testruns/get.html:182
msgid "Update text version"
msgstr "更新文本版本"

#: tcms/testruns/templates/testruns/get.html:217
msgid "Add comment"
msgstr "添加注释"

#: tcms/testruns/templates/testruns/get.html:228
#: tcms/testruns/templates/testruns/get.html:331
#: tcms/testruns/templates/testruns/get.html:568
msgid "Add hyperlink"
msgstr "添加超链接"

#: tcms/testruns/templates/testruns/get.html:279
msgid "Mine"
msgstr "我的"

#: tcms/testruns/templates/testruns/get.html:280
msgid "All"
msgstr "全部"

#: tcms/testruns/templates/testruns/get.html:306
msgid "records"
msgstr "记录"

#: tcms/testruns/templates/testruns/get.html:332
#: tcms/testruns/templates/testruns/get.html:607
#: tcms/testruns/templates/testruns/get.html:624
msgid "Report bug"
msgstr "报告 bug"

#: tcms/testruns/templates/testruns/get.html:339
msgid "Test case is not part of parent test plan"
msgstr "测试用例不属于该测试计划"

#: tcms/testruns/templates/testruns/get.html:371
msgid "Assigned to"
msgstr "分配给"

#: tcms/testruns/templates/testruns/get.html:379
#: tcms/testruns/templates/testruns/get.html:381
msgid "Last bug"
msgstr ""

#: tcms/testruns/templates/testruns/get.html:423
msgid "Text version"
msgstr "文本版本"

#: tcms/testruns/templates/testruns/get.html:431
msgid "Bugs and hyperlinks"
msgstr "错误链接和超链接"

#: tcms/testruns/templates/testruns/get.html:585
msgid "Is a defect"
msgstr "是缺陷"

#: tcms/testruns/templates/testruns/get.html:592
#: tcms/testruns/templates/testruns/get.html:623
msgid "Cancel"
msgstr "取消"

#: tcms/testruns/templates/testruns/get.html:612
msgid "Issue Tracker"
msgstr "问题跟踪"

#: tcms/testruns/templates/testruns/mutable.html:7
msgid "Edit TestRun"
msgstr "编辑测试执行"

#: tcms/testruns/templates/testruns/mutable.html:9
msgid "Clone TestRun"
msgstr "克隆测试执行"

#: tcms/testruns/templates/testruns/mutable.html:180
msgid "Affects only test cases with parameters"
msgstr "仅影响带参数的测试用例"

#: tcms/testruns/templates/testruns/mutable.html:194
msgid "more information"
msgstr "更多信息"

#: tcms/testruns/templates/testruns/mutable.html:218
msgid "Selected TestCase(s):"
msgstr "已选测试用例(s)："

#: tcms/testruns/templates/testruns/mutable.html:221
#, python-format
msgid "%(count)s of the pre-selected test cases is not CONFIRMED and will not be cloned!\n"
"See test plan for more details!"
msgstr "预选测试案例中的 %(count)s 不是 CONFIRMED ，将不会被克隆！\n"
"查看测试计划以了解更多详情！"

#: tcms/testruns/templates/testruns/search.html:21
msgid "Plan ID"
msgstr "计划 ID"

#: tcms/testruns/templates/testruns/search.html:23
msgid "TestPlan ID"
msgstr "测试计划 ID"

#: tcms/testruns/templates/testruns/search.html:79
msgid "Running"
msgstr "运行中"

#: tcms/testruns/templates/testruns/search.html:86
#: tcms/testruns/templates/testruns/search.html:186
msgid "Start date"
msgstr "开始日期"

#: tcms/testruns/templates/testruns/search.html:106
#: tcms/testruns/templates/testruns/search.html:187
msgid "Stop date"
msgstr "截至日期"

#: tcms/testruns/views.py:273
msgid "Clone of "
msgstr "克隆来自 "

#: testcases.TestCaseStatus/name:1
msgid "PROPOSED"
msgstr "已创建"

#: testcases.TestCaseStatus/name:3
msgid "DISABLED"
msgstr "已禁用"

#: testcases.TestCaseStatus/name:4
msgid "NEED_UPDATE"
msgstr "需要更新"

#: testruns.TestExecutionStatus/name:1
msgid "IDLE"
msgstr "空闲"

#: testruns.TestExecutionStatus/name:2
msgid "RUNNING"
msgstr "运行中"

#: testruns.TestExecutionStatus/name:3
msgid "PAUSED"
msgstr "已暂停"

#: testruns.TestExecutionStatus/name:4
msgid "PASSED"
msgstr "已通过"

#: testruns.TestExecutionStatus/name:5
msgid "FAILED"
msgstr "失败"

#: testruns.TestExecutionStatus/name:6
msgid "BLOCKED"
msgstr "已挂起"

#: testruns.TestExecutionStatus/name:7
msgid "ERROR"
msgstr "错误"

#: testruns.TestExecutionStatus/name:8
msgid "WAIVED"
msgstr "已放弃"

#: tcms_github_app/admin.py:122
#, python-format
msgid "For additional configuration see\n"
"<a href=\"%s\">GitHub</a>"
msgstr "更多配置参考\n"
"<a href=\"%s\">GitHub</a>"

#: tcms_github_app/menu.py:11
msgid "GitHub integration"
msgstr "GitHub集成"

#: tcms_github_app/menu.py:12
msgid "Resync"
msgstr "重新同步"

#: tcms_github_app/menu.py:13
msgid "Settings"
msgstr "设置"

#: tcms_github_app/middleware.py:41
#, python-format
msgid "Unconfigured GitHub App %d"
msgstr "未配置 GitHub 应用程序 %d"

#: tcms_github_app/utils.py:274
#, python-format
msgid "%s was imported from GitHub"
msgstr "%s 已从 GitHub 导入"

#: tcms_github_app/utils.py:278
#, python-format
msgid "%s already exists"
msgstr "%s 已存在"

#: tcms_github_app/views.py:48
#, python-format
msgid "You have not logged-in via GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "您尚未通过 GitHub 帐户登录！ <a href=\"%s\">点击这里</a>！"

#: tcms_github_app/views.py:62
#, python-format
msgid "You have not installed Kiwi TCMS into your GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "您还没有在 GitHub 账户中安装 Kiwi TCMS ！ <a href=\"%s\">点击这里</a>！"

#: tcms_github_app/views.py:76
msgid "Multiple GitHub App installations detected! See below:"
msgstr "检测到多个GitHub 应用程序安装！请参阅下面："

#: tcms_github_app/views.py:85
#, python-format
msgid "Edit GitHub App <a href=\"%s\">%s</a>"
msgstr "Edit GitHub App <a href=\"%s\">%s</a>"

#: tcms_github_app/views.py:102
#, python-format
msgid "Cannot find GitHub App installation for tenant \"%s\""
msgstr "找不到对于用户 \"%s\"的GitHub 应用安装"

#: tcms_github_app/views.py:111
msgid "Multiple GitHub App installations detected!"
msgstr "检测到多个GitHub 应用已安装！"

#: tcms_github_marketplace/menu.py:11
msgid "Subscriptions"
msgstr "订阅"

#: tcms_github_marketplace/templates/tcms_github_marketplace/email/exit_poll.txt:1
msgid "Thank you for using Kiwi TCMS via a paid subscription.\n"
"We're sorry to see you go but we'd like to know why so we can improve in the future!\n\n"
"You can share your feedback at https://forms.gle/RJhFengbjN9C6wtUA\n\n"
"Thank you!"
msgstr "感谢您通过付费订阅使用 Kiwi TCMS。\n"
"我们很抱歉看到您离开，但我们想知道为什么以便我们可以在未来改进！\n\n"
"您可以在论坛分享您的反馈。 \n\n"
"谢谢！"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:6
msgid "Tenant subscriptions"
msgstr "租户订阅"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:15
msgid "You can access the following tenants"
msgstr "您可以访问以下租户"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:20
msgid "Tenant"
msgstr "租户"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:31
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:90
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:30
msgid "Organization"
msgstr "组织"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:44
msgid "Docker credentials"
msgstr "Docker凭据"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:61
msgid "Private containers instructions"
msgstr "Private容器指令"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:75
msgid "You own the following tenants"
msgstr "你拥有下列租户"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:97
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:98
msgid "Price"
msgstr "价格"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:102
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:103
msgid "Subscription type"
msgstr "訂閱類型"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:107
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:108
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:8
msgid "Paid until"
msgstr "支付至"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:114
msgid "Cancel subscription"
msgstr "停止订购"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:127
msgid "You don't own any tenants"
msgstr "您不拥有任何租户"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:131
msgid "Subscribe via FastSpring"
msgstr "通过 FastSpring 订阅"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:147
msgid "Transaction history"
msgstr "交易记录"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:169
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:170
msgid "Sender"
msgstr "发送者"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:174
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:175
msgid "Vendor"
msgstr "供应商"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:179
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:180
msgid "Received on"
msgstr "收到的时间"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:4
msgid "Extra emails"
msgstr "额外邮件"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:14
msgid "Kiwi TCMS will try to match recurring billing events against tenant.owner.email + tenant.extra_emails"
msgstr "Kiwi TCMS 将尝试匹配与tenant.owners.email + tenant.extern_email相匹配的重复性计费事件"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:17
msgid "Separate by comma (,), semi-colon (;) or white space ( )"
msgstr "用逗号 (,), 半冒号 (;) 或空格分开"

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:44
msgid "Private Tenant Warning"
msgstr "私人租户警告"

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:49
msgid "You are about to create a Private Tenant for Kiwi TCMS.\n"
"It will take a few minutes until your DB schema is ready!\n"
"After clicking the 'Save' button <strong>do not</strong> close or refresh this page!<br>\n"
"You will be redirected to your new tenant when the creation process is complete!\n"
"If you see a 500 Internal Server Error page please contact\n"
"<a href=\"mailto:<EMAIL>\"><EMAIL></a> immediately!"
msgstr "您将为Kiwi TCMS创建一个私人租户。\n"
"您的数据库架构准备就绪将需要几分钟！\n"
"点击“保存”按钮后，<strong>请勿</strong>关闭或刷新此页面！<br>\n"
"创建过程完成后，您将被重定向到新的租户！\n"
"如果看到500 Internal Server Error页面，请联系\n"
"立即<a href=\"mailto:<EMAIL>\"> <EMAIL> </a>！"

#: tcms_github_marketplace/utils.py:95
msgid "Kiwi TCMS Subscription Exit Poll"
msgstr "Kiwi TCMS 订阅退出调查"

#: tcms_github_marketplace/views.py:567
msgid "Kiwi TCMS subscription notification"
msgstr "Kiwi TCMS 订阅通知"

#: tcms_github_marketplace/views.py:749
msgid "mo"
msgstr "月"

#: tcms_github_marketplace/views.py:752
msgid "yr"
msgstr "年"

#: tcms_enterprise/pipeline.py:17
msgid "Email address is required"
msgstr "需要提供电子邮件地址"

#: tcms_enterprise/templates/registration/custom_login.html:10
msgid "or Continue With"
msgstr "或者使用"

#: tcms_settings_dir/enterprise.py:19
msgid "Legal information"
msgstr "法律信息"

#: tcms_tenants/admin.py:55 tcms_tenants/admin.py:62
#: tcms_tenants/middleware.py:35
msgid "Unauthorized"
msgstr "未授权"

#: tcms_tenants/admin.py:86
msgid "Existing username, email or user ID"
msgstr "用户名、电子邮件或用户 ID已存在"

#: tcms_tenants/admin.py:159
msgid "Full name"
msgstr "全名"

#: tcms_tenants/forms.py:30
msgid "Invalid string"
msgstr "无效的字符串"

#: tcms_tenants/menu.py:15
msgid "Create"
msgstr "新建"

#: tcms_tenants/menu.py:20
#: tcms_tenants/templates/tcms_tenants/invite_users.html:17
msgid "Invite users"
msgstr "邀请用户"

#: tcms_tenants/menu.py:21
msgid "Authorized users"
msgstr "被授权用户"

#: tcms_tenants/middleware.py:59
msgid "Unpaid"
msgstr "未缴"

#: tcms_tenants/middleware.py:70
msgid "Tenant expires soon"
msgstr "用户即将过期"

#: tcms_tenants/templates/tcms_tenants/email/invite_user.txt:1
#, python-format
msgid "Dear tester,\n"
"%(invited_by)s has invited you to join their Kiwi TCMS tenant at\n"
"%(tenant_url)s\n\n"
"In case you have never logged in before an account was created for you\n"
"automatically. You can login with a social account which has the same email\n"
"address or go to %(password_reset_url)s to reset your password.\n"
"The password reset email message also contains your username!"
msgstr "%(invited_by)s 邀请您加入他们的 Kiwi TCMS 租户，访问地址为 %(tenant_url)s。\n\n"
"如果您之前从未登录过，系统已为您自动创建了一个账户。您可以使用与该账户关联的社交账号登录，或者访问 %(password_reset_url)s 重置密码。\n"
"密码重置邮件中还包含您的用户名！"

#: tcms_tenants/templates/tcms_tenants/email/new.txt:1
#, python-format
msgid "Your Kiwi TCMS tenant was created at:\n"
"%(tenant_url)s\n\n"
"If you have troubles please contact support!"
msgstr "您的 Kiwi TCMS 租户创建于：\n"
"%(tenant_url)s\n\n"
"如果您遇到问题，请联系技术支持！"

#: tcms_tenants/templates/tcms_tenants/invite_users.html:28
msgid "Email"
msgstr "邮箱"

#: tcms_tenants/templates/tcms_tenants/new.html:18
msgid "New tenant"
msgstr "新租户"

#: tcms_tenants/templates/tcms_tenants/new.html:35
msgid "Company, team or project name"
msgstr "公司、团队或项目名称"

#: tcms_tenants/templates/tcms_tenants/new.html:43
msgid "Schema"
msgstr "概述"

#: tcms_tenants/templates/tcms_tenants/new.html:56
msgid "Validation pattern"
msgstr "验证模式"

#: tcms_tenants/templates/tcms_tenants/new.html:61
msgid "Publicly readable"
msgstr "公开可读"

#: tcms_tenants/templates/tcms_tenants/new.html:80
msgid "Tenant logo"
msgstr "Tenant logo"

#: tcms_tenants/utils.py:66
msgid "Schema name already in use"
msgstr "方案名称已被使用"

#: tcms_tenants/utils.py:170
msgid "New Kiwi TCMS tenant created"
msgstr "新的 Kiwi TCMS 租户已创建"

#: tcms_tenants/utils.py:230
#, python-brace-format
msgid "User {user.username} added to tenant group {group.name}"
msgstr "用户{user.username} 已添加进账户群组{group.name}"

#: tcms_tenants/utils.py:262
msgid "Invitation to join Kiwi TCMS"
msgstr "邀请加入TCMS"

#: tcms_tenants/views.py:84
msgid "Only super-user and tenant owner are allowed to edit tenant properties"
msgstr "只允许超级用户和租户所有者编辑租户属性"

#: tcms_tenants/views.py:102
msgid "Edit tenant"
msgstr "编辑租户"

#: tcms_tenants/views.py:153
msgid "Only users who are authorized for this tenant can invite others"
msgstr "只有获得此租户授权的用户才能邀请其他"

#: tenant_groups/admin.py:30
msgid "users"
msgstr "用户"

#: tenant_groups/models.py:34
msgid "name"
msgstr "名称"

#: tenant_groups/models.py:37
msgid "permissions"
msgstr "权限"

#: tenant_groups/models.py:47
msgid "group"
msgstr "用户组"

#: tenant_groups/models.py:48
msgid "groups"
msgstr "组"

#: trackers_integration/menu.py:4
msgid "Personal API tokens"
msgstr "个人API令牌"
