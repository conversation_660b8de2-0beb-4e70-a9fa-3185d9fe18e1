{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Search Test Plans" %}{% endblock %}
{% block page_id %}page-testplans-search{% endblock %}

{% block contents %}
<div
    class="container-fluid container-cards-pf"
    id="main-element"
    data-trans-some-rows-not-shown="{% trans 'Some child test plans do not match search criteria'%}"
>
    <form class="form-horizontal" method="get">
        {% csrf_token %}

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_name">{% trans "Name" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_name" type="text" class="form-control" placeholder="{% trans 'Test plan name' %}"
                    value="{{ form.name.value|default:'' }}">
            </div>

            <label class="col-md-1 col-lg-1">{% trans "Created" %}</label>
            <div class="col-md-3 col-lg-3">
                <div class="input-group date-time-picker-pf">
                    <span class="input-group-addon">{% trans "Before" %}</span>
                    <input type="text" class="form-control" id="id_before">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>

                    <span class="input-group-addon">{% trans "After" %}</span>
                    <input type="text" class="form-control" id="id_after">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>
                </div>
            </div>

            <label class="col-md-1 col-lg-1" for="id_active">{% trans "Active" %}</label>
            <div class="col-md-3 col-lg-3">
                <input class="bootstrap-switch" id="id_active" type="checkbox"
                    checked data-on-text="{% trans 'Yes' %}" data-off-text="{% trans 'No' %}"
                    value="{{ form.is_active.value }}">
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_product">{% trans "Product" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_product">
                    <option value="">----------</option>
                {% for option in form.product.field.queryset %}
                    <option value="{{ option.pk }}" {% if form.product.value|add:'0' == option.pk %}selected{% endif %}>{{ option.name }}</option>
                {% endfor %}
                </select>
            </div>

            <label class="col-md-1 col-lg-1" for="id_version">{% trans "Version" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_version">
                    <option value="">----------</option>
                {% for option in form.product_version.field.queryset %}
                    <option value="{{ option.pk }}" {% if form.product_version.value|add:'0' == option.pk %}selected{% endif %}>{{ option.value }}</option>
                {% endfor %}
                </select>
            </div>

            <label class="col-md-1 col-lg-1" for="id_type">{% trans "Type" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_type">
                    <option value="">----------</option>
                {% for option in form.type.field.queryset %}
                    <option value="{{ option.pk }}" {% if form.type.value|add:'0' == option.pk %}selected{% endif %}>{{ option.name }}</option>
                {% endfor %}
                </select>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_author">{% trans "Author" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_author" type="text" class="form-control" placeholder="{% trans 'Username' %}"
                    value="{{ form.author.value|default:'' }}">
            </div>

            <label class="col-md-1 col-lg-1" for="id_default_tester">{% trans "Default tester" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_default_tester" type="text" class="form-control" placeholder="{% trans 'Username' %}"
                    value="{{ form.default_tester.value|default:'' }}">
            </div>

            <label class="col-md-1 col-lg-1" for="id_tag">{% trans "Tag" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_tag" type="text" class="form-control" value="{{ form.tag.value.0|default:'' }}">
                <p class="help-block">{% trans "Separate multiple values with comma (,)" %}</p>
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-1 col-lg-1">
                <button id="btn_search" type="submit" class="btn btn-default btn-lg">{% trans "Search" %}</button>
            </div>
        </div>
    </form>

    <div class="panel panel-default">
        <!-- Table HTML -->
        <table class="table table-striped table-bordered table-hover" id="resultsTable">
            <thead>
                <tr>
                    <th></th>
                    <th>{% trans "ID" %}</th>
                    <th>{% trans "Test plan" %}</th>
                    <th>{% trans "Created on" %}</th>
                    <th>{% trans "Product" %}</th>
                    <th>{% trans "Version" %}</th>
                    <th>{% trans "Type" %}</th>
                    <th>{% trans "Author" %}</th>
                    <th>{% trans "Tags" %}</th>
                </tr>
            </thead>
        </table>

    </div>
</div><!-- /container -->
{% endblock %}
