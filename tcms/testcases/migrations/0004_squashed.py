# Generated by Django 2.1.2 on 2018-10-17 11:00

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("testcases", "0001_initial"),
        ("testruns", "0004_squashed"),
    ]

    operations = [
        migrations.AddField(
            model_name="bug",
            name="case_run",
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=models.deletion.CASCADE,
                related_name="case_run_bug",
                to="testruns.TestCaseRun",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="bug",
            unique_together={("bug_id", "case_run"), ("bug_id", "case_run", "case")},
        ),
    ]
