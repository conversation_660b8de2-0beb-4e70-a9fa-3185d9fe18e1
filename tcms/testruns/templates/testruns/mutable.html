{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}
    {% if object %}
        {% trans "Edit TestRun" %}
    {% elif is_cloning %}
        {% trans "Clone TestRun" %}
    {% else %}
        {% trans "New Test Run" %}
    {% endif %}
{% endblock %}

{% block page_id %}page-testruns-mutable{% endblock %}

{% block contents %}
<div class="container-fluid container-cards-pf">
    <form class="form-horizontal" action="{% if object %}{% url 'testruns-edit' object.pk %}{% else %}{% url 'testruns-new' %}{% endif %}" method="post">
        {% csrf_token %}

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_summary">{% trans "Summary" %}</label>
            <div class="col-md-3 col-lg-3 {% if form.summary.errors %}has-error{% endif %}">
                <input type="text" id="id_summary" name="summary" value="{{ form.summary.value }}" class="form-control" required>
                {{ form.summary.errors }}
            </div>

            <label class="col-md-1 col-lg-1" for="id_manager">{% trans "Manager" %}</label>
            <div class="col-md-3 col-lg-3 {% if form.manager.errors %}has-error{% endif %}">
                <input id="id_manager" name="manager" value="{{ form.manager.value }}" type="text"
                    class="form-control" placeholder="{% trans 'Username or email' %}" required>
                {{ form.manager.errors }}
            </div>

            <label class="col-md-1 col-lg-1" for="id_default_tester">{% trans "Default tester" %}</label>
            <div class="col-md-3 col-lg-3 {% if form.default_tester.errors %}has-error{% endif %}">
                <input id="id_default_tester" name="default_tester" value="{{ form.default_tester.value|default:'' }}" type="text"
                    class="form-control" placeholder="{% trans 'Username or email' %}">
                {{ form.default_tester.errors }}
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-1 col-lg-1">
                <label for="id_product">{% trans "Product" %}</label>
                {% if not plan_id %}
                    <a href="{% url 'admin:management_product_add' %}?_popup" id="add_id_product" alt="{% trans 'add new Product' %}" title="{% trans 'add new Product' %}">+</a>
                {% endif %}
            </div>

            <div class="col-md-3 {% if form.product.errors %}has-error{% endif %}">
                <select name="product" id="id_product" class="form-control selectpicker">
                    {% if not plan_id %}
                        <option value="">----------</option>
                    {% endif %}

                    {% for option in form.product.field.queryset %}
                        <option value="{{ option.pk }}" {% if option.pk|escape == form.plan.field.queryset.0.product_id|escape %}selected{% endif %}>{{ option.name }}</option>
                    {% endfor %}
                </select>

                {{ form.product.errors }}
            </div>

            <div class="col-md-1 col-lg-1">
                <label for="id_test_plan">{% trans "Test plan" %}</label>

                {% if not plan_id %}
                <a href="{% url 'plans-new' %}" alt="{% trans 'New Test Plan' %}" title="{% trans 'New Test Plan' %}">+</a>
                {% endif %}
            </div>

            <div class="col-md-3 col-lg-3 {% if form.plan.errors %}has-error{% endif %}">
                <select class="form-control selectpicker" id="id_test_plan" name="plan">
                {% if not plan_id %}
                    <option value="">----------</option>
                {% endif %}

                {% for option in form.plan.field.queryset %}
                    <option value="{{ option.pk }}" {% if option.pk|escape == form.plan.value|escape %}selected{% endif %}>{{ option.name }}</option>
                {% endfor %}
                </select>

                {{ form.plan.errors }}
            </div>

            <div class="col-md-1 col-lg-1">
                <label for="id_build">{% trans "Build" %}</label>

                <a href="{% url 'admin:management_build_add' %}?_popup&version={{ form.plan.field.queryset.0.product_version_id }}&product={{ form.plan.field.queryset.0.product_id }}"
                    id="add_id_build"
                    alt="{% trans 'add new Build' %}"
                    title="{% trans 'add new Build' %}">+</a>
            </div>

            <div class="col-md-3 col-lg-3 {% if form.build.errors %}has-error{% endif %}">
                <select class="form-control selectpicker" id="id_build" name="build">
                    <option value="">----------</option>
                {% for option in form.build.field.queryset %}
                    <option value="{{ option.pk }}" {% if option.pk|escape == form.build.value|escape %}selected{% endif %}>{{ option.name }}</option>
                {% endfor %}
                </select>

                {{ form.build.errors }}
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_planned_start">{% trans "Planned start" %}</label>
            <div class="col-md-3 col-lg-3 {% if form.planned_start.errors %}has-error{% endif %}">
                <div class="input-group date-time-picker-pf planned_start_date">
                    <input
                        type="text" id="id_planned_start" name="planned_start" class="form-control"
                        value="{{ form.planned_start.value|date:'Y-m-d H:i:s' }}">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>
                </div>
                {{ form.planned_start.errors }}
            </div>

            <label class="col-md-1 col-lg-1" for="id_planned_stop">{% trans "Planned stop" %}</label>
            <div class="col-md-3 col-lg-3 {% if form.planned_stop.errors %}has-error{% endif %}">
                <div class="input-group date-time-picker-pf">
                    <input
                        type="text" class="form-control" id="id_planned_stop" name="planned_stop"
                        value="{{ form.planned_stop.value|date:'Y-m-d H:i:s' }}">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar">
                        </span>
                    </span>
                </div>
                {{  form.planned_stop.errors }}
            </div>

            <input type="hidden" id="id_start_date" name="start_date" value="{{ form.start_date.value|date:'Y-m-d H:i:s' }}">

            {% if object and object.stop_date %}
            <label class="col-md-1 col-lg-1" for="id_stop_date">{% trans "Finished at" %}</label>
            <div class="col-md-3 col-lg-3 {% if form.stop_date.errors %}has-error{% endif %}">
                <div class="input-group date-time-picker-pf">
                    <input
                        type="text" class="form-control" id="id_stop_date" name="stop_date"
                        value="{{ form.stop_date.value|date:'Y-m-d H:i:s' }}">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar">
                        </span>
                    </span>
                </div>
                {{  form.stop_date.errors }}
            </div>
            {% else %}
                <input type="hidden" id="id_stop_date" name="stop_date" value="{{ form.stop_date.value|date:'Y-m-d H:i:s' }}">
            {% endif %}
        </div>

        {% if test_cases %}
        <div class="form-group">
            <label for="id_environment" class="col-md-1 col-lg-1">{% trans "Environment" %}</label>
            <div class="col-md-7 {% if form.environment.errors %}has-error{% endif %}">
                <select name="environment" id="id_environment" class="form-control selectpicker" multiple title="----------">
                    {% for option in form.environment.field.queryset %}
                        <option value="{{ option.pk }}" {% if option.pk|escape == form.environment.field.queryset.0.product_id|escape %}selected{% endif %}>{{ option.name }}</option>
                    {% endfor %}
                </select>

                <p class="help-block">
                    <span class="fa fa-exclamation-triangle kiwi-color-warning"></span>
                    {% trans 'This is a tech-preview feature!' %}
                </p>

                {{ form.environment.errors }}
            </div>

            <div class="col-md-1 col-lg-1">
                <label for="id_matrix_type">{% trans "Matrix" %}</label>
                <span class="fa pficon-help help-tooltip"
                    data-toggle="tooltip" data-placement="bottom"
                    title="{% trans 'Affects only test cases with parameters' %}">
                </span>
            </div>

            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_matrix_type" name="matrix_type">
                    {% for option in form.matrix_type.field.choices %}
                        <option value="{{ option.0 }}" {% if option.0 == form.matrix_type.value %}selected{% endif %}>{{ option.1 }}</option>
                    {% endfor %}
                </select>

                <p class="help-block">
                    <span class="fa fa-external-link"></span>
                    <a href="https://kiwitcms.org/blog/atodorov/2022/01/24/parameters-environments-and-test-matrix-generation/">
                        {% trans 'more information' %}
                    </a>
                </p>
            </div>
        </div>
        {% endif %}

        <div class="form-group">
            <label class="col-lg-12" for="id_notes">{% trans "Notes" %}</label>
            <div class="col-lg-12">
                <textarea class="form-control" id="id_notes" name="notes">{{ form.notes.value }}</textarea>
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-1 col-lg-1">
                <button type="submit" class="btn btn-default btn-lg">{% trans "Save" %}</button>
            </div>
        </div>

        {% if test_cases %}

        <div class="panel {% if disabled_cases %}panel-danger{% else%}panel-default{% endif%} col-lg-12 kiwi-padding-0">
            <div class="panel-heading">
                <strong>{% trans "Selected TestCase(s):" %}</strong>
                {% if disabled_cases %}
                    <span class="danger">
{% blocktrans with count=disabled_cases %}{{ count }} of the pre-selected test cases is not CONFIRMED and will not be cloned!
See test plan for more details!{% endblocktrans %}
                    </span>
                {% endif %}
            </div>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>{% trans "Summary" %}</th>
                        <th>{% trans "Author" %}</th>
                        <th>{% trans "Created on" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Category" %}</th>
                        <th>{% trans "Priority" %}</th>
                    </tr>
                </thead>

                <tbody>
                {% for test_case in test_cases %}
                    <tr>
                        <td>
                            <input type="hidden" name="case" value="{{ test_case.pk }}">
                            <a href="{% url 'testcases-get' test_case.pk %}">
                                TC-{{ test_case.pk }}: {{ test_case.summary }}
                            </a>
                        </td>
                        <td>
                            <a href="{% url 'tcms-profile' test_case.author.pk %}">{{ test_case.author.username }}</a>
                        </td>
                        <td>{{ test_case.create_date }}</td>
                        <td>{{ test_case.case_status }}</td>
                        <td>{{ test_case.category }}</td>
                        <td>{{ test_case.priority }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div> <!-- /panel -->
        {% endif %}
    </form>
</div><!-- /container -->
{% endblock %}
