version: '3'

services:
    db:
        container_name: kiwi_db
        image: mariadb:latest
        command:
            --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
        volumes:
            - db_data:/var/lib/mysql
        restart: always
        environment:
            # see "Docker Secrets" section at https://hub.docker.com/_/mysql
            MYSQL_ROOT_PASSWORD_FILE: /run/secrets/db-root-password
            MYSQL_DATABASE_FILE: /run/secrets/db-name
            MYSQL_USER_FILE: /run/secrets/db-user
            MYSQL_PASSWORD_FILE: /run/secrets/db-password
        secrets:
          - db-root-password
          - db-name
          - db-user
          - db-password

    web:
        container_name: kiwi_web
        depends_on:
            - db
        restart: always
        image: pub.kiwitcms.eu/kiwitcms/kiwi:latest
        ports:
            - 80:8080
            - 443:8443
        volumes:
            - uploads:/Kiwi/uploads:Z
            - ./tests/init-docker-secrets/:/Kiwi/secrets/:Z
        environment:
            # all of these support being defined as docker secrets
            KIWI_DB_HOST: db
            KIWI_DB_PORT: 3306
            KIWI_DB_NAME: /run/secrets/db-name
            KIWI_DB_USER: /run/secrets/db-user
            KIWI_DB_PASSWORD: /Kiwi/secrets/db-password.txt
            # or as regular files mounted inside the container
        secrets:
          - db-name
          - db-user
          - db-password
        cap_drop:
          - ALL

secrets:
   db-name:
     file: tests/init-docker-secrets/db-name.txt
   db-user:
     file: tests/init-docker-secrets/db-user.txt
   db-password:
     file: tests/init-docker-secrets/db-password.txt
   db-root-password:
     file: tests/init-docker-secrets/db-root-password.txt

volumes:
    db_data:
    uploads:
