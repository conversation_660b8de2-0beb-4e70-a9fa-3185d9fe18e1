{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Search Test Cases" %}{% endblock %}
{% block page_id %}page-testcases-search{% endblock %}

{% block contents %}
<div class="container-fluid container-cards-pf">
    <form class="form-horizontal" method="get">
        {% csrf_token %}
        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_summary">{% trans "Summary" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_summary" type="text" class="form-control" placeholder="{% trans 'Test case summary' %}"
                    value="{{ form.summary.value|default:'' }}">
            </div>

            <label class="col-md-1 col-lg-1">{% trans "Created" %}</label>
            <div class="col-md-3 col-lg-3">
                <div class="input-group date-time-picker-pf">
                    <span class="input-group-addon">{% trans "Before" %}</span>
                    <input type="text" class="form-control" id="id_before">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>

                    <span class="input-group-addon">{% trans "After" %}</span>
                    <input type="text" class="form-control" id="id_after">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>
                </div>
            </div>

            <label class="col-md-1 col-lg-1">{% trans "Automated" %}</label>
            <div class="col-md-3 col-lg-3">
                <input type="radio" name="is_automated" value="true">
                <label>{% trans "Yes" %}</label>

                <input type="radio" name="is_automated" value="false">
                <label>{% trans "No" %}</label>

                <input type="radio" name="is_automated" checked value="both">
                <label class="active">{% trans "Both" %}</label>
            </div>

        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_product">{% trans "Product" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_product">
                    <option value="">----------</option>
                {% for option in form.product.field.queryset %}
                    <option value="{{ option.pk }}" {% if form.product.value|add:'0' == option.pk %}selected{% endif %}>{{ option.name }}</option>
                {% endfor %}
                </select>

                <p class="help-block">
                    {% trans "include in search request" %}

                    <input class="bootstrap-switch" id="include_product" type="checkbox"
                        checked data-size="mini" data-on-text="{% trans 'Yes' %}" data-off-text="{% trans 'No' %}">
                </p>
            </div>

            <label for="id_test_plan" class="col-md-1 col-lg-1">{% trans "Test plan" %}</label>
            <div class="col-md-3">
                <select name="test_plan" id="id_test_plan" class="form-control selectpicker" multiple title="----------">
                </select>

                <p class="help-block">
                    {% trans "include child test plans" %}
                    <input class="bootstrap-switch" id="id_include_child_tps" type="checkbox"
                        data-size="mini" data-on-text="{% trans 'Yes' %}" data-off-text="{% trans 'No' %}">
                </p>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_priority">{% trans "Priority" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_priority" multiple>
                {% for option in form.priority.field.queryset %}
                    <option value="{{ option.pk }}">{{ option.value }}</option>
                {% endfor %}
                </select>
            </div>

            <label class="col-md-1 col-lg-1" for="id_status">{% trans "Status" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_status" multiple>
                {% for option in form.case_status.field.queryset %}
                    <option value="{{ option.pk }}" {% if option.pk|stringformat:"i" in form.case_status.value %}selected{% endif %}>{{ option.name }}</option>
                {% endfor %}
                </select>
            </div>

            <label class="col-md-1 col-lg-1" for="id_category">{% trans "Category" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_category">
                    <option value="">----------</option>
                {% for option in form.category.field.queryset %}
                    <option value="{{ option.pk }}">{{ option.name }}</option>
                {% endfor %}
                </select>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_component">{% trans "Component" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_component">
                    <option value="">----------</option>
                {% for option in form.component.field.queryset %}
                    <option value="{{ option.pk }}">{{ option.name }}</option>
                {% endfor %}
                </select>
            </div>

            <label class="col-md-1 col-lg-1" for="id_author">{% trans "Author" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_author" type="text" class="form-control" placeholder="{% trans 'Username' %}"
                    value="{{ form.author.value|default:'' }}">
            </div>

            <label class="col-md-1 col-lg-1" for="id_text">{% trans "Text" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_text" type="text" class="form-control"
                    value="{{ form.text.value|default:'' }}">
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_tag">{% trans "Tag" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_tag" type="text" class="form-control" value="{{ form.tag.value.0|default:'' }}">
                <p class="help-block">{% trans "Separate multiple values with comma (,)" %}</p>
            </div>

            <label class="col-md-1 col-lg-1" for="id_run">{% trans "Test run" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_run" type="text" class="form-control" placeholder="{% trans 'ID' %}" value="">
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-1 col-lg-1">
                <button id="btn_search" type="submit" class="btn btn-default btn-lg">{% trans "Search" %}</button>
            </div>
        </div>
    </form>

    <div class="panel panel-default">
        <!-- Table HTML -->
        <table class="table table-striped table-bordered table-hover" id="resultsTable">
            <thead>
                <tr>
                    <th class="js-select-checkbox"><input id="check-all" type="checkbox"></th>
                    <th>{% trans "ID" %}</th>
                    <th>{% trans "Summary" %}</th>
                    <th>{% trans "Created on" %}</th>
                    <th>{% trans "Category" %}</th>
                    <th>{% trans "Component" %}</th>
                    <th>{% trans "Priority" %}</th>
                    <th>{% trans "Status" %}</th>
                    <th>{% trans "Automated" %}</th>
                    <th>{% trans "Author" %}</th>
                    <th>{% trans "Default tester" %}</th>
                    <th>{% trans "Tags" %}</th>
                </tr>
            </thead>
        </table>

        {% if REQUEST__ALLOW_SELECT %}
        <div class="form-group">
            <button id="select-btn" type="submit" class="btn btn-primary">{% trans "Select" %}</button>
        </div>
        {% endif %}
    </div>
</div><!-- /container -->
{% endblock %}
