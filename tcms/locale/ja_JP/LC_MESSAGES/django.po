msgid ""
msgstr ""
"Project-Id-Version: kiwitcms\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-30 12:22+0000\n"
"PO-Revision-Date: 2025-07-30 13:13\n"
"Last-Translator: \n"
"Language-Team: Japanese\n"
"Language: ja_JP\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: kiwitcms\n"
"X-Crowdin-Project-ID: 295734\n"
"X-Crowdin-Language: ja\n"
"X-Crowdin-File: /master/tcms/locale/en/LC_MESSAGES/django.po\n"
"X-Crowdin-File-ID: 19\n"

#: tcms/bugs/forms.py:33
msgid "Description of problem:\n\n\n"
"How often reproducible:\n\n\n"
"Steps to Reproduce:\n"
"1.\n"
"2.\n"
"3.\n\n"
"Actual results:\n\n\n"
"Expected results:\n\n\n"
"Additional info:"
msgstr "問題の説明：\n\n\n"
"どのくらいの頻度で再現可能か：\n\n\n"
"再現する手順：\n"
"1. \n"
"2. \n"
"3. \n\n"
"実際の結果：\n\n\n"
"期待する結果：\n\n\n"
"追加情報："

#: tcms/bugs/models.py:24 tcms/bugs/templates/bugs/get.html:50
#: tcms/bugs/templates/bugs/mutable.html:27
#: tcms/bugs/templates/bugs/search.html:18
#: tcms/bugs/templates/bugs/search.html:108
msgid "Severity"
msgstr "深刻度"

#: tcms/bugs/templates/bugs/get.html:37 tcms/bugs/templates/bugs/search.html:89
#: tcms/issuetracker/kiwitcms.py:49 tcms/templates/include/bug_details.html:3
msgid "Open"
msgstr "オープン"

#: tcms/bugs/templates/bugs/get.html:39 tcms/issuetracker/kiwitcms.py:49
#: tcms/templates/include/bug_details.html:3
msgid "Closed"
msgstr "クローズ"

#: tcms/bugs/templates/bugs/get.html:59 tcms/bugs/templates/bugs/search.html:79
#: tcms/bugs/templates/bugs/search.html:114
#: tcms/templates/include/bug_details.html:7
msgid "Reporter"
msgstr "報告者"

#: tcms/bugs/templates/bugs/get.html:64
#: tcms/bugs/templates/bugs/mutable.html:94
#: tcms/bugs/templates/bugs/search.html:84
#: tcms/bugs/templates/bugs/search.html:115
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:36
#: tcms/templates/include/bug_details.html:10
#: tcms/testruns/templates/testruns/get.html:207
#: tcms/testruns/templates/testruns/get.html:267
msgid "Assignee"
msgstr "担当者"

#: tcms/bugs/templates/bugs/get.html:75
#: tcms/bugs/templates/bugs/mutable.html:45
#: tcms/bugs/templates/bugs/search.html:47
#: tcms/bugs/templates/bugs/search.html:111
#: tcms/core/templates/dashboard.html:53 tcms/management/admin.py:107
#: tcms/telemetry/templates/telemetry/include/filters.html:8
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:29
#: tcms/templates/include/bug_details.html:13
#: tcms/testcases/templates/testcases/get.html:44
#: tcms/testcases/templates/testcases/get.html:170
#: tcms/testcases/templates/testcases/mutable.html:43
#: tcms/testcases/templates/testcases/search.html:51
#: tcms/testplans/templates/testplans/clone.html:21
#: tcms/testplans/templates/testplans/get.html:64
#: tcms/testplans/templates/testplans/mutable.html:33
#: tcms/testplans/templates/testplans/search.html:50
#: tcms/testplans/templates/testplans/search.html:117
#: tcms/testruns/templates/testruns/get.html:45
#: tcms/testruns/templates/testruns/mutable.html:46
#: tcms/testruns/templates/testruns/search.html:35
#: tcms/testruns/templates/testruns/search.html:183
msgid "Product"
msgstr "プロダクト"

#: tcms/bugs/templates/bugs/get.html:80
#: tcms/bugs/templates/bugs/mutable.html:60
#: tcms/bugs/templates/bugs/search.html:57
#: tcms/bugs/templates/bugs/search.html:112
#: tcms/telemetry/templates/telemetry/include/filters.html:17
#: tcms/templates/include/bug_details.html:15 tcms/templates/navbar.html:71
#: tcms/testplans/templates/testplans/clone.html:36
#: tcms/testplans/templates/testplans/get.html:70
#: tcms/testplans/templates/testplans/mutable.html:48
#: tcms/testplans/templates/testplans/search.html:60
#: tcms/testplans/templates/testplans/search.html:118
#: tcms/testruns/templates/testruns/get.html:50
#: tcms/testruns/templates/testruns/search.html:45
#: tcms/testruns/templates/testruns/search.html:184
msgid "Version"
msgstr "バージョン"

#: tcms/bugs/templates/bugs/get.html:85
#: tcms/bugs/templates/bugs/mutable.html:78
#: tcms/bugs/templates/bugs/search.html:67
#: tcms/bugs/templates/bugs/search.html:113 tcms/management/models.py:132
#: tcms/telemetry/templates/telemetry/include/filters.html:25
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:30
#: tcms/templates/include/bug_details.html:17
#: tcms/testruns/templates/testruns/get.html:55
#: tcms/testruns/templates/testruns/get.html:414
#: tcms/testruns/templates/testruns/mutable.html:89
#: tcms/testruns/templates/testruns/search.html:55
#: tcms/testruns/templates/testruns/search.html:185
msgid "Build"
msgstr "ビルド"

#: tcms/bugs/templates/bugs/get.html:119
msgid "commented on"
msgstr "コメント"

#: tcms/bugs/templates/bugs/get.html:149
msgid "Reopen"
msgstr "再オープン"

#: tcms/bugs/templates/bugs/get.html:151
#: tcms/bugs/templates/bugs/mutable.html:114
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:78
#: tcms/testcases/templates/testcases/mutable.html:263
#: tcms/testplans/templates/testplans/get.html:477
#: tcms/testplans/templates/testplans/mutable.html:173
#: tcms/testruns/templates/testruns/get.html:480
#: tcms/testruns/templates/testruns/get.html:593
#: tcms/testruns/templates/testruns/mutable.html:210
msgid "Save"
msgstr "保存"

#: tcms/bugs/templates/bugs/get.html:153
msgid "Close"
msgstr "クローズ"

#: tcms/bugs/templates/bugs/mutable.html:20
#: tcms/bugs/templates/bugs/search.html:13
#: tcms/bugs/templates/bugs/search.html:109
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:27
#: tcms/testcases/templates/testcases/mutable.html:24
#: tcms/testcases/templates/testcases/search.html:13
#: tcms/testcases/templates/testcases/search.html:162
#: tcms/testplans/templates/testplans/get.html:212
#: tcms/testplans/templates/testplans/get.html:487
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:3
#: tcms/testruns/templates/testruns/get.html:251
#: tcms/testruns/templates/testruns/get.html:258
#: tcms/testruns/templates/testruns/get.html:261
#: tcms/testruns/templates/testruns/mutable.html:23
#: tcms/testruns/templates/testruns/mutable.html:229
#: tcms/testruns/templates/testruns/search.html:14
#: tcms/testruns/templates/testruns/search.html:181
msgid "Summary"
msgstr "概要"

#: tcms/bugs/templates/bugs/mutable.html:28
msgid "add new Severity"
msgstr "新しい深刻度を追加"

#: tcms/bugs/templates/bugs/mutable.html:46
#: tcms/testcases/templates/testcases/mutable.html:44
#: tcms/testplans/templates/testplans/clone.html:22
#: tcms/testplans/templates/testplans/mutable.html:34
#: tcms/testruns/templates/testruns/mutable.html:48
msgid "add new Product"
msgstr "新しいプロダクトを追加"

#: tcms/bugs/templates/bugs/mutable.html:62
#: tcms/testplans/templates/testplans/clone.html:38
#: tcms/testplans/templates/testplans/mutable.html:50
msgid "add new Version"
msgstr "新しいバージョンを追加"

#: tcms/bugs/templates/bugs/mutable.html:81
#: tcms/bugs/templates/bugs/mutable.html:82
#: tcms/testruns/templates/testruns/mutable.html:93
#: tcms/testruns/templates/testruns/mutable.html:94
msgid "add new Build"
msgstr "新しいビルドを追加"

#: tcms/bugs/templates/bugs/mutable.html:98
#: tcms/testruns/templates/testruns/mutable.html:32
#: tcms/testruns/templates/testruns/mutable.html:39
msgid "Username or email"
msgstr "ユーザー名またはメール"

#: tcms/bugs/templates/bugs/search.html:5 tcms/settings/common.py:414
msgid "Search Bugs"
msgstr "バグを検索"

#: tcms/bugs/templates/bugs/search.html:28
#: tcms/testcases/templates/testcases/search.html:19
#: tcms/testplans/templates/testplans/search.html:24
msgid "Created"
msgstr "作成日"

#: tcms/bugs/templates/bugs/search.html:31
#: tcms/telemetry/templates/telemetry/include/filters.html:53
#: tcms/testcases/templates/testcases/search.html:22
#: tcms/testplans/templates/testplans/search.html:27
#: tcms/testruns/templates/testruns/search.html:90
#: tcms/testruns/templates/testruns/search.html:110
#: tcms/testruns/templates/testruns/search.html:131
#: tcms/testruns/templates/testruns/search.html:151
msgid "Before"
msgstr "以前"

#: tcms/bugs/templates/bugs/search.html:37
#: tcms/telemetry/templates/telemetry/include/filters.html:43
#: tcms/testcases/templates/testcases/search.html:28
#: tcms/testplans/templates/testplans/search.html:33
#: tcms/testruns/templates/testruns/search.html:97
#: tcms/testruns/templates/testruns/search.html:117
#: tcms/testruns/templates/testruns/search.html:138
#: tcms/testruns/templates/testruns/search.html:158
msgid "After"
msgstr "以後"

#: tcms/bugs/templates/bugs/search.html:81
#: tcms/bugs/templates/bugs/search.html:86
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:45
#: tcms/templates/registration/login.html:17
#: tcms/templates/registration/registration_form.html:15
#: tcms/testcases/templates/testcases/search.html:124
#: tcms/testplans/templates/testplans/search.html:84
#: tcms/testplans/templates/testplans/search.html:90
#: tcms/testruns/templates/testruns/get.html:524
#: tcms/testruns/templates/testruns/search.html:69
#: tcms/testruns/templates/testruns/search.html:75
msgid "Username"
msgstr "ユーザー名"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:39
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "Yes"
msgstr "はい"

#: tcms/bugs/templates/bugs/search.html:91
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:18
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:18
#: tcms/testcases/templates/testcases/clone.html:22
#: tcms/testcases/templates/testcases/clone.html:36
#: tcms/testcases/templates/testcases/mutable.html:100
#: tcms/testcases/templates/testcases/mutable.html:187
#: tcms/testcases/templates/testcases/mutable.html:194
#: tcms/testcases/templates/testcases/mutable.html:201
#: tcms/testcases/templates/testcases/mutable.html:210
#: tcms/testcases/templates/testcases/mutable.html:217
#: tcms/testcases/templates/testcases/mutable.html:232
#: tcms/testcases/templates/testcases/mutable.html:239
#: tcms/testcases/templates/testcases/search.html:42
#: tcms/testcases/templates/testcases/search.html:64
#: tcms/testcases/templates/testcases/search.html:76
#: tcms/testplans/templates/testplans/clone.html:58
#: tcms/testplans/templates/testplans/clone.html:66
#: tcms/testplans/templates/testplans/mutable.html:124
#: tcms/testplans/templates/testplans/mutable.html:130
#: tcms/testplans/templates/testplans/mutable.html:136
#: tcms/testplans/templates/testplans/mutable.html:148
#: tcms/testplans/templates/testplans/mutable.html:154
#: tcms/testplans/templates/testplans/mutable.html:167
#: tcms/testplans/templates/testplans/search.html:44
#: tcms/testruns/templates/testruns/get.html:587
#: tcms/testruns/templates/testruns/search.html:81
msgid "No"
msgstr "いいえ"

#: tcms/bugs/templates/bugs/search.html:97
#: tcms/testcases/templates/testcases/search.html:150
#: tcms/testplans/templates/testplans/get.html:221
#: tcms/testplans/templates/testplans/search.html:103
#: tcms/testruns/templates/testruns/get.html:273
#: tcms/testruns/templates/testruns/search.html:170
msgid "Search"
msgstr "検索"

#: tcms/bugs/templates/bugs/search.html:107
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:26
#: tcms/testcases/templates/testcases/get.html:166
#: tcms/testcases/templates/testcases/search.html:144
#: tcms/testcases/templates/testcases/search.html:161
#: tcms/testplans/templates/testplans/search.html:114
#: tcms/testruns/templates/testruns/search.html:180
msgid "ID"
msgstr "ID"

#: tcms/bugs/templates/bugs/search.html:110
#: tcms/templates/include/bug_details.html:5
#: tcms/testcases/templates/testcases/search.html:163
#: tcms/testplans/templates/testplans/search.html:116
#: tcms/testruns/templates/testruns/mutable.html:231
msgid "Created on"
msgstr "作成日時"

#: tcms/bugs/views.py:42 tcms/testcases/views.py:130
#: tcms/testplans/templates/testplans/get.html:380 tcms/testplans/views.py:143
#: tcms/testruns/views.py:194 tcms/testruns/views.py:308
msgid "Edit"
msgstr "編集"

#: tcms/bugs/views.py:45 tcms/testcases/views.py:143
#: tcms/testplans/views.py:151 tcms/testruns/views.py:207
#: tcms/testruns/views.py:316
msgid "Object permissions"
msgstr "オブジェクトの権限"

#: tcms/bugs/views.py:50
#: tcms/templates/include/comments_for_object_template.html:10
#: tcms/templates/include/properties_card.html:32
#: tcms/templates/include/properties_card.html:47 tcms/testcases/views.py:151
#: tcms/testplans/templates/testplans/get.html:193
#: tcms/testplans/templates/testplans/get.html:384 tcms/testplans/views.py:159
#: tcms/testruns/templates/testruns/get.html:238
#: tcms/testruns/templates/testruns/get.html:450 tcms/testruns/views.py:215
#: tcms/testruns/views.py:324
msgid "Delete"
msgstr "削除"

#: tcms/bugs/views.py:68 tcms/settings/common.py:400
msgid "New Bug"
msgstr "新しいバグ"

#: tcms/bugs/views.py:188
msgid "Edit bug"
msgstr "バグを編集"

#: tcms/bugs/views.py:231
msgid "*bug closed*"
msgstr "*バグはクローズされました。*"

#: tcms/bugs/views.py:235
msgid "*bug reopened*"
msgstr "*バグが再オープンされました。*"

#: tcms/core/history.py:52
#, python-format
msgid "UPDATE: %(model_name)s #%(pk)d - %(title)s"
msgstr "更新: %(model_name)s #%(pk)d - %(title)s"

#: tcms/core/history.py:62
#, python-format
msgid "Updated on %(history_date)s\n"
"Updated by %(username)s\n\n"
"%(diff)s\n\n"
"For more information:\n"
"%(instance_url)s"
msgstr "更新日: %(history_date)s\n"
"更新者: %(username)s\n\n"
"%(diff)s\n\n"
"詳細情報:\n"
"%(instance_url)s"

#: tcms/core/templates/dashboard.html:3 tcms/settings/common.py:430
msgid "Dashboard"
msgstr "ダッシュボード"

#: tcms/core/templates/dashboard.html:8
#: tcms/testruns/templates/testruns/get.html:153
msgid "Test executions"
msgstr "テスト実行"

#: tcms/core/templates/dashboard.html:15
#, python-format
msgid "%(amount)s%% complete"
msgstr "%(amount)s%% コンプリート"

#: tcms/core/templates/dashboard.html:24
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:38
#: tcms/testruns/templates/testruns/get.html:78
msgid "Started at"
msgstr "開始日時"

#: tcms/core/templates/dashboard.html:32
#, python-format
msgid "%(total_count)s TestRun(s) or TestCase(s) assigned to you need to be executed.\n"
"Here are the latest %(count)s."
msgstr "%(total_count)s TestRun(s) または TestCase(s) が割り当てられている必要があります。\n"
"最新の %(count)s です。"

#: tcms/core/templates/dashboard.html:36 tcms/core/templates/dashboard.html:80
msgid "SEE ALL"
msgstr "すべて見る"

#: tcms/core/templates/dashboard.html:39
msgid "There are no TestRun(s) assigned to you"
msgstr "あなたに割り当てられたテストランはありません"

#: tcms/core/templates/dashboard.html:46
msgid "Your Test plans"
msgstr "テスト計画"

#: tcms/core/templates/dashboard.html:52
msgid "TestPlan"
msgstr "テスト計画"

#: tcms/core/templates/dashboard.html:54
#: tcms/testcases/templates/testcases/get.html:169
#: tcms/testplans/templates/testplans/mutable.html:66
#: tcms/testplans/templates/testplans/search.html:70
#: tcms/testplans/templates/testplans/search.html:119
msgid "Type"
msgstr "タイプ"

#: tcms/core/templates/dashboard.html:55
#: tcms/templates/include/tc_executions.html:7
msgid "Executions"
msgstr "実行"

#: tcms/core/templates/dashboard.html:76
#, python-format
msgid "You manage %(total_count)s TestPlan(s), %(disabled_count)s are disabled.\n"
"Here are the latest %(count)s."
msgstr "%(total_count)s TestPlan(s), %(disabled_count)s が無効になっています。\n"
"こちらが最新の %(count)s です。"

#: tcms/core/templates/dashboard.html:83
msgid "There are no TestPlan(s) that belong to you"
msgstr "あなたに属するテスト計画はありません"

#: tcms/core/views.py:47
#, python-format
msgid "Base URL is not configured! See <a href=\"%(doc_url)s\">documentation</a> and <a href=\"%(admin_url)s\">change it</a>"
msgstr "ベースURLが設定されていません！ <a href=\"%(doc_url)s\">ドキュメント</a> を参照し、 <a href=\"%(admin_url)s\">変更してください。</a>"

#: tcms/core/views.py:71
#, python-format
msgid "You have %(unapplied_migration_count)s unapplied migration(s). See <a href=\"%(doc_url)s\">documentation</a>"
msgstr "%(unapplied_migration_count)s 件の未適用の移行があります。 <a href=\"%(doc_url)s\">ドキュメント</a> を参照してください。"

#: tcms/core/views.py:92
#, python-format
msgid "You are not using a secure connection. See <a href=\"%(doc_url)s\">documentation</a> and enable SSL."
msgstr "セキュアな接続を使用していません。 <a href=\"%(doc_url)s\">ドキュメント</a> を参照して、SSL を有効にしてください。"

#: tcms/kiwi_attachments/validators.py:10
#, python-brace-format
msgid "File contains forbidden tag: <{tag_name}>"
msgstr "ファイルに禁止タグが含まれています: <{tag_name}>"

#: tcms/kiwi_attachments/validators.py:92
#, python-brace-format
msgid "File contains forbidden attribute: `{attr_name}`"
msgstr "ファイルに禁止属性が含まれています: `{attr_name}`"

#: tcms/kiwi_attachments/validators.py:97
msgid "Uploading executable files is forbidden"
msgstr "実行可能ファイルのアップロードは禁止されています"

#: tcms/kiwi_auth/admin.py:36 tcms/settings/common.py:443
msgid "Users"
msgstr "ユーザー"

#: tcms/kiwi_auth/admin.py:82
#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:5
#: tcms/templates/navbar.html:102
msgid "Reset email address"
msgstr ""

#: tcms/kiwi_auth/admin.py:148
msgid "Personal info"
msgstr "個人情報"

#: tcms/kiwi_auth/admin.py:150
msgid "Permissions"
msgstr "権限"

#: tcms/kiwi_auth/admin.py:187
msgid "This is the last superuser, it cannot be deleted!"
msgstr "これは最後のスーパーユーザーであり、削除できません！"

#: tcms/kiwi_auth/forms.py:28
msgid "A user with that email already exists."
msgstr "このメールのユーザーはすでに存在しています。"

#: tcms/kiwi_auth/forms.py:48
msgid "Please confirm your Kiwi TCMS account email address"
msgstr ""

#: tcms/kiwi_auth/forms.py:142
msgid "Email mismatch"
msgstr ""

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:14
msgid "Warning"
msgstr ""

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:19
msgid "After clicking the 'Save' button your account will become <strong>inactive</strong>\n"
"and you will be <strong>logged out</strong>! A confirmation email will be sent to the newly specified address!<br>\n"
"Double check that your new email address is <strong>entered correctly</strong> otherwise\n"
"<strong>you may be left locked out</strong> of your account!\n"
"After following the activation link you will be able to log in as usual!"
msgstr ""

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:51
msgid "NOT yourself"
msgstr ""

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:58
#: tcms/templates/registration/password_reset_form.html:16
#: tcms/templates/registration/registration_form.html:39
msgid "E-mail"
msgstr "Ｅメール"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:66
#: tcms/templates/registration/password_reset_confirm.html:22
#: tcms/templates/registration/registration_form.html:31
msgid "Confirm"
msgstr "確定する"

#: tcms/kiwi_auth/templates/accounts/reset_user_email.html:71
msgid "Please type! Do not copy-and-paste value from previous field!"
msgstr ""

#: tcms/kiwi_auth/views.py:70
msgid "Your account has been created, please check your mailbox for confirmation"
msgstr "あなたのアカウントが作成されました。確認用のリンクについては、メールをチェックしてください"

#: tcms/kiwi_auth/views.py:75
msgid "Your account has been created, but you need an administrator to activate it"
msgstr "アカウントが作成されましたが、有効化するには管理者の確認が必要です。"

#: tcms/kiwi_auth/views.py:80
msgid "Following is the administrator list"
msgstr "管理者リストは次のとおりです。"

#: tcms/kiwi_auth/views.py:123
msgid "This activation key no longer exists in the database"
msgstr "このアクティベーションキーはデータベースに存在しません。"

#: tcms/kiwi_auth/views.py:129
msgid "This activation key has expired"
msgstr "アクティベーションキーの有効期限が切れました。"

#: tcms/kiwi_auth/views.py:141
msgid "Your account has been activated successfully"
msgstr "アカウントがアクティブになりました。"

#: tcms/kiwi_auth/views.py:168 tcms/kiwi_auth/views.py:177
#: tcms/kiwi_auth/views.py:196 tcms/kiwi_auth/views.py:205
#, python-format
msgid "You are viewing records from tenant '%s'"
msgstr "テナントの '%s' からレコードを表示しています"

#: tcms/kiwi_auth/views.py:256
msgid "Email address has been reset, please check inbox for further instructions"
msgstr ""

#: tcms/management/models.py:58
#: tcms/telemetry/templates/telemetry/testing/breakdown.html:34
msgid "Priorities"
msgstr "優先順位"

#: tcms/management/models.py:133
msgid "Builds"
msgstr "ビルド"

#: tcms/management/models.py:144
#: tcms/testcases/templates/testcases/search.html:136
#: tcms/testplans/templates/testplans/get.html:217
#: tcms/testplans/templates/testplans/search.html:94
#: tcms/testruns/templates/testruns/get.html:263
#: tcms/testruns/templates/testruns/search.html:27
msgid "Tag"
msgstr "タグ"

#: tcms/management/models.py:145 tcms/templates/include/tags_card.html:6
#: tcms/testcases/templates/testcases/search.html:171
#: tcms/testplans/templates/testplans/get.html:456
#: tcms/testplans/templates/testplans/search.html:121
#: tcms/testruns/templates/testruns/get.html:353
#: tcms/testruns/templates/testruns/search.html:190
msgid "Tags"
msgstr "タグ"

#: tcms/rpc/api/bug.py:69
msgid "Enable reporting to this Issue Tracker by configuring its base_url!"
msgstr "base_urlを設定することで、この課題トラッカーへの報告を有効にします！"

#: tcms/rpc/api/forms/__init__.py:9
msgid "Invalid date format. Expected YYYY-MM-DD [HH:MM:SS]."
msgstr "不正な日付フォーマットです。期待値 YYYY-MM-DD [HH:MM:SS]。"

#: tcms/settings/common.py:391
msgid "TESTING"
msgstr "テスト中"

#: tcms/settings/common.py:393 tcms/testruns/templates/testruns/mutable.html:70
msgid "New Test Plan"
msgstr "新しいテスト計画"

#: tcms/settings/common.py:395
#: tcms/testcases/templates/testcases/mutable.html:12
#: tcms/testplans/templates/testplans/get.html:131
msgid "New Test Case"
msgstr "新しいテストケース"

#: tcms/settings/common.py:397 tcms/testplans/templates/testplans/get.html:121
#: tcms/testruns/templates/testruns/get.html:172
#: tcms/testruns/templates/testruns/mutable.html:11
msgid "New Test Run"
msgstr "新しいテスト実行"

#: tcms/settings/common.py:407
msgid "SEARCH"
msgstr "検索"

#: tcms/settings/common.py:409 tcms/testplans/templates/testplans/search.html:5
msgid "Search Test Plans"
msgstr "テスト計画の検索"

#: tcms/settings/common.py:410 tcms/testcases/templates/testcases/search.html:5
msgid "Search Test Cases"
msgstr "テストケースを検索"

#: tcms/settings/common.py:411 tcms/testruns/templates/testruns/search.html:5
msgid "Search Test Runs"
msgstr "テスト実行を検索"

#: tcms/settings/common.py:412
msgid "Search Test Executions"
msgstr ""

#: tcms/settings/common.py:421
msgid "TELEMETRY"
msgstr "テレメトリー"

#: tcms/settings/common.py:424
msgid "Testing"
msgstr "テスト中"

#: tcms/settings/common.py:426
msgid "Breakdown"
msgstr "ブレイクダウン"

#: tcms/settings/common.py:428
msgid "Execution"
msgstr "実行"

#: tcms/settings/common.py:431
#: tcms/testruns/templates/testruns/mutable.html:177
msgid "Matrix"
msgstr "マトリクス"

#: tcms/settings/common.py:432
msgid "Trends"
msgstr "トレンド"

#: tcms/settings/common.py:435
#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:5
msgid "TestCase health"
msgstr "テストケースの状況"

#: tcms/settings/common.py:441
msgid "ADMIN"
msgstr "管理者"

#: tcms/settings/common.py:444
msgid "Groups"
msgstr "グループ"

#: tcms/settings/common.py:446
msgid "Everything else"
msgstr "他のすべて"

#: tcms/settings/common.py:449
msgid "MORE"
msgstr ""

#: tcms/settings/common.py:459
msgid "Report an Issue"
msgstr "問題を報告する"

#: tcms/settings/common.py:462
msgid "Ask for help on StackOverflow"
msgstr "StackOverflowで助けを求める"

#: tcms/settings/common.py:466
msgid "Donate €5 via Open Collective"
msgstr "Open Collective経由で€5を寄付する"

#: tcms/settings/common.py:468
msgid "Administration Guide"
msgstr "管理ガイド"

#: tcms/settings/common.py:469
msgid "User Guide"
msgstr "ユーザーガイド"

#: tcms/settings/common.py:470
msgid "API Help"
msgstr "API ヘルプ"

#: tcms/signals.py:85
msgid "New user awaiting approval"
msgstr "承認待ちの新規ユーザー"

#: tcms/signals.py:163
#, python-format
msgid "NEW: TestRun #%(pk)d - %(summary)s"
msgstr "新着: テストラン #%(pk)d - %(summary)s"

#: tcms/signals.py:235
#, python-format
msgid "Bug #%(pk)d - %(summary)s"
msgstr "バグ #%(pk)d - %(summary)s"

#: tcms/telemetry/api.py:60 testcases.TestCaseStatus/name:2
msgid "CONFIRMED"
msgstr "確認済み"

#: tcms/telemetry/api.py:61
msgid "OTHER"
msgstr "その他"

#: tcms/telemetry/api.py:133 tcms/telemetry/api.py:185
#: tcms/telemetry/api.py:191
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:9
#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:13
#: tcms/testruns/templates/testruns/get.html:129
msgid "TOTAL"
msgstr "合計"

#: tcms/telemetry/templates/telemetry/include/filters.html:36
#: tcms/testcases/templates/testcases/search.html:68
#: tcms/testplans/templates/testplans/search.html:115
#: tcms/testruns/templates/testruns/get.html:40
#: tcms/testruns/templates/testruns/mutable.html:67
#: tcms/testruns/templates/testruns/search.html:182
msgid "Test plan"
msgstr "テスト計画"

#: tcms/telemetry/templates/telemetry/include/filters.html:66
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
#: tcms/testcases/templates/testcases/search.html:142
msgid "Test run"
msgstr "テストの実行"

#: tcms/telemetry/templates/telemetry/include/filters.html:70
#: tcms/testruns/templates/testruns/search.html:17
msgid "Test run summary"
msgstr "テスト実行の概要"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:5
msgid "Testing Breakdown"
msgstr "テストの分析"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:14
msgid "Total"
msgstr "合計"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:24
#: tcms/testcases/templates/testcases/get.html:93
#: tcms/testcases/templates/testcases/mutable.html:98
#: tcms/testcases/templates/testcases/search.html:36
#: tcms/testcases/templates/testcases/search.html:168
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testplans/templates/testplans/get.html:489
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:5
#: tcms/testruns/templates/testruns/get.html:264
#: tcms/testruns/templates/testruns/get.html:361
msgid "Automated"
msgstr "自動化"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:28
#: tcms/testplans/templates/testplans/get.html:402
#: tcms/testruns/templates/testruns/get.html:361
msgid "Manual"
msgstr "手動"

#: tcms/telemetry/templates/telemetry/testing/breakdown.html:39
#: tcms/testcases/models.py:42
msgid "Categories"
msgstr "カテゴリ"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:5
msgid "Execution Dashboard"
msgstr "実行ダッシュボード"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:15
#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:15
msgid "Child TPs"
msgstr "子TPs"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:28
#: tcms/templates/include/bug_details.html:3
#: tcms/testcases/templates/testcases/get.html:59
#: tcms/testcases/templates/testcases/mutable.html:74
#: tcms/testcases/templates/testcases/search.html:91
#: tcms/testcases/templates/testcases/search.html:167
#: tcms/testplans/templates/testplans/get.html:149
#: tcms/testplans/templates/testplans/get.html:363
#: tcms/testplans/templates/testplans/get.html:396
#: tcms/testplans/templates/testplans/get.html:488
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:4
#: tcms/testruns/templates/testruns/get.html:189
#: tcms/testruns/templates/testruns/get.html:269
#: tcms/testruns/templates/testruns/get.html:385
#: tcms/testruns/templates/testruns/mutable.html:232
msgid "Status"
msgstr "状態"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:31
#: tcms/testcases/templates/testcases/get.html:208
#: tcms/testplans/templates/testplans/get.html:447
#: tcms/testruns/templates/testruns/get.html:349
msgid "Components"
msgstr "コンポーネント"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
#: tcms/testcases/templates/testcases/get.html:33
#: tcms/testcases/templates/testcases/mutable.html:36
#: tcms/testcases/templates/testcases/mutable.html:207
#: tcms/testcases/templates/testcases/search.html:170
#: tcms/testplans/templates/testplans/get.html:175
#: tcms/testplans/templates/testplans/get.html:378
#: tcms/testplans/templates/testplans/get.html:416
#: tcms/testplans/templates/testplans/get.html:493
#: tcms/testplans/templates/testplans/mutable.html:133
#: tcms/testplans/templates/testplans/search.html:88
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:9
#: tcms/testruns/templates/testruns/get.html:66
#: tcms/testruns/templates/testruns/mutable.html:36
#: tcms/testruns/templates/testruns/search.html:73
#: tcms/testruns/templates/testruns/search.html:189
msgid "Default tester"
msgstr "デフォルトのテスター"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:33
msgid "TC"
msgstr ""

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:34
msgid "TR"
msgstr ""

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:37
#: tcms/testruns/templates/testruns/get.html:268
#: tcms/testruns/templates/testruns/get.html:375
msgid "Tested by"
msgstr "によってテストされた"

#: tcms/telemetry/templates/telemetry/testing/execution-dashboard.html:39
#: tcms/testruns/templates/testruns/get.html:96
#: tcms/testruns/templates/testruns/get.html:405
#: tcms/testruns/templates/testruns/mutable.html:140
msgid "Finished at"
msgstr "完了"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:5
msgid "Execution Trends"
msgstr "実行のトレンド"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:21
msgid "Positive"
msgstr "ポジティブ"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:24
msgid "Neutral"
msgstr "ニュートラル"

#: tcms/telemetry/templates/telemetry/testing/execution-trends.html:27
msgid "Negative"
msgstr "ネガティブ"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:5
msgid "Execution Matrix"
msgstr "実行マトリクス"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:21
msgid "Order"
msgstr "順序"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Ascending"
msgstr "昇順"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:24
msgid "Descending"
msgstr "降順"

#: tcms/telemetry/templates/telemetry/testing/status-matrix.html:32
msgid "Test case"
msgstr "テストケース"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:12
msgid "Most frequently failing test cases"
msgstr "最も頻繁に失敗するテストケース"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:16
msgid "Test Case"
msgstr "テストケース"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:18
msgid "Failed executions"
msgstr "失敗した実行"

#: tcms/telemetry/templates/telemetry/testing/test-case-health.html:19
#, python-format
msgid "%% of failed executions"
msgstr "%% 失敗した実行"

#: tcms/templates/404.html:5 tcms/templates/404.html:16
msgid "Page not found"
msgstr "ページが見つかりませんでした。"

#: tcms/templates/500.html:5 tcms/templates/500.html:16
msgid "Internal Server Error"
msgstr "内部サーバーエラー"

#: tcms/templates/attachments/add.html:3
msgid "Attachment upload error"
msgstr "添付ファイルのアップロードエラー"

#: tcms/templates/attachments/delete_link.html:3
msgid "Are you sure you want to delete this attachment?"
msgstr "この添付ファイルを削除してよろしいですか？"

#: tcms/templates/base.html:8
msgid "day"
msgstr "日"

#: tcms/templates/base.html:8
msgid "days"
msgstr "日間"

#: tcms/templates/base.html:9
msgid "hour"
msgstr " 時間"

#: tcms/templates/base.html:9
msgid "hours"
msgstr " 時間"

#: tcms/templates/base.html:10
msgid "minute"
msgstr "分"

#: tcms/templates/base.html:10
msgid "minutes"
msgstr "分間"

#: tcms/templates/base.html:11
msgid "second"
msgstr "秒"

#: tcms/templates/base.html:11
msgid "seconds"
msgstr "秒間"

#: tcms/templates/base.html:16 tcms/templates/registration/login.html:46
msgid "the leading open source test case management system"
msgstr "主要なオープンソーステストケース管理システム"

#: tcms/templates/email/confirm_registration.txt:1
#, python-format
msgid "Welcome to Kiwi TCMS!\n\n"
"To confirm email address for username `%(user)s` and activate your account\n"
"please follow this URL:\n"
"%(confirm_url)s\n\n"
"Regards,\n"
"Kiwi TCMS"
msgstr ""

#: tcms/templates/email/post_bug_save/email.txt:2
#, python-format
msgid "Bug %(pk)s has been updated.\n\n"
"Link: %(bug_url)s\n\n"
"Summary: %(summary)s\n"
"Created at: %(creation_date)s\n"
"Reporter: %(reporter)s\n"
"Assignee: %(assignee)s\n"
"Product: %(product)s\n"
"Version: %(version)s\n"
"Build: %(build)s\n"
"Last comment:\n"
"%(last_comment)s"
msgstr "バグ %(pk)s が更新されました。\n\n"
"リンク：%(bug_url)s\n\n"
"要約：%(summary)s\n"
"作成日：%(creation_date)s\n"
"レポーター：%(reporter)s\n"
"担当者：%(assignee)s\n"
"製品：%(product)s\n"
"バージョン：%(version)s\n"
"ビルド：%(build)s\n"
"最後のコメント：\n"
"%(last_comment)s"

#: tcms/templates/email/post_case_delete/email.txt:2
#, python-format
msgid "TestCase has been deleted by %(username)s!"
msgstr "テストケースが %(username)s によって削除されました！"

#: tcms/templates/email/post_run_save/email.txt:2
#, python-format
msgid "Test run %(pk)s has been created or updated for you.\n\n"
"### Links ###\n"
"Test run: %(run_url)s\n"
"Test plan: %(plan_url)s\n\n"
"### Basic run information ###\n"
"Summary: %(summary)s\n\n"
"Managed: %(manager)s.\n"
"Default tester: %(default_tester)s.\n\n"
"Product: %(product)s\n"
"Product version: %(version)s\n"
"Build: %(build)s\n\n"
"Notes:\n"
"%(notes)s"
msgstr "テスト実行%(pk)sが作成または更新されました。\n\n"
"###リンク###\n"
"テスト実行：%(run_url)s\n"
"テスト計画：%(plan_url)s\n\n"
"###基本的な実行情報###\n"
"要約：%(summary)s\n\n"
"管理：%(manager)s。\n"
"デフォルトのテスター：%(default_tester)s。\n\n"
"製品：%(product)s\n"
"製品バージョン：%(version)s\n"
"ビルド：%(build)s\n\n"
"ノート：\n"
"%(notes)s"

#: tcms/templates/email/user_registered/notify_admins.txt:2
#, python-format
msgid "Dear Administrator,\n"
"somebody just registered an account with username %(username)s at your\n"
"Kiwi TCMS instance and is awaiting your approval!\n\n"
"Go to %(user_url)s to activate the account!"
msgstr "管理者各位,\n"
"ユーザー名 %(username)s でアカウントを登録されました。\n"
"あなたのKiwi TCMSインスタンスであり、あなたの承認を待っています！\n\n"
"%(user_url)s にアクセスして、アカウントをアクティブ化してください！"

#: tcms/templates/include/attachments.html:10
#: tcms/testplans/templates/testplans/get.html:437
#: tcms/testruns/templates/testruns/get.html:487
msgid "Attachments"
msgstr "添付ファイル"

#: tcms/templates/include/attachments.html:18
msgid "File"
msgstr "ファイル"

#: tcms/templates/include/attachments.html:19
msgid "Owner"
msgstr "所有者"

#: tcms/templates/include/attachments.html:20
msgid "Date"
msgstr "日付"

#: tcms/templates/include/attachments.html:35
#: tcms/templates/include/bugs_table.html:4
#: tcms/testplans/templates/testplans/get.html:332
msgid "No records found"
msgstr "レコードはありません。"

#: tcms/templates/include/bugs_table.html:15
#: tcms/testruns/templates/testruns/get.html:573
msgid "URL"
msgstr "URL"

#: tcms/templates/include/properties_card.html:9 tcms/testruns/admin.py:117
#: tcms/testruns/templates/testruns/get.html:345
msgid "Parameters"
msgstr "パラメーター"

#: tcms/templates/include/properties_card.html:15
#: tcms/testruns/templates/testruns/mutable.html:170
msgid "This is a tech-preview feature!"
msgstr "これは技術プレビュー機能です！"

#: tcms/templates/include/properties_card.html:68
msgid "name=value"
msgstr "name=value"

#: tcms/templates/include/properties_card.html:69
#: tcms/templates/include/tags_card.html:28
#: tcms/testcases/templates/testcases/get.html:184
#: tcms/testcases/templates/testcases/get.html:229
#: tcms/testplans/templates/testplans/get.html:258
#: tcms/testruns/templates/testruns/get.html:292
#: tcms/testruns/templates/testruns/get.html:546
msgid "Add"
msgstr "追加"

#: tcms/templates/include/tags_card.html:13
#: tcms/testcases/templates/testcases/get.html:167
#: tcms/testcases/templates/testcases/get.html:215
#: tcms/testplans/templates/testplans/clone.html:14
#: tcms/testplans/templates/testplans/get.html:205
#: tcms/testplans/templates/testplans/mutable.html:24
#: tcms/testplans/templates/testplans/search.html:18
#: tcms/testruns/templates/testruns/get.html:579
msgid "Name"
msgstr "名称"

#: tcms/templates/initdb.html:5 tcms/templates/initdb.html:17
#: tcms/templates/initdb.html:29
msgid "Initialize database"
msgstr "データベースを初期化"

#: tcms/templates/initdb.html:20
msgid "Your database has not been initialized yet. Click the button below to initialize it!"
msgstr "データベースはまだ初期化されていません。下のボタンをクリックして初期化してください！"

#: tcms/templates/initdb.html:22
msgid "WARNING: this operation will take a while! This page will redirect when done."
msgstr "警告: この操作にはしばらく時間がかかります! 完了後、このページはリダイレクトされます。"

#: tcms/templates/initdb.html:27
msgid "Please wait"
msgstr "しばらくお待ちください。"

#: tcms/templates/navbar.html:9
msgid "Toggle navigation"
msgstr "ナビゲーションの切り替え"

#: tcms/templates/navbar.html:15
msgid "DASHBOARD"
msgstr "ダッシュボード"

#: tcms/templates/navbar.html:41
msgid "Language"
msgstr "言語"

#: tcms/templates/navbar.html:45
msgid "Change language"
msgstr "言語を変更"

#: tcms/templates/navbar.html:46
msgid "Supported languages"
msgstr "サポートされる言語"

#: tcms/templates/navbar.html:47
msgid "Request new language"
msgstr "新しい言語をリクエスト"

#: tcms/templates/navbar.html:53
msgid "Translation mode"
msgstr "翻訳モード"

#: tcms/templates/navbar.html:57
msgid "Translation guide"
msgstr "翻訳者ガイド"

#: tcms/templates/navbar.html:63
msgid "Help"
msgstr "ヘルプ"

#: tcms/templates/navbar.html:78
msgid "Welcome Guest"
msgstr "ようこそ ゲストさん"

#: tcms/templates/navbar.html:84
msgid "My Test Runs"
msgstr "テスト実行"

#: tcms/templates/navbar.html:88
msgid "My Test Plans"
msgstr "テスト計画"

#: tcms/templates/navbar.html:94
msgid "My profile"
msgstr "自分のプロフィール"

#: tcms/templates/navbar.html:98
#: tcms/templates/registration/password_reset_confirm.html:29
msgid "Change password"
msgstr "パスワードの変更"

#: tcms/templates/navbar.html:108
msgid "Logout"
msgstr "ログアウト"

#: tcms/templates/navbar.html:113 tcms/templates/registration/login.html:4
msgid "Login"
msgstr "ログイン"

#: tcms/templates/navbar.html:120
#: tcms/templates/registration/registration_form.html:53
msgid "Register"
msgstr "登録"

#: tcms/templates/registration/login.html:24
#: tcms/templates/registration/password_reset_confirm.html:15
#: tcms/templates/registration/registration_form.html:23
msgid "Password"
msgstr "パスワード"

#: tcms/templates/registration/login.html:31
msgid "Forgot password"
msgstr "パスワードを忘れた場合"

#: tcms/templates/registration/login.html:34
msgid "Log in"
msgstr "ログイン"

#: tcms/templates/registration/login.html:45
msgid "Welcome to Kiwi TCMS"
msgstr "Kiwi TCMSへようこそ"

#: tcms/templates/registration/login.html:50
msgid "Please login to get started"
msgstr "開始するにはログインしてください。"

#: tcms/templates/registration/login.html:52
msgid "or"
msgstr "または"

#: tcms/templates/registration/login.html:53
msgid "register an account"
msgstr "アカウントを登録してください。"

#: tcms/templates/registration/login.html:54
msgid "if you don't have one!"
msgstr "（アカウントを持っていない場合）"

#: tcms/templates/registration/password_reset_complete.html:12
msgid "Your password has been set. You may go ahead and"
msgstr "あなたのパスワードが設定されています。先に進んで今すぐログイン"

#: tcms/templates/registration/password_reset_complete.html:13
msgid "now"
msgstr "今"

#: tcms/templates/registration/password_reset_confirm.html:43
msgid "Please enter your new password twice so we can verify you typed it in correctly"
msgstr "確認のために、新しいパスワードを2回入力してください。"

#: tcms/templates/registration/password_reset_confirm.html:46
msgid "request a new password reset"
msgstr "パスワードリセットのリクエスト"

#: tcms/templates/registration/password_reset_done.html:11
msgid "Password reset email was sent"
msgstr "パスワードリセットのメールを送信しました。"

#: tcms/templates/registration/password_reset_form.html:27
msgid "Password reset"
msgstr "パスワード再設定"

#: tcms/templates/registration/password_reset_form.html:34
msgid "Kiwi TCMS password reset"
msgstr "Kiwi TCMS パスワードのリセット"

#: tcms/templates/registration/registration_form.html:4
msgid "Register new account"
msgstr "新しいアカウントの登録"

#: tcms/testcases/admin.py:28
msgid "For more information about customizing test case statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-case-statuses\">\n"
"        the documentation</a>!"
msgstr "テストケースのステータスのカスタマイズについての詳細は、\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-case-statuses\">\n"
"        ドキュメント</a> を参照してください!"

#: tcms/testcases/admin.py:56
msgid "1 confirmed & 1 uncomfirmed status required!"
msgstr "確認済みと未確認のステータスが１つずつは必要です!"

#: tcms/testcases/admin.py:131
msgid "Bug URL"
msgstr "バグのURL"

#: tcms/testcases/admin.py:151
msgid "External Issue Tracker Integration"
msgstr "外部課題トラッキングシステムの統合"

#: tcms/testcases/admin.py:161
msgid "<h1>Warning: read the\n"
"<a href=\"http://kiwitcms.readthedocs.io/en/latest/admin.html#configure-external-bug-trackers\">\n"
"Configure external bug trackers</a> section before editting the values below!</h1>"
msgstr "<h1>警告:以下の値を編集する前に<a href=\"http://kiwitcms.readthedocs.io/en/latest/admin.html#configure-external-bug-trackers\">外部バグトラッカーを設定</a> セクションを読んでください！</h1>"

#: tcms/testcases/admin.py:168
msgid "Configuration health check"
msgstr "設定のヘルスチェック"

#: tcms/testcases/admin.py:172
msgid "Kiwi TCMS will try fetching details for the given bug URL using the integration defined above! Click the `Save and continue` button and watch out for messages at the top of the screen. <strong>WARNING:</strong> in case of failures some issue trackers will fall back to fetching details via the OpenGraph protocol. In that case the result will include field named `from_open_graph`."
msgstr "Kiwi TCMSは、上記で定義した統合して使用されるバグのURLから詳細を取得しようとします。 「保存して続ける」ボタンをクリックし、画面上部に表示されるメッセージを確認してください。 <strong>警告:</strong> 問題が発生した場合には、OpenGraphプロトコルを介して詳細を取得するためにトラッカーが転送されます。 その場合、結果には `from_open_graph` という名前のフィールドが含まれます。"

#: tcms/testcases/admin.py:192
msgid "Failed creating Issue Tracker"
msgstr "課題トラッカーの作成に失敗しました"

#: tcms/testcases/admin.py:201
msgid "Details extracted via OpenGraph. Issue Tracker may still be configured incorrectly!"
msgstr "OpenGraphで抽出された詳細。問題トラッカーが正しく設定されていない可能性があります。"

#: tcms/testcases/admin.py:210
msgid "Details extracted via API. Issue Tracker configuration looks good!"
msgstr "API 経由で抽出された詳細。問題トラッカーの設定が良いです！"

#: tcms/testcases/admin.py:223
msgid "Issue Tracker configuration check failed"
msgstr "課題トラッカーの設定チェックが完了しました"

#: tcms/testcases/helpers/email.py:22
#, python-format
msgid "DELETED: TestCase #%(pk)d - %(summary)s"
msgstr "削除: テストケース #%(pk)d - %(summary)s"

#: tcms/testcases/models.py:23
msgid "Test case status"
msgstr "テストケースのステータス"

#: tcms/testcases/models.py:24
msgid "Test case statuses"
msgstr "テストケースのステータス"

#: tcms/testcases/models.py:379
#: tcms/testcases/templates/testcases/mutable.html:107
msgid "Template"
msgstr "テンプレート"

#: tcms/testcases/models.py:380
msgid "Templates"
msgstr "テンプレート"

#: tcms/testcases/templates/testcases/clone.html:5
msgid "Clone TestCase"
msgstr "テストケースを複製"

#: tcms/testcases/templates/testcases/clone.html:15
msgid "Add new TC into TP"
msgstr "TPに新しいTCを追加"

#: tcms/testcases/templates/testcases/clone.html:30
msgid "Selected TC"
msgstr "選択されたTC"

#: tcms/testcases/templates/testcases/clone.html:45 tcms/testcases/views.py:134
#: tcms/testplans/templates/testplans/clone.html:73
#: tcms/testplans/templates/testplans/get.html:138
#: tcms/testplans/templates/testplans/get.html:358 tcms/testplans/views.py:144
#: tcms/testruns/views.py:198
msgid "Clone"
msgstr "クローン"

#: tcms/testcases/templates/testcases/get.html:28
#: tcms/testcases/templates/testcases/get.html:168
#: tcms/testcases/templates/testcases/mutable.html:184
#: tcms/testcases/templates/testcases/search.html:122
#: tcms/testcases/templates/testcases/search.html:169
#: tcms/testplans/templates/testplans/get.html:54
#: tcms/testplans/templates/testplans/get.html:412
#: tcms/testplans/templates/testplans/get.html:492
#: tcms/testplans/templates/testplans/mutable.html:121
#: tcms/testplans/templates/testplans/search.html:82
#: tcms/testplans/templates/testplans/search.html:120
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:8
#: tcms/testruns/templates/testruns/mutable.html:230
msgid "Author"
msgstr "作成者"

#: tcms/testcases/templates/testcases/get.html:49
#: tcms/testcases/templates/testcases/mutable.html:58
#: tcms/testcases/templates/testcases/search.html:100
#: tcms/testcases/templates/testcases/search.html:164
#: tcms/testplans/templates/testplans/get.html:408
#: tcms/testplans/templates/testplans/get.html:491
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:7
#: tcms/testruns/templates/testruns/get.html:266
#: tcms/testruns/templates/testruns/get.html:367
#: tcms/testruns/templates/testruns/mutable.html:233
msgid "Category"
msgstr "カテゴリー"

#: tcms/testcases/templates/testcases/get.html:64
#: tcms/testcases/templates/testcases/mutable.html:86
#: tcms/testcases/templates/testcases/search.html:82
#: tcms/testcases/templates/testcases/search.html:166
#: tcms/testplans/templates/testplans/get.html:162
#: tcms/testplans/templates/testplans/get.html:371
#: tcms/testplans/templates/testplans/get.html:404
#: tcms/testplans/templates/testplans/get.html:490
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:6
#: tcms/testruns/templates/testruns/get.html:265
#: tcms/testruns/templates/testruns/get.html:363
#: tcms/testruns/templates/testruns/mutable.html:234
msgid "Priority"
msgstr "優先度"

#: tcms/testcases/templates/testcases/get.html:73
#: tcms/testcases/templates/testcases/mutable.html:120
msgid "Setup duration"
msgstr "セットアップ時間"

#: tcms/testcases/templates/testcases/get.html:78
#: tcms/testcases/templates/testcases/mutable.html:127
msgid "Testing duration"
msgstr "テスト時間"

#: tcms/testcases/templates/testcases/get.html:83
msgid "Expected duration"
msgstr "予想時間"

#: tcms/testcases/templates/testcases/get.html:98
#: tcms/testcases/templates/testcases/mutable.html:143
msgid "Script"
msgstr "スクリプト名"

#: tcms/testcases/templates/testcases/get.html:103
#: tcms/testcases/templates/testcases/mutable.html:149
msgid "Arguments"
msgstr "アーギュメント"

#: tcms/testcases/templates/testcases/get.html:108
#: tcms/testcases/templates/testcases/mutable.html:157
msgid "Requirements"
msgstr "要件"

#: tcms/testcases/templates/testcases/get.html:113
#: tcms/testcases/templates/testcases/mutable.html:162
#: tcms/testplans/templates/testplans/get.html:80
#: tcms/testplans/templates/testplans/mutable.html:102
msgid "Reference link"
msgstr "参照"

#: tcms/testcases/templates/testcases/get.html:133
#: tcms/testcases/templates/testcases/mutable.html:170
#: tcms/testplans/templates/testplans/get.html:433
#: tcms/testplans/templates/testplans/get.html:434
#: tcms/testruns/templates/testruns/get.html:400
#: tcms/testruns/templates/testruns/mutable.html:202
msgid "Notes"
msgstr "ノート"

#: tcms/testcases/templates/testcases/get.html:152
msgid "Bugs"
msgstr "バグ"

#: tcms/testcases/templates/testcases/get.html:159
msgid "Test plans"
msgstr "テスト計画"

#: tcms/testcases/templates/testcases/mutable.html:10
msgid "Edit TestCase"
msgstr "テストケースの編集"

#: tcms/testcases/templates/testcases/mutable.html:59
msgid "add new Category"
msgstr "新しいカテゴリーを追加する。"

#: tcms/testcases/templates/testcases/mutable.html:108
msgid "add new Template"
msgstr "新しいテンプレートを追加"

#: tcms/testcases/templates/testcases/mutable.html:180
#: tcms/testruns/templates/testruns/get.html:517
msgid "Notify"
msgstr "通知する"

#: tcms/testcases/templates/testcases/mutable.html:191
msgid "Manager of runs"
msgstr "実行マネージャー"

#: tcms/testcases/templates/testcases/mutable.html:198
msgid "Asignees"
msgstr "担当者"

#: tcms/testcases/templates/testcases/mutable.html:214
msgid "Default tester of runs"
msgstr "テストランのデフォルトテスターです"

#: tcms/testcases/templates/testcases/mutable.html:223
msgid "Notify when"
msgstr "通知のタイミング"

#: tcms/testcases/templates/testcases/mutable.html:224
#: tcms/testplans/templates/testplans/mutable.html:142
msgid "applies only for changes made by somebody else"
msgstr "他の誰かが行った変更にのみ適用されます"

#: tcms/testcases/templates/testcases/mutable.html:229
msgid "TestCase is updated"
msgstr "テストケースが更新されました"

#: tcms/testcases/templates/testcases/mutable.html:236
msgid "TestCase is deleted"
msgstr "テストケースが削除されました"

#: tcms/testcases/templates/testcases/mutable.html:245
msgid "CC to"
msgstr "CC to"

#: tcms/testcases/templates/testcases/mutable.html:250
msgid "Email addresses separated by comma. A notification email will be sent to each Email address within CC list."
msgstr "カンマで区切られたメールアドレス。 通知メールは、CCリスト内の各メールアドレスに送信されます。"

#: tcms/testcases/templates/testcases/search.html:15
msgid "Test case summary"
msgstr "テストケースの概要"

#: tcms/testcases/templates/testcases/search.html:45
msgid "Both"
msgstr "両方"

#: tcms/testcases/templates/testcases/search.html:61
msgid "include in search request"
msgstr ""

#: tcms/testcases/templates/testcases/search.html:74
msgid "include child test plans"
msgstr ""

#: tcms/testcases/templates/testcases/search.html:112
#: tcms/testcases/templates/testcases/search.html:165
#: tcms/testplans/templates/testplans/get.html:216
#: tcms/testruns/templates/testruns/get.html:262
msgid "Component"
msgstr "コンポーネント"

#: tcms/testcases/templates/testcases/search.html:128
msgid "Text"
msgstr "テキスト"

#: tcms/testcases/templates/testcases/search.html:139
#: tcms/testplans/templates/testplans/search.html:97
#: tcms/testruns/templates/testruns/search.html:30
msgid "Separate multiple values with comma (,)"
msgstr "複数の値をカンマで区切ってください (,)"

#: tcms/testcases/templates/testcases/search.html:178
msgid "Select"
msgstr "選択"

#: tcms/testcases/views.py:138 tcms/testplans/views.py:146
#: tcms/testruns/templates/testruns/get.html:458 tcms/testruns/views.py:202
msgid "History"
msgstr "履歴"

#: tcms/testcases/views.py:250
msgid "TestCase cloning was successful"
msgstr "テストケースの複製に成功しました"

#: tcms/testcases/views.py:281
msgid "At least one TestCase is required"
msgstr "少なくとも 1 つのテストケースが必要です"

#: tcms/testplans/templates/testplans/clone.html:5
msgid "Clone TestPlan"
msgstr "クローンテスト計画"

#: tcms/testplans/templates/testplans/clone.html:55
msgid "Clone TCs"
msgstr "TCをクローンする"

#: tcms/testplans/templates/testplans/clone.html:59
msgid "Clone or link existing TCs into new TP"
msgstr "既存のTCを新しいTPにクローンまたはリンク"

#: tcms/testplans/templates/testplans/clone.html:63
msgid "Parent TP"
msgstr "親TP"

#: tcms/testplans/templates/testplans/clone.html:67
msgid "Set the source TP as parent of new TP"
msgstr "ソースのTPを新しいTPの親として設定します"

#: tcms/testplans/templates/testplans/get.html:25
#: tcms/testruns/templates/testruns/get.html:23
msgid "Enter username, email or user ID:"
msgstr "ユーザー名、メールアドレス、またはユーザーIDを入力してください。"

#: tcms/testplans/templates/testplans/get.html:26
#: tcms/testruns/templates/testruns/get.html:22
msgid "No rows selected! Please select at least one!"
msgstr "行が選択されていません！少なくとも 1 行は選択してください！"

#: tcms/testplans/templates/testplans/get.html:27
#: tcms/testruns/templates/testruns/get.html:24
msgid "Are you sure?"
msgstr "よろしいですか?"

#: tcms/testplans/templates/testplans/get.html:28
msgid "Cannot create TestRun with unconfirmed test cases"
msgstr "未確認のテストケースではテスト実行を作成できません"

#: tcms/testplans/templates/testplans/get.html:29
msgid "Error adding test cases"
msgstr "テストケースの追加エラー"

#: tcms/testplans/templates/testplans/get.html:43
msgid "Show more"
msgstr "すべて表示"

#: tcms/testplans/templates/testplans/get.html:75
msgid "Plan Type"
msgstr "計画の種類"

#: tcms/testplans/templates/testplans/get.html:100
msgid "Test cases"
msgstr "テストケース"

#: tcms/testplans/templates/testplans/get.html:182
#: tcms/testplans/templates/testplans/get.html:421
#: tcms/testplans/templates/testplans/get.html:494
#: tcms/testplans/templates/testplans/toolbar_dropdown.html:10
msgid "Reviewer"
msgstr "レビューア"

#: tcms/testplans/templates/testplans/get.html:231
#: tcms/testplans/templates/testplans/get.html:234
msgid "Sort key"
msgstr "並べ替えのキー"

#: tcms/testplans/templates/testplans/get.html:244
msgid "Re-order cases"
msgstr "順序を変える"

#: tcms/testplans/templates/testplans/get.html:254
#: tcms/testruns/templates/testruns/get.html:288
msgid "Search and add test cases"
msgstr "テストケースの検索と追加"

#: tcms/testplans/templates/testplans/get.html:263
#: tcms/testruns/templates/testruns/get.html:298
msgid "Advanced search"
msgstr "高度な検索"

#: tcms/testplans/templates/testplans/get.html:286
msgid "Active test runs"
msgstr "アクティブなテスト実行"

#: tcms/testplans/templates/testplans/get.html:308
msgid "More"
msgstr "さらに表示"

#: tcms/testplans/templates/testplans/get.html:310
msgid "Inactive"
msgstr "非アクティブ"

#: tcms/testplans/templates/testplans/get.html:444
#: tcms/testruns/templates/testruns/get.html:494
msgid "No attachments"
msgstr "添付ファイルなし"

#: tcms/testplans/templates/testplans/get.html:469
#: tcms/testruns/templates/testruns/get.html:483
msgid "Comments"
msgstr "コメント"

#: tcms/testplans/templates/testplans/mutable.html:10
msgid "Edit TestPlan"
msgstr "テスト計画を編集"

#: tcms/testplans/templates/testplans/mutable.html:12
msgid "Create new TestPlan"
msgstr "新しいテストプランを作成"

#: tcms/testplans/templates/testplans/mutable.html:82
msgid "Parent ID"
msgstr "親ID"

#: tcms/testplans/templates/testplans/mutable.html:100
msgid "Enter to assign; Backspace + Enter to clear"
msgstr ""

#: tcms/testplans/templates/testplans/mutable.html:110
msgid "Test plan document:"
msgstr "テスト計画"

#: tcms/testplans/templates/testplans/mutable.html:119
msgid "Notify:"
msgstr "通知:"

#: tcms/testplans/templates/testplans/mutable.html:127
msgid "TestCase author"
msgstr "テストケースの作成者"

#: tcms/testplans/templates/testplans/mutable.html:141
msgid "Notify when:"
msgstr "通知のタイミング:"

#: tcms/testplans/templates/testplans/mutable.html:145
msgid "TestPlan is updated"
msgstr "テスト計画が更新された時"

#: tcms/testplans/templates/testplans/mutable.html:151
msgid "Test cases are updated"
msgstr "テストケースが更新された時"

#: tcms/testplans/templates/testplans/mutable.html:165
#: tcms/testplans/templates/testplans/search.html:41
msgid "Active"
msgstr "アクティブ"

#: tcms/testplans/templates/testplans/search.html:12
msgid "Some child test plans do not match search criteria"
msgstr ""

#: tcms/testplans/templates/testplans/search.html:20
msgid "Test plan name"
msgstr "テスト計画名"

#: tcms/testruns/admin.py:32
msgid "Permission denied: TestRun does not belong to you"
msgstr "アクセスが拒否されました: テストランはあなたに属していません"

#: tcms/testruns/admin.py:39
msgid "For more information about customizing test execution statuses see\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-execution-statuses\">\n"
"        the documentation</a>!"
msgstr "テスト実行ステータスのカスタマイズの詳細については、\n"
"        <a href=\"https://kiwitcms.readthedocs.io/en/latest/admin.html#test-execution-statuses\">\n"
"        ドキュメント</a> を参照してください。"

#: tcms/testruns/admin.py:95
msgid "1 negative, 1 neutral & 1 positive status required!"
msgstr "1つのネガティブ、1つの ニュートラル 、1つのポジティブなステータスが必要です!"

#: tcms/testruns/admin.py:106
msgid "Edit parameters"
msgstr "パラメータの編集"

#: tcms/testruns/forms.py:39
msgid "Full"
msgstr "フル"

#: tcms/testruns/forms.py:40
msgid "Pairwise"
msgstr "対比評価"

#: tcms/testruns/models.py:230
msgid "Test execution statuses"
msgstr "テスト実行の状態"

#: tcms/testruns/templates/testruns/get.html:25
msgid "Unconfirmed test cases were not added"
msgstr "未確認のテストケースは追加されませんでした"

#: tcms/testruns/templates/testruns/get.html:26
msgid "Type 0 or 1"
msgstr "0または1を入力"

#: tcms/testruns/templates/testruns/get.html:27
msgid "Comment"
msgstr "コメント"

#: tcms/testruns/templates/testruns/get.html:60
#: tcms/testruns/templates/testruns/mutable.html:29
#: tcms/testruns/templates/testruns/search.html:67
#: tcms/testruns/templates/testruns/search.html:188
msgid "Manager"
msgstr "マネージャー"

#: tcms/testruns/templates/testruns/get.html:73
#: tcms/testruns/templates/testruns/mutable.html:110
#: tcms/testruns/templates/testruns/search.html:127
msgid "Planned start"
msgstr "計画開始日"

#: tcms/testruns/templates/testruns/get.html:84
msgid "Start"
msgstr "開始"

#: tcms/testruns/templates/testruns/get.html:91
#: tcms/testruns/templates/testruns/mutable.html:123
#: tcms/testruns/templates/testruns/search.html:147
msgid "Planned stop"
msgstr "計画終了日"

#: tcms/testruns/templates/testruns/get.html:102
msgid "Stop"
msgstr "終了"

#: tcms/testruns/templates/testruns/get.html:139
#: tcms/testruns/templates/testruns/mutable.html:160
msgid "Environment"
msgstr "環境設定"

#: tcms/testruns/templates/testruns/get.html:182
msgid "Update text version"
msgstr "最新バージョンに更新"

#: tcms/testruns/templates/testruns/get.html:217
msgid "Add comment"
msgstr "コメントの追加"

#: tcms/testruns/templates/testruns/get.html:228
#: tcms/testruns/templates/testruns/get.html:331
#: tcms/testruns/templates/testruns/get.html:568
msgid "Add hyperlink"
msgstr "ハイパーリンクを追加"

#: tcms/testruns/templates/testruns/get.html:279
msgid "Mine"
msgstr "Mine"

#: tcms/testruns/templates/testruns/get.html:280
msgid "All"
msgstr "すべて"

#: tcms/testruns/templates/testruns/get.html:306
msgid "records"
msgstr "レコード"

#: tcms/testruns/templates/testruns/get.html:332
#: tcms/testruns/templates/testruns/get.html:607
#: tcms/testruns/templates/testruns/get.html:624
msgid "Report bug"
msgstr "バグをレポート"

#: tcms/testruns/templates/testruns/get.html:339
msgid "Test case is not part of parent test plan"
msgstr "テストケースは親テスト計画の一部ではありません"

#: tcms/testruns/templates/testruns/get.html:371
msgid "Assigned to"
msgstr "担当"

#: tcms/testruns/templates/testruns/get.html:379
#: tcms/testruns/templates/testruns/get.html:381
msgid "Last bug"
msgstr ""

#: tcms/testruns/templates/testruns/get.html:423
msgid "Text version"
msgstr "最新バージョン"

#: tcms/testruns/templates/testruns/get.html:431
msgid "Bugs and hyperlinks"
msgstr "バグとハイパーリンク"

#: tcms/testruns/templates/testruns/get.html:585
msgid "Is a defect"
msgstr "不具合"

#: tcms/testruns/templates/testruns/get.html:592
#: tcms/testruns/templates/testruns/get.html:623
msgid "Cancel"
msgstr "キャンセル"

#: tcms/testruns/templates/testruns/get.html:612
msgid "Issue Tracker"
msgstr "課題管理"

#: tcms/testruns/templates/testruns/mutable.html:7
msgid "Edit TestRun"
msgstr "テスト実行を編集"

#: tcms/testruns/templates/testruns/mutable.html:9
msgid "Clone TestRun"
msgstr "テストランを複製"

#: tcms/testruns/templates/testruns/mutable.html:180
msgid "Affects only test cases with parameters"
msgstr "パラメータを持つテストケースのみに影響します"

#: tcms/testruns/templates/testruns/mutable.html:194
msgid "more information"
msgstr "詳細情報"

#: tcms/testruns/templates/testruns/mutable.html:218
msgid "Selected TestCase(s):"
msgstr "選択したテストケース:"

#: tcms/testruns/templates/testruns/mutable.html:221
#, python-format
msgid "%(count)s of the pre-selected test cases is not CONFIRMED and will not be cloned!\n"
"See test plan for more details!"
msgstr "事前に選択されたテストケースの %(count)s は確認されておらず、クローンされません！\n"
"詳細については、テストプランを参照してください！"

#: tcms/testruns/templates/testruns/search.html:21
msgid "Plan ID"
msgstr "計画ID"

#: tcms/testruns/templates/testruns/search.html:23
msgid "TestPlan ID"
msgstr "テスト計画ID"

#: tcms/testruns/templates/testruns/search.html:79
msgid "Running"
msgstr "実行中"

#: tcms/testruns/templates/testruns/search.html:86
#: tcms/testruns/templates/testruns/search.html:186
msgid "Start date"
msgstr "開始日"

#: tcms/testruns/templates/testruns/search.html:106
#: tcms/testruns/templates/testruns/search.html:187
msgid "Stop date"
msgstr "終了日"

#: tcms/testruns/views.py:273
msgid "Clone of "
msgstr "クローン - "

#: testcases.TestCaseStatus/name:1
msgid "PROPOSED"
msgstr "プロポーズ済み"

#: testcases.TestCaseStatus/name:3
msgid "DISABLED"
msgstr "無効"

#: testcases.TestCaseStatus/name:4
msgid "NEED_UPDATE"
msgstr "更新が必要"

#: testruns.TestExecutionStatus/name:1
msgid "IDLE"
msgstr "待機中"

#: testruns.TestExecutionStatus/name:2
msgid "RUNNING"
msgstr "実行中"

#: testruns.TestExecutionStatus/name:3
msgid "PAUSED"
msgstr "一時停止"

#: testruns.TestExecutionStatus/name:4
msgid "PASSED"
msgstr "成功"

#: testruns.TestExecutionStatus/name:5
msgid "FAILED"
msgstr "失敗"

#: testruns.TestExecutionStatus/name:6
msgid "BLOCKED"
msgstr "ブロック"

#: testruns.TestExecutionStatus/name:7
msgid "ERROR"
msgstr "エラー"

#: testruns.TestExecutionStatus/name:8
msgid "WAIVED"
msgstr "却下"

#: tcms_github_app/admin.py:122
#, python-format
msgid "For additional configuration see\n"
"<a href=\"%s\">GitHub</a>"
msgstr "追加の設定については、\n"
"<a href=\"%s\">GitHub</a> を参照してください。"

#: tcms_github_app/menu.py:11
msgid "GitHub integration"
msgstr "GitHub連携"

#: tcms_github_app/menu.py:12
msgid "Resync"
msgstr "再同期"

#: tcms_github_app/menu.py:13
msgid "Settings"
msgstr "設定"

#: tcms_github_app/middleware.py:41
#, python-format
msgid "Unconfigured GitHub App %d"
msgstr "設定されていないGitHubアプリ %d"

#: tcms_github_app/utils.py:274
#, python-format
msgid "%s was imported from GitHub"
msgstr "%s は GitHub からインポートされました"

#: tcms_github_app/utils.py:278
#, python-format
msgid "%s already exists"
msgstr "%s は既に存在します"

#: tcms_github_app/views.py:48
#, python-format
msgid "You have not logged-in via GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "GitHubアカウントでログインしていません！ <a href=\"%s\">ここをクリックしてください</a>!"

#: tcms_github_app/views.py:62
#, python-format
msgid "You have not installed Kiwi TCMS into your GitHub account! <a href=\"%s\">Click here</a>!"
msgstr "GitHubアカウントにKiwi TCMSがインストールされていません！ <a href=\"%s\">ここをクリック</a>!"

#: tcms_github_app/views.py:76
msgid "Multiple GitHub App installations detected! See below:"
msgstr "複数の GitHub アプリのインストールが検出されました！以下を参照してください:"

#: tcms_github_app/views.py:85
#, python-format
msgid "Edit GitHub App <a href=\"%s\">%s</a>"
msgstr "GitHubアプリを編集 <a href=\"%s\">%s</a>"

#: tcms_github_app/views.py:102
#, python-format
msgid "Cannot find GitHub App installation for tenant \"%s\""
msgstr "テナント \"%s\" の GitHub アプリのインストールが見つかりません"

#: tcms_github_app/views.py:111
msgid "Multiple GitHub App installations detected!"
msgstr "複数の GitHub アプリのインストールが検出されました！"

#: tcms_github_marketplace/menu.py:11
msgid "Subscriptions"
msgstr "サブスクリプション"

#: tcms_github_marketplace/templates/tcms_github_marketplace/email/exit_poll.txt:1
msgid "Thank you for using Kiwi TCMS via a paid subscription.\n"
"We're sorry to see you go but we'd like to know why so we can improve in the future!\n\n"
"You can share your feedback at https://forms.gle/RJhFengbjN9C6wtUA\n\n"
"Thank you!"
msgstr ""

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:6
msgid "Tenant subscriptions"
msgstr "テナントサブスクリプション"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:15
msgid "You can access the following tenants"
msgstr "以下のテナントにアクセスできます"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:20
msgid "Tenant"
msgstr "テナント"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:31
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:90
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:30
msgid "Organization"
msgstr "組織"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:44
msgid "Docker credentials"
msgstr "Docker 資格情報"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:61
msgid "Private containers instructions"
msgstr "プライベートコンテナの説明"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:75
msgid "You own the following tenants"
msgstr "以下のテナントを所有しています"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:97
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:98
msgid "Price"
msgstr "価格"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:102
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:103
msgid "Subscription type"
msgstr "購読の種類"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:107
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:108
#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:8
msgid "Paid until"
msgstr "支払完了まで"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:114
msgid "Cancel subscription"
msgstr "定期購入を解約"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:127
msgid "You don't own any tenants"
msgstr "テナントを所有していません"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:131
msgid "Subscribe via FastSpring"
msgstr "FastSpring 経由で購読"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:147
msgid "Transaction history"
msgstr "トランザクション履歴"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:169
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:170
msgid "Sender"
msgstr "送信元"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:174
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:175
msgid "Vendor"
msgstr "ベンダー"

#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:179
#: tcms_github_marketplace/templates/tcms_github_marketplace/subscription.html:180
msgid "Received on"
msgstr "受信"

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:4
msgid "Extra emails"
msgstr ""

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:14
msgid "Kiwi TCMS will try to match recurring billing events against tenant.owner.email + tenant.extra_emails"
msgstr ""

#: tcms_github_marketplace/templates/tcms_tenants/include/tenant_extra_emails.html:17
msgid "Separate by comma (,), semi-colon (;) or white space ( )"
msgstr ""

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:44
msgid "Private Tenant Warning"
msgstr "プライベートテナント警告"

#: tcms_github_marketplace/templates/tcms_tenants/override_new.html:49
msgid "You are about to create a Private Tenant for Kiwi TCMS.\n"
"It will take a few minutes until your DB schema is ready!\n"
"After clicking the 'Save' button <strong>do not</strong> close or refresh this page!<br>\n"
"You will be redirected to your new tenant when the creation process is complete!\n"
"If you see a 500 Internal Server Error page please contact\n"
"<a href=\"mailto:<EMAIL>\"><EMAIL></a> immediately!"
msgstr "Kiwi TCMSのプライベートテナントを作成しようとしています。\n"
"DBスキーマの準備ができるまで数分かかります！\n"
"[保存]ボタンをクリックした後、このページを閉じたり更新したり<strong>しないでください</strong>。<br>\n"
"作成プロセスが完了すると、新しいテナントにリダイレクトされます。\n"
"500内部サーバーエラーページが表示された場合は、お問い合わせください\n"
"<a href=\"mailto:<EMAIL>\"> <EMAIL> </a>すぐに！"

#: tcms_github_marketplace/utils.py:95
msgid "Kiwi TCMS Subscription Exit Poll"
msgstr ""

#: tcms_github_marketplace/views.py:567
msgid "Kiwi TCMS subscription notification"
msgstr ""

#: tcms_github_marketplace/views.py:749
msgid "mo"
msgstr "月"

#: tcms_github_marketplace/views.py:752
msgid "yr"
msgstr "年"

#: tcms_enterprise/pipeline.py:17
msgid "Email address is required"
msgstr "メールアドレスは必須項目です"

#: tcms_enterprise/templates/registration/custom_login.html:10
msgid "or Continue With"
msgstr "または以下でログイン"

#: tcms_settings_dir/enterprise.py:19
msgid "Legal information"
msgstr "法的情報"

#: tcms_tenants/admin.py:55 tcms_tenants/admin.py:62
#: tcms_tenants/middleware.py:35
msgid "Unauthorized"
msgstr "未認証"

#: tcms_tenants/admin.py:86
msgid "Existing username, email or user ID"
msgstr "既存のユーザー名、メールアドレス、またはユーザーID"

#: tcms_tenants/admin.py:159
msgid "Full name"
msgstr "姓名"

#: tcms_tenants/forms.py:30
msgid "Invalid string"
msgstr "無効な文字列"

#: tcms_tenants/menu.py:15
msgid "Create"
msgstr "作成する"

#: tcms_tenants/menu.py:20
#: tcms_tenants/templates/tcms_tenants/invite_users.html:17
msgid "Invite users"
msgstr "ユーザーを招待"

#: tcms_tenants/menu.py:21
msgid "Authorized users"
msgstr "承認されたユーザー"

#: tcms_tenants/middleware.py:59
msgid "Unpaid"
msgstr "未払い"

#: tcms_tenants/middleware.py:70
msgid "Tenant expires soon"
msgstr ""

#: tcms_tenants/templates/tcms_tenants/email/invite_user.txt:1
#, python-format
msgid "Dear tester,\n"
"%(invited_by)s has invited you to join their Kiwi TCMS tenant at\n"
"%(tenant_url)s\n\n"
"In case you have never logged in before an account was created for you\n"
"automatically. You can login with a social account which has the same email\n"
"address or go to %(password_reset_url)s to reset your password.\n"
"The password reset email message also contains your username!"
msgstr ""

#: tcms_tenants/templates/tcms_tenants/email/new.txt:1
#, python-format
msgid "Your Kiwi TCMS tenant was created at:\n"
"%(tenant_url)s\n\n"
"If you have troubles please contact support!"
msgstr "Kiwi TCMS テナントが作成されました:\n"
"%(tenant_url)s\n\n"
"問題が発生した場合は、サポートにお問い合わせください!"

#: tcms_tenants/templates/tcms_tenants/invite_users.html:28
msgid "Email"
msgstr "メールアドレス"

#: tcms_tenants/templates/tcms_tenants/new.html:18
msgid "New tenant"
msgstr "新規テナント名"

#: tcms_tenants/templates/tcms_tenants/new.html:35
msgid "Company, team or project name"
msgstr "会社、チーム、またはプロジェクト名"

#: tcms_tenants/templates/tcms_tenants/new.html:43
msgid "Schema"
msgstr "スキーマ"

#: tcms_tenants/templates/tcms_tenants/new.html:56
msgid "Validation pattern"
msgstr "検証パターン"

#: tcms_tenants/templates/tcms_tenants/new.html:61
msgid "Publicly readable"
msgstr "公開"

#: tcms_tenants/templates/tcms_tenants/new.html:80
msgid "Tenant logo"
msgstr "テナントロゴ"

#: tcms_tenants/utils.py:66
msgid "Schema name already in use"
msgstr "スキーマ名はすでに使用されています"

#: tcms_tenants/utils.py:170
msgid "New Kiwi TCMS tenant created"
msgstr "新しい Kiwi TCMS テナントが作成されました"

#: tcms_tenants/utils.py:230
#, python-brace-format
msgid "User {user.username} added to tenant group {group.name}"
msgstr "ユーザー {user.username} がテナントグループ {group.name} に追加されました"

#: tcms_tenants/utils.py:262
msgid "Invitation to join Kiwi TCMS"
msgstr "Kiwi TCMS への招待"

#: tcms_tenants/views.py:84
msgid "Only super-user and tenant owner are allowed to edit tenant properties"
msgstr "スーパーユーザーとテナントの所有者のみがテナントのプロパティを編集することができます"

#: tcms_tenants/views.py:102
msgid "Edit tenant"
msgstr "テナントを編集"

#: tcms_tenants/views.py:153
msgid "Only users who are authorized for this tenant can invite others"
msgstr "このテナントの許可を得たユーザーのみが他のユーザーを招待できます"

#: tenant_groups/admin.py:30
msgid "users"
msgstr "ユーザー"

#: tenant_groups/models.py:34
msgid "name"
msgstr "名前"

#: tenant_groups/models.py:37
msgid "permissions"
msgstr "権限"

#: tenant_groups/models.py:47
msgid "group"
msgstr "グループ"

#: tenant_groups/models.py:48
msgid "groups"
msgstr "グループ"

#: trackers_integration/menu.py:4
msgid "Personal API tokens"
msgstr "個人用APIトークン"
