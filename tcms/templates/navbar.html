{% load i18n %}
{% load static %}
{% load tz %}

    <!-- PatternFly Horizontal Nav -->
    <nav class="navbar navbar-default navbar-pf" role="navigation" id="navbar" data-defaultpagesize="{{ SETTINGS.DEFAULT_PAGE_SIZE }}">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                <span class="sr-only">{% trans "Toggle navigation" %}</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="{% url 'core-views-index' %}" target="_parent">
                <img src="{% static 'images/kiwi_h20.png' %}" alt="{% trans 'DASHBOARD' %}" title="{% trans 'DASHBOARD' %}"/>
            </a>

            {% block customized_logo %}
                {{ CUSTOMIZED_LOGO_CONTENTS }}
            {% endblock %}
        </div>

        <div class="collapse navbar-collapse">
            <ul class="nav navbar-nav navbar-utility">
                {% include "include/menu_items.html" with menu_items=SETTINGS.MENU_ITEMS %}
                {% if OBJECT_MENU_ITEMS %}
                    {% include "include/menu_items.html" with menu_items=OBJECT_MENU_ITEMS %}
                {% endif %}

                <li>
                    <a>
                        {% get_current_timezone as TIME_ZONE %}
                        <span id="clock" class="fa fa-clock-o" data-time-zone="{{ TIME_ZONE }}" title="{{ SERVER_TIME }} {{ TIME_ZONE }}">
                            {{ SERVER_TIME|date:"G:i" }} {{ TIME_ZONE }}
                        </span>
                    </a>
                </li>

                <li class="dropdown">
                    <a class="nav-item-iconic" id="horizontalDropdownMenuLang" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                        <span title="{% trans 'Language' %}" class="fa fa-globe"></span>
                        <span class="caret"></span>
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="horizontalDropdownMenuLang">
                        <li><a target="_parent" href="https://kiwitcms.readthedocs.io/en/latest/guide/language.html">{% trans "Change language" %}</a></li>
                        <li><a target="_parent" href="https://crowdin.com/project/kiwitcms">{% trans "Supported languages" %}</a></li>
                        <li><a target="_parent" href="https://github.com/kiwitcms/Kiwi/issues/new?title=Request+new+language:+...&body=Please+enable+...+language+in+Crowdin">{% trans "Request new language" %}</a></li>

                        <li class="divider"></li>
                        {% get_current_language as LANGUAGE_CODE %}
                        <li>
                            <a target="_parent" href="{% url 'translation-mode' %}?next={{ request.path }}{% if LANGUAGE_CODE != 'eo' %}&language=eo{% endif %}">
                                {% trans "Translation mode" %}
                                <span class="fa {% if LANGUAGE_CODE != 'eo' %}fa-toggle-on{% else %}fa-toggle-off{% endif %}"></span>
                            </a>
                        </li>
                        <li><a target="_parent" href="https://kiwitcms.readthedocs.io/en/latest/contribution.html#translation">{% trans "Translation guide" %}</a></li>
                    </ul>
                </li>

                <li class="dropdown">
                    <a class="nav-item-iconic" id="horizontalDropdownMenuHelp" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                        <span title="{% trans 'Help' %}" class="fa pficon-help"></span>
                        <span class="caret"></span>
                    </a>
                    <ul class="dropdown-menu" aria-labelledby="horizontalDropdownMenuHelp">
                        {% for link in SETTINGS.HELP_MENU_ITEMS %}
                        <li><a href="{{ link.0 }}" target="_parent">{{ link.1 }}</a></li>
                        {% endfor %}
                        <li class="divider"></li>
                        <li><a target="_parent" href="https://github.com/kiwitcms/Kiwi/blob/master/CHANGELOG.rst#change-log">{% trans 'Version' %} {{ SETTINGS.KIWI_VERSION }}</a></li>
                    </ul>
                </li>

                <li class="dropdown">
                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                        <span class="pficon pficon-user"></span>
                        {% if user.is_authenticated %}{{ user }}{% else %}{% trans "Welcome Guest" %}{% endif %}
                        <b class="caret"></b>
                    </a>
                    <ul class="dropdown-menu">
                    {% if user.is_authenticated %}
                        <li>
                            <a href="{% url "testruns-search" %}?default_tester={{ user.username }}" target="_parent">{% trans "My Test Runs" %}</a>
                        </li>

                        <li>
                            <a href="{% url "plans-search" %}?author={{ user.username }}" target="_parent">{% trans "My Test Plans" %}</a>
                        </li>

                        <li class="divider"></li>

                        <li>
                            <a href="{% url "tcms-profile" user.pk %}" target="_parent">{% trans "My profile" %}</a>
                        </li>

                        <li>
                            <a href="{% url "admin:password_change" %}" target="_parent">{% trans "Change password" %}</a>
                        </li>

                        <li>
                            <a href="{% url "reset-user-email" user.pk %}">{% trans "Reset email address" %}</a>
                        </li>

                        <li class="divider"></li>

                        <li>
                            <a      href="{% url "tcms-logout" %}" id="logout_link">{% trans "Logout" %}</a>
                            <form action="{% url "tcms-logout" %}" id="logout_form" method="post">{% csrf_token %}</form>
                        </li>
                    {% else %}
                        <li>
                            <a href="{% url "tcms-login" %}" target="_parent">{% trans "Login" %}</a>
                        </li>

                        {% if SETTINGS.REGISTRATION_ENABLED %}
                        <li class="divider"></li>

                        <li>
                            <a href="{% url "tcms-register" %}" target="_parent">{% trans "Register" %}</a>
                        </li>
                        {% endif %}
                    {% endif %}
                    </ul>
                </li>
            </ul>
        </div>
    </nav>
