# Generated by Django 2.1.5 on 2019-02-11 13:59

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("testcases", "0007_convert_is_automated_to_boolean"),
    ]

    operations = [
        migrations.AlterField(
            model_name="testcaseemailsettings",
            name="auto_to_case_author",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name="testcaseemailsettings",
            name="auto_to_case_run_assignee",
            field=models.BooleanField(default=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="testcaseemailsettings",
            name="auto_to_case_tester",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name="testcaseemailsettings",
            name="auto_to_run_manager",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name="testcaseemailsettings",
            name="auto_to_run_tester",
            field=models.<PERSON><PERSON>an<PERSON>ield(default=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="testcaseemailsettings",
            name="notify_on_case_delete",
            field=models.<PERSON>oleanField(default=True),
        ),
        migrations.AlterField(
            model_name="testcaseemailsettings",
            name="notify_on_case_update",
            field=models.BooleanField(default=True),
        ),
    ]
