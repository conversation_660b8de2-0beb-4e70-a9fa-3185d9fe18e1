name: 'integration / bugtracker'

on:
  push:
    branches: master
  pull_request:

permissions: read-all

jobs:
  integration_test:
    name: ${{ matrix.tracker }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: [3.11]
        tracker: [azureboards, bitbucket, bugzilla, github, gitlab_ce, gitlab_ee, gitlab_com, jira, kiwitcms, redmine]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Dockerize
      run: |
        docker compose -f tests/${{ matrix.tracker}}/docker-compose.yml build
        docker compose -f tests/${{ matrix.tracker}}/docker-compose.yml up -d
        sleep 10

        IP_ADDR=`docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' bugtracker_kiwitcms_org`
        sudo sh -c "echo '$IP_ADDR    bugtracker.kiwitcms.org' >> /etc/hosts"

    # install all dev dependencies here b/c these steps are now part of a
    # job matrix which unconditionally initializes the bugtracker DB and
    # runs the tests. Some trackers may end up with empty initialization!
    - name: Install Python dependencies
      run: |
        pip install -U pip
        pip install -r requirements/devel.txt

    - name: Initialize bugs DB
      run: |
        ./tests/${{ matrix.tracker }}/initialize-data

    - name: Execute tests
      run: |
        export GH_BUGTRACKER_INTEGRATION_TEST_API_TOKEN="${{ secrets.GH_BUGTRACKER_INTEGRATION_TEST_API_TOKEN }}"
        export GITLAB_INTEGRATION_API_TOKEN="${{ secrets.GITLAB_INTEGRATION_API_TOKEN }}"
        export JIRA_BUGTRACKER_INTEGRATION_API_USERNAME="${{ secrets.JIRA_BUGTRACKER_INTEGRATION_API_USERNAME }}"
        export JIRA_BUGTRACKER_INTEGRATION_API_TOKEN="${{ secrets.JIRA_BUGTRACKER_INTEGRATION_API_TOKEN }}"
        export BITBUCKET_INTEGRATION_API_USERNAME="${{ secrets.BITBUCKET_INTEGRATION_API_USERNAME }}"
        export BITBUCKET_INTEGRATION_API_PASSWORD="${{ secrets.BITBUCKET_INTEGRATION_API_PASSWORD }}"
        export AZURE_BOARDS_INTEGRATION_API_TOKEN="${{ secrets.AZURE_BOARDS_INTEGRATION_API_TOKEN }}"
        export LANG=en-us
        export TEST_BUGTRACKER_INTEGRATION=1
        coverage run --source='.' ./manage.py test -v2 --noinput --settings=tcms.settings.test tcms.issuetracker.tests.test_${{ matrix.tracker }}

    - name: Send coverage to codecov.io
      if: env.CODECOV_TOKEN
      uses: codecov/codecov-action@v5
      env:
        CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
      with:
        fail_ci_if_error: false
        verbose: true
