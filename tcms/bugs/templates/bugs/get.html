{% extends "base.html" %}
{% load i18n %}
{% load static %}
{% load comments %}
{% load extra_filters %}

{% block title %}BUG-{{ object.pk }}: {{ object.summary }}{% endblock %}
{% block page_id %}page-bugs-get{% endblock %}
{% block body_class %}cards-pf{% endblock %}

{% block head %}
    <meta property="og:title" content="{{ object.summary }}">
    {% get_comment_list for object as comments %}
    <meta property="og:description" content="{{ comments.0.comment|truncatechars:200 }}">
    {{ comment_form.media }}
{% endblock %}

{% block contents %}
<div class="container-cards-pf">
    <!-- Important:  if you need to nest additional .row within a .row.row-cards-pf, do *not* use .row-cards-pf on the nested .row  -->
    <h1 class="col-md-12 kiwi-margin-top-0">
        {% if bug.severity %}
            <span
                class="{{ bug.severity.icon }}"
                title="{{ bug.severity.name }}"
                style="color: {{ bug.severity.color }}"
            >
            </span>
        {% endif %}

        <span id="object_pk"
            data-pk="{{ object.pk }}"
            data-perm-remove-tag="{{ perms.bugs.delete_bugtag }}"
        >BUG-{{ object.pk }}: {{ object.summary }}</span>

        {% if object.status %}
            <span class="fa fa-envelope-open-o" title="{% trans 'Open' %}"></span>
        {% else %}
            <span class="fa fa-times" title="{% trans 'Closed' %}"></span>
        {% endif %}
    </h1>

    <div class="row row-cards-pf">
        <div class="col-xs-12 col-sm-6 col-md-3">
            <div class="card-pf card-pf-accented card-pf-aggregate-status">
                <h2 class="card-pf-title kiwi-text-align-left">
                    <span
                        class="{{ bug.severity.icon|default:'fa fa-fire-extinguisher' }}"
                        {% if bug.severity %}style="color: {{ bug.severity.color }}"{% endif %}
                    ></span>{% trans 'Severity' %}:
                    {{ bug.severity.name|default:'-' }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-calendar"></span>{{ object.created_at }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa pficon-user"></span>{% trans 'Reporter' %}:
                    <a href="{% url "tcms-profile" object.reporter.pk %}">{{ object.reporter.username }}</a>
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa pficon-user"></span>{% trans 'Assignee' %}:
                    {% if object.assignee %}
                        <a href="{% url "tcms-profile" object.assignee.pk %}">{{ object.assignee.username }}</a>
                    {% else %}
                        -
                    {% endif %}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span id="product_pk"
                        data-pk="{{ object.product.pk }}"
                        class="fa fa-shopping-cart"></span>{% trans 'Product' %}:
                    {{ object.product }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-refresh"></span>{% trans 'Version' %}:
                    {{ object.version }}
                </h2>

                <h2 class="card-pf-title kiwi-text-align-left">
                    <span class="fa fa-wrench"></span>{% trans 'Build' %}:
                    {{ object.build }}
                </h2>

                <div class="card-pf-body"></div>
            </div>
        </div>

        <div class="col-xs-12 col-sm-6 col-md-3">
            {% include 'include/tags_card.html' with add_perm=perms.bugs.add_bugtag %}
        </div>

        <div class="col-xs-12 col-sm-12 col-md-6">
            {% include 'include/attachments.html' %}
        </div>
    </div>

    <div class="row row-cards-pf">
        <div class="col-xs-12 col-sm-12 col-md-12">
            {% include 'include/tc_executions.html' %}
        </div>
    </div>

    {% get_comment_list for object as comments %}
    {% for comment in comments %}
    <div class="row row-cards-pf">
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="card-pf card-pf-accented">
                <h2 class="card-pf-title">
                    {% if comment.user %}
                        <a href="{% url "tcms-profile" comment.user.pk %}">{{ comment.user }}</a>
                    {% else %}
                        {{ comment.name }}
                    {% endif %}
                    {% trans 'commented on' %}
                    {{ comment.submit_date }}
                </h2>
                <div class="card-pf-body">
                    <div class="markdown-text">
                        {{ comment.comment|markdown2html }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}

    {% if perms.django_comments.add_comment %}
    <div class="row row-cards-pf">
        <div class="col-xs-12 col-sm-12 col-md-12">
            <div class="card-pf">
                <form class="form-horizontal" action="{% url 'bugs-comment' %}" method="post">
                    {% csrf_token %}
                    <input name="bug" type="hidden" value="{{ object.pk }}">
                    <div class="form-group">
                        <div>
                            <div>{{ comment_form.text }}</div>
                            {{ comment_form.text.errors }}
                        </div>
                    </div>

                    <div class="form-group">
                        <div>
                            {% if not object.status %}
                            <button type="submit" name="action" value="reopen" class="btn btn-warning btn-lg kiwi-float-right">{% trans "Reopen" %}</button>
                            {% else %}
                            <button type="submit" name="action" value="comment" class="btn btn-default btn-lg">{% trans "Save" %}</button>
                            <button type="submit" name="action" value="close" class="btn btn-danger btn-lg kiwi-float-right">
                                {% trans "Close" %}
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
