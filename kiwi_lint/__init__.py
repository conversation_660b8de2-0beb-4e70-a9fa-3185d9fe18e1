# Copyright (c) 2018-2020 <PERSON> <<EMAIL>>

# Licensed under the GPL 2.0: https://www.gnu.org/licenses/old-licenses/gpl-2.0.html

# NOTE: import order matches the numeric ID of the checker
from .dunder_attributes import DunderC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .list_comprehension import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .docstring import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .raw_sql import RawS<PERSON><PERSON>he<PERSON>
from .bulk_create import <PERSON>ulk<PERSON><PERSON><PERSON><PERSON><PERSON>
from .objects_update import ObjectsUpdate<PERSON>he<PERSON>
from .tags import <PERSON>s<PERSON><PERSON><PERSON>
from .empty import EmptyModule<PERSON>hecker
from .empty import ModuleInDirectoryWithoutInitChecker
from .empty import Empty<PERSON><PERSON><PERSON><PERSON><PERSON>
from .nested_definition import NestedD<PERSON>ini<PERSON><PERSON><PERSON><PERSON>
from .missing_permissions import <PERSON><PERSON>er<PERSON>s<PERSON><PERSON><PERSON>
from .missing_permissions import MissingAPIPermissions<PERSON>hecker
from .auto_field import <PERSON>Field<PERSON>hecker
from .one_to_one_field import OneToOneField<PERSON>hecker
from .views import ClassB<PERSON>d<PERSON>iew<PERSON>he<PERSON>
from .datetime import Date<PERSON><PERSON><PERSON><PERSON>
from .forms import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .db_column import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .generic_foreign_key import <PERSON>ric<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .api_distinct import APIDistinctChecker
from .similar_string import SimilarStringChecker


def register(linter):
    linter.register_checker(DunderClassAttributeChecker(linter))
    linter.register_checker(ListComprehensionChecker(linter))
    linter.register_checker(DocstringChecker(linter))
    linter.register_checker(RawSQLChecker(linter))
    linter.register_checker(BulkCreateChecker(linter))
    linter.register_checker(ObjectsUpdateChecker(linter))
    linter.register_checker(TagsChecker(linter))
    linter.register_checker(EmptyModuleChecker(linter))
    linter.register_checker(ModuleInDirectoryWithoutInitChecker(linter))
    linter.register_checker(EmptyClassChecker(linter))
    linter.register_checker(NestedDefinitionChecker(linter))
    linter.register_checker(MissingPermissionsChecker(linter))
    linter.register_checker(MissingAPIPermissionsChecker(linter))
    linter.register_checker(AutoFieldChecker(linter))
    linter.register_checker(OneToOneFieldChecker(linter))
    linter.register_checker(ClassBasedViewChecker(linter))
    linter.register_checker(DatetimeChecker(linter))
    linter.register_checker(FormFieldChecker(linter))
    linter.register_checker(ModelFormChecker(linter))
    linter.register_checker(DbColumnChecker(linter))
    linter.register_checker(GenericForeignKeyChecker(linter))
    linter.register_checker(APIDistinctChecker(linter))
    linter.register_checker(SimilarStringChecker(linter))
