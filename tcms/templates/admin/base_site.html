{% extends "admin/base.html" %}
{% load static %}
{% load i18n grp_tags %}
{% get_current_language as LANGUAGE_CODE %}

{% block title %}{{ title }} | {% get_site_title %}{% endblock %}

{% block blockbots %}
    {{ block.super }}
    {% include 'patternfly.html' %}
{% endblock %}

{% block extrastyle %}
    <link rel="stylesheet" type="text/css" href="{% static 'style/admin.css' %}" media="screen">
    <link rel="stylesheet" type="text/css" href="{% static 'style/patternfly_override.css' %}" media="all">
{% endblock %}

{% block javascripts %}
    {{ block.super }}

    <script src="{% static 'simplemde/dist/simplemde.min.js' %}"></script>
    <script src="{% static 'js/admin_override.js' %}"></script>

    {% if LANGUAGE_CODE == 'eo' %}
    <script type="text/javascript" src="{% static 'js/crowdin.js'  %}"></script>
    <script type="text/javascript" src="//cdn.crowdin.com/jipt/jipt.js"></script>
    {% endif %}
{% endblock %}

{% block navigation %}
    {% include 'navbar.html' %}
{% endblock %}
