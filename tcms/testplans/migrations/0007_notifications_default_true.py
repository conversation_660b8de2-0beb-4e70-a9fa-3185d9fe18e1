# Generated by Django 2.1.5 on 2019-02-11 13:59

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("testplans", "0006_remove_testplan_owner"),
    ]

    operations = [
        migrations.<PERSON>mo<PERSON><PERSON><PERSON>(
            model_name="testplanemailsettings",
            name="is_active",
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="testplanemailsettings",
            name="auto_to_case_default_tester",
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name="testplanemailsettings",
            name="auto_to_case_owner",
            field=models.BooleanField(default=True),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name="testplanemailsettings",
            name="auto_to_plan_author",
            field=models.<PERSON><PERSON>an<PERSON>ield(default=True),
        ),
        migrations.AlterField(
            model_name="testplanemailsettings",
            name="notify_on_case_update",
            field=models.<PERSON>oleanField(default=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="testplanemailsettings",
            name="notify_on_plan_update",
            field=models.<PERSON><PERSON><PERSON><PERSON>ield(default=True),
        ),
    ]
