{% load i18n %}
{% load static %}

<form class="form-horizontal">
    {% csrf_token %}
    <div class="form-group">
        <label for="id_product" class="col-md-1 col-lg-1">
            {% trans "Product" %}
        </label>
        <div class="col-md-3">
            <select name="product" id="id_product" class="form-control selectpicker" multiple title="----------">
            </select>
        </div>

<div id="version_and_build">
        <label for="id_version" class="col-md-1 col-lg-1">
            {% trans "Version" %}
        </label>
        <div class="col-md-3">
            <select name="version" id="id_version" class="form-control selectpicker" multiple title="----------">
            </select>
        </div>

        <label for="id_build" class="col-md-1 col-lg-1">
            {% trans "Build" %}
        </label>
        <div class="col-md-3">
            <select name="build" id="id_build" class="form-control selectpicker" multiple title="----------">
            </select>
        </div>
</div>
    </div>

    <div class="form-group">
        <label for="id_test_plan" class="col-md-1 col-lg-1">
            {% trans "Test plan" %}
        </label>
        <div class="col-md-3">
            <select name="test_plan" id="id_test_plan" class="form-control selectpicker" multiple title="----------">
            </select>
        </div>

        <label class="col-md-1 col-lg-1" for="id_after">{% trans "After" %}</label>
        <div class="col-md-3 col-lg-3">
            <div class="input-group date-time-picker-pf">
                <input type="text" class="form-control" id="id_after">
                <span class="input-group-addon">
                    <span class="fa fa-calendar"></span>
                </span>
            </div>
        </div>

        <label class="col-md-1 col-lg-1" for="id_before">{% trans "Before" %}</label>
        <div class="col-md-3 col-lg-3">
            <div class="input-group date-time-picker-pf">
                <input type="text" class="form-control" id="id_before">
                <span class="input-group-addon">
                    <span class="fa fa-calendar"></span>
                </span>
            </div>
        </div>
    </div>

    <div class="form-group">
        <label for="id_test_run_summary" class="col-md-1 col-lg-1">
            {% trans "Test run" %}
        </label>
        <div class="col-md-3">
            <input id="id_test_run_summary" type="text" class="form-control"
                placeholder="{% trans 'Test run summary' %}"
                value="{{ form.summary.value|default:'' }}">
        </div>
    </div>
</form>
