version: '2'

services:
    db:
        container_name: kiwi_db
        image: postgres:latest
        volumes:
            - db_data:/var/lib/postgresql/data
        restart: always
        environment:
            POSTGRES_DB: kiwi
            POSTGRES_USER: kiwi
            POSTGRES_PASSWORD: kiwi

    web:
        container_name: kiwi_web
        depends_on:
            - db
        restart: always
        image: pub.kiwitcms.eu/kiwitcms/kiwi:latest
        ports:
            - 80:8080
            - 443:8443
        volumes:
            - uploads:/Kiwi/uploads:Z
        environment:
            KIWI_DB_ENGINE: django.db.backends.postgresql
            KIWI_DB_HOST: db
            KIWI_DB_PORT: 5432
            KIWI_DB_NAME: kiwi
            KIWI_DB_USER: kiwi
            KIWI_DB_PASSWORD: kiwi
        cap_drop:
          - ALL

volumes:
    db_data:
    uploads:
