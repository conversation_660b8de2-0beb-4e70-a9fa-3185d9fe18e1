version: 2

updates:
  - package-ecosystem: pip
    directory: "/"
    schedule:
      interval: daily
      time: "03:00"
    open-pull-requests-limit: 10

  - package-ecosystem: npm
    directory: "/tcms"
    schedule:
      interval: daily
      time: "03:00"
    open-pull-requests-limit: 10

  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      time: "04:00"
    open-pull-requests-limit: 5

  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      time: "04:00"
    open-pull-requests-limit: 5

  - package-ecosystem: "docker"
    directory: "/tests/redmine/"
    schedule:
      interval: "weekly"
      time: "04:00"
    open-pull-requests-limit: 5

  - package-ecosystem: "docker"
    directory: "/tests/bugzilla/"
    schedule:
      interval: "weekly"
      time: "04:00"
    open-pull-requests-limit: 5
