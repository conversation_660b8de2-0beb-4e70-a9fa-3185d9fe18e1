version: '3'

services:
    bugtracker_kiwitcms_org:
        container_name: bugtracker_kiwitcms_org
        image: gitlab/gitlab-ce
        restart: always
        hostname: 'bugtracker.kiwitcms.org'
        environment:
          GITLAB_OMNIBUS_CONFIG: |
            external_url = 'http://bugtracker.kiwitcms.org'
            gitlab_rails['initial_root_password'] = 's3cr3t-Password'
            gitlab_rails['initial_shared_runners_registration_token'] = 'Secret-t0k3n'
            letsencrypt['enable'] = false
            letsencrypt['auto_renew'] = false
        volumes:
          - ./25_api_personal_access_token.rb:/opt/gitlab/embedded/service/gitlab-rails/db/fixtures/production/25_api_personal_access_token.rb
