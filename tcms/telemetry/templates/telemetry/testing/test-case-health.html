{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "TestCase health" %}{% endblock %}
{% block page_id %}page-telemetry-test-case-health{% endblock %}

{% block contents %}
    <div class="container-fluid container-cards-pf">
        {% include "telemetry/include/filters.html" %}

        <h2>{% trans "Most frequently failing test cases" %}</h2>
        <table class="table table-bordered table-with-horizontal-scroll cell-border" id="test-case-health-table">
            <thead>
                <tr>
                    <th class="header"> {% trans "Test Case" %}</th>
                    <th class="header" style="min-width: 70px"> </th>
                    <th class="header"> {% trans "Failed executions" %}</th>
                    <th class="header"> {% trans "% of failed executions" %}</th>
                </tr>
            </thead>
        </table>
    </div>

    <link rel="stylesheet" type="text/css" href="{% static 'telemetry/css/testing/test-case-health.css' %}"/>
{% endblock %}
