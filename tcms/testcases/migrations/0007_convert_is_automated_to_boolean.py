# Generated by Django 2.1.5 on 2019-01-20 19:24

from django.db import migrations, models


def forward_migrate_data(apps, schema_editor):
    test_case_model = apps.get_model("testcases", "TestCase")
    historical_test_case_model = apps.get_model("testcases", "HistoricalTestCase")

    for test_case in test_case_model.objects.all():
        test_case.new_is_automated = test_case.is_automated >= 1
        test_case.save()

    for tc_history in historical_test_case_model.objects.all():
        tc_history.new_is_automated = tc_history.is_automated >= 1
        tc_history.save()


def backward_restore_data(apps, schema_editor):
    test_case_model = apps.get_model("testcases", "TestCase")
    historical_test_case_model = apps.get_model("testcases", "HistoricalTestCase")

    for test_case in test_case_model.objects.all():
        test_case.is_automated = int(test_case.new_is_automated)
        test_case.save()

    for tc_history in historical_test_case_model.objects.all():
        tc_history.is_automated = int(tc_history.new_is_automated)
        tc_history.save()


class Migration(migrations.Migration):
    dependencies = [
        ("testcases", "0006_merge_text_field_into_testcase_model"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicaltestcase",
            name="new_is_automated",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="testcase",
            name="new_is_automated",
            field=models.BooleanField(default=False),
        ),
        migrations.RunPython(forward_migrate_data, backward_restore_data),
        migrations.RemoveField(
            model_name="historicaltestcase",
            name="is_automated",
        ),
        migrations.RemoveField(
            model_name="testcase",
            name="is_automated",
        ),
        migrations.RenameField(
            model_name="historicaltestcase",
            old_name="new_is_automated",
            new_name="is_automated",
        ),
        migrations.RenameField(
            model_name="testcase",
            old_name="new_is_automated",
            new_name="is_automated",
        ),
    ]
