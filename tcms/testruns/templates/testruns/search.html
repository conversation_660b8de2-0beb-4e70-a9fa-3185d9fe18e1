{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Search Test Runs" %}{% endblock %}
{% block page_id %}page-testruns-search{% endblock %}

{% block contents %}
<div class="container-fluid container-cards-pf">
    <form class="form-horizontal" method="get">
        {% csrf_token %}

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_summary">{% trans "Summary" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_summary" type="text" class="form-control"
                    placeholder="{% trans 'Test run summary' %}"
                    value="{{ form.summary.value|default:'' }}">
            </div>

            <label class="col-md-1 col-lg-1" for="id_plan">{% trans "Plan ID" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_plan" type="text" class="form-control" placeholder="{% trans 'TestPlan ID' %}"
                    value="{{ form.plan.value|default:'' }}">
            </div>

            <label class="col-md-1 col-lg-1" for="id_tag">{% trans "Tag" %}</label>
                <div class="col-md-3 col-lg-3">
                    <input id="id_tag" type="text" class="form-control" value="{{ form.tag.value.0|default:'' }}">
                    <p class="help-block">{% trans "Separate multiple values with comma (,)" %}</p>
                </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_product">{% trans "Product" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_product">
                    <option value="">----------</option>
                {% for option in form.product.field.queryset %}
                    <option value="{{ option.pk }}" {% if form.product.value|add:'0' == option.pk %}selected{% endif %}>{{ option.name }}</option>
                {% endfor %}
                </select>
            </div>

            <label class="col-md-1 col-lg-1" for="id_version">{% trans "Version" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_version">
                    <option value="">----------</option>
                {% for option in form.version.field.queryset %}
                    <option value="{{ option.pk }}" {% if form.version.value|add:'0' == option.pk %}selected{% endif %}>{{ option.value }}</option>
                {% endfor %}
                </select>
            </div>

            <label class="col-md-1 col-lg-1" for="id_build">{% trans "Build" %}</label>
            <div class="col-md-3 col-lg-3">
                <select class="form-control selectpicker" id="id_build">
                    <option value="">----------</option>
                {% for option in form.build.field.queryset %}
                    <option value="{{ option.pk }}" {% if form.build.value|add:'0' == option.pk %}selected{% endif %}>{{ option.name }}</option>
                {% endfor %}
                </select>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_manager">{% trans "Manager" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_manager" type="text" class="form-control" placeholder="{% trans 'Username' %}"
                    value="{{ form.manager.value|default:'' }}">
            </div>

            <label class="col-md-1 col-lg-1" for="id_default_tester">{% trans "Default tester" %}</label>
            <div class="col-md-3 col-lg-3">
                <input id="id_default_tester" type="text" class="form-control" placeholder="{% trans 'Username' %}"
                    value="{{ form.default_tester.value|default:'' }}">
            </div>

            <label class="col-md-1 col-lg-1" for="id_running">{% trans "Running" %}</label>
            <div class="col-md-3 col-lg-3">
                <input class="bootstrap-switch" id="id_running" name="running" type="checkbox" {% if form.running.value != "0" %}checked{% endif %} data-on-text="{% trans 'Yes' %}" data-off-text="{% trans 'No' %}">
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1">{% trans "Start date" %}</label>

            <div class="col-md-3 col-lg-3">
                <div class="input-group date-time-picker-pf">
                    <span class="input-group-addon">{% trans "Before" %}</span>
                    <label for="id_before_start_date"></label>
                    <input type="text" class="form-control" id="id_before_start_date">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>

                    <span class="input-group-addon">{% trans "After" %}</span>
                    <label for="id_after_start_date"></label>
                    <input type="text" class="form-control" id="id_after_start_date">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>
                </div>
            </div>

            <label class="col-md-1 col-lg-1">{% trans "Stop date" %}</label>

            <div class="col-md-3 col-lg-3">
                <div class="input-group date-time-picker-pf">
                    <span class="input-group-addon">{% trans "Before" %}</span>
                    <label for="id_before_stop_date"></label>
                    <input type="text" class="form-control" id="id_before_stop_date">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>

                    <span class="input-group-addon">{% trans "After" %}</span>
                    <label for="id_after_stop_date"></label>
                    <input type="text" class="form-control" id="id_after_stop_date">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-1 col-lg-1">{% trans "Planned start" %}</label>

            <div class="col-md-3 col-lg-3">
                <div class="input-group date-time-picker-pf">
                    <span class="input-group-addon">{% trans "Before" %}</span>
                    <label for="id_before_planned_start"></label>
                    <input type="text" class="form-control" id="id_before_planned_start">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>

                    <span class="input-group-addon">{% trans "After" %}</span>
                    <label for="id_after_planned_start"></label>
                    <input type="text" class="form-control" id="id_after_planned_start">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>
                </div>
            </div>

            <label class="col-md-1 col-lg-1">{% trans "Planned stop" %}</label>

            <div class="col-md-3 col-lg-3">
                <div class="input-group date-time-picker-pf">
                    <span class="input-group-addon">{% trans "Before" %}</span>
                    <label for="id_before_planned_stop"></label>
                    <input type="text" class="form-control" id="id_before_planned_stop">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>

                    <span class="input-group-addon">{% trans "After" %}</span>
                    <label for="id_after_planned_stop"></label>
                    <input type="text" class="form-control" id="id_after_planned_stop">
                    <span class="input-group-addon">
                        <span class="fa fa-calendar"></span>
                    </span>
                </div>
            </div>
        </div>

         <div class="form-group">
            <div class="col-md-1 col-lg-1">
                <button id="btn_search" type="submit" class="btn btn-default btn-lg">{% trans "Search" %}</button>
            </div>
         </div>
    </form>

    <div class="panel panel-default">
        <!-- Table HTML -->
        <table class="table table-striped table-bordered table-hover" id="resultsTable">
            <thead>
                <tr>
                    <th>{% trans "ID" %}</th>
                    <th>{% trans "Summary" %}</th>
                    <th>{% trans "Test plan" %}</th>
                    <th>{% trans "Product" %}</th>
                    <th>{% trans "Version" %}</th>
                    <th>{% trans "Build" %}</th>
                    <th>{% trans "Start date" %}</th>
                    <th>{% trans "Stop date" %}</th>
                    <th>{% trans "Manager" %}</th>
                    <th>{% trans "Default tester" %}</th>
                    <th>{% trans "Tags" %}</th>
                </tr>
            </thead>
        </table>

    </div>
</div><!-- /container -->
{% endblock %}
