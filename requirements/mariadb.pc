# these four variables are present in almost every .pc file
prefix=/usr
exec_prefix=${prefix}
libdir=${prefix}/lib64
includedir=${prefix}/include/mysql
# its common to want to know where to install it.
plugindir=${prefix}/lib64/mariadb/plugin
# Below are rarely present or not at all, but we export them regardless
bindir=${prefix}/bin
sbindir=${prefix}/libexec
scriptdir=${prefix}/bin
docdir=${prefix}/share/doc/mariadb
mandir=${prefix}/share/man
sharedir=${prefix}/share
mysqlsharedir=${prefix}/share/mariadb
mysqltestdir=${prefix}/share/mysql-test
socket=/var/lib/mysql/mysql.sock

Name: MariaDB
Description: MariaDB: a very fast and robust SQL database server
URL: http://mariadb.org
Version: 10.5.18
Libs: -L${libdir} -lmariadb -ldl -lm -lssl -lcrypto -lz
Cflags: -I${includedir}
