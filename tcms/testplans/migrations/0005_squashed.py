# Generated by Django 2.1.2 on 2018-10-22 19:59

from django.conf import settings
from django.db import migrations, models

import tcms.core.models.base

plan_types = [
    "Unit",
    "Integration",
    "Function",
    "System",
    "Acceptance",
    "Installation",
    "Performance",
    "Product",
    "Interoperability",
    "Smoke",
    "Regression",
]


TYPE_ID_COLUMN = "type_id"
if settings.DATABASES["default"]["ENGINE"].find("sqlite") > -1:
    TYPE_ID_COLUMN = ""


def forwards_add_initial_data(apps, schema_editor):
    plan_type_model = apps.get_model("testplans", "PlanType")

    plan_type_model.objects.bulk_create(
        [plan_type_model(name=name, description="") for name in plan_types]
    )


def reverse_add_initial_data(apps, schema_editor):
    plan_type_model = apps.get_model("testplans", "PlanType")
    plan_type_model.objects.filter(name__in=plan_types).delete()


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("management", "0003_squashed"),
    ]

    operations = [
        migrations.CreateModel(
            name="TestPlan",
            fields=[
                ("plan_id", models.AutoField(primary_key=True, serialize=False)),
                ("name", models.CharField(db_index=True, max_length=255)),
                ("text", models.TextField(blank=True)),
                (
                    "create_date",
                    models.DateTimeField(auto_now_add=True, db_column="creation_date"),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        db_column="isactive", db_index=True, default=True
                    ),
                ),
                (
                    "extra_link",
                    models.CharField(
                        blank=True, default=None, max_length=1024, null=True
                    ),
                ),
            ],
            bases=(models.Model, tcms.core.models.base.UrlMixin),
        ),
        migrations.CreateModel(
            name="TestPlanEmailSettings",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_active", models.BooleanField(default=False)),
                ("auto_to_plan_owner", models.BooleanField(default=False)),
                ("auto_to_plan_author", models.BooleanField(default=False)),
                ("auto_to_case_owner", models.BooleanField(default=False)),
                ("auto_to_case_default_tester", models.BooleanField(default=False)),
                ("notify_on_plan_update", models.BooleanField(default=False)),
                ("notify_on_case_update", models.BooleanField(default=False)),
                (
                    "plan",
                    models.OneToOneField(
                        on_delete=models.deletion.CASCADE,
                        related_name="email_settings",
                        to="testplans.TestPlan",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="TestPlanTag",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "plan",
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE, to="testplans.TestPlan"
                    ),
                ),
                (
                    "tag",
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE, to="management.Tag"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="PlanType",
            fields=[
                (
                    "id",
                    models.AutoField(
                        db_column=TYPE_ID_COLUMN, primary_key=True, serialize=False
                    ),
                ),
                ("name", models.CharField(max_length=64, unique=True)),
                ("description", models.TextField(blank=True, null=True)),
            ],
            options={
                "ordering": ["name"],
            },
            bases=(models.Model, tcms.core.models.base.UrlMixin),
        ),
        migrations.AddField(
            model_name="testplan",
            name="author",
            field=models.ForeignKey(
                on_delete=models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name="testplan",
            name="owner",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=models.deletion.CASCADE,
                related_name="myplans",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="testplan",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=models.deletion.CASCADE,
                related_name="child_set",
                to="testplans.TestPlan",
            ),
        ),
        migrations.AddField(
            model_name="testplan",
            name="product",
            field=models.ForeignKey(
                on_delete=models.deletion.CASCADE,
                related_name="plan",
                to="management.Product",
            ),
        ),
        migrations.AddField(
            model_name="testplan",
            name="product_version",
            field=models.ForeignKey(
                on_delete=models.deletion.CASCADE,
                related_name="plans",
                to="management.Version",
            ),
        ),
        migrations.AddField(
            model_name="testplan",
            name="tag",
            field=models.ManyToManyField(
                related_name="plan",
                through="testplans.TestPlanTag",
                to="management.Tag",
            ),
        ),
        migrations.AddField(
            model_name="testplan",
            name="type",
            field=models.ForeignKey(
                on_delete=models.deletion.CASCADE, to="testplans.PlanType"
            ),
        ),
        migrations.RunPython(forwards_add_initial_data, reverse_add_initial_data),
        migrations.CreateModel(
            name="HistoricalTestPlan",
            fields=[
                ("plan_id", models.IntegerField(blank=True, db_index=True)),
                ("name", models.CharField(db_index=True, max_length=255)),
                (
                    "create_date",
                    models.DateTimeField(
                        blank=True, db_column="creation_date", editable=False
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        db_column="isactive", db_index=True, default=True
                    ),
                ),
                (
                    "extra_link",
                    models.CharField(
                        blank=True, default=None, max_length=1024, null=True
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_change_reason", models.TextField(null=True)),
                ("history_date", models.DateTimeField()),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "author",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=models.deletion.DO_NOTHING,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=models.deletion.DO_NOTHING,
                        related_name="+",
                        to="testplans.TestPlan",
                    ),
                ),
                (
                    "product",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=models.deletion.DO_NOTHING,
                        related_name="+",
                        to="management.Product",
                    ),
                ),
                (
                    "product_version",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=models.deletion.DO_NOTHING,
                        related_name="+",
                        to="management.Version",
                    ),
                ),
                (
                    "type",
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=models.deletion.DO_NOTHING,
                        related_name="+",
                        to="testplans.PlanType",
                    ),
                ),
                ("text", models.TextField(blank=True)),
            ],
            options={
                "verbose_name": "historical test plan",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": "history_date",
            },
        ),
    ]
