{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Clone TestPlan" %} - {{ test_plan.name }}{% endblock %}
{% block page_id %}page-testplans-mutable{% endblock %}

{% block contents %}
<div class="container-fluid container-cards-pf">
    <form class="form-horizontal" method="post" action="{% url "plans-clone" object.pk %}">
        {% csrf_token %}

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_name">{% trans "Name" %}</label>
            <div class="col-md-3 col-lg-3 {% if form.name.errors %}has-error{% endif %}">
                <input type="text" id="id_name" name="name" value="{{ form.name.value|default:'' }}" class="form-control" required>
                {{ form.name.errors }}
            </div>

            <div class="col-md-1 col-lg-1">
                <label for="id_product">{% trans "Product" %}</label>
                <a href="{% url 'admin:management_product_add' %}?_popup" id="add_id_product" alt="{% trans 'add new Product' %}" title="{% trans 'add new Product' %}">+</a>
            </div>
            <div class="col-md-3 col-lg-3 {% if form.product.errors %}has-error{% endif %}">
                <select id="id_product" name="product" class="form-control selectpicker">
                    {% for product in form.product.field.queryset %}
                        <option value="{{ product.pk }}" {% if product.pk|escape == form.product.value|escape %}selected{% endif %}>
                            {{ product.name }}
                        </option>
                    {% endfor %}
                </select>
                {{ form.product.errors }}
            </div>

            <div class="col-md-1 col-lg-1">
                <label for="id_version">{% trans "Version" %}</label>
                <a href="{% url 'admin:management_version_add' %}?_popup&product={{ form.product.value }}"
                   id="add_id_version" alt="{% trans 'add new Version' %}" title="{% trans 'add new Version' %}">
                    +
                </a>
            </div>
            <div class="col-md-3 col-lg-3 {% if form.version.errors %}has-error{% endif %}">
                <select id="id_version" name="version" class="form-control selectpicker">
                    {% for product_version in form.version.field.queryset %}
                        <option value="{{ product_version.pk }}" {% if product_version.pk|escape == form.version.value|escape %}selected{% endif %}>
                            {{ product_version.value }}
                        </option>
                    {% endfor %}
                </select>
                {{ form.version.errors }}
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-1 col-lg-1" for="id_copy_testcases">{% trans "Clone TCs" %}</label>
            <div class="col-md-3 col-lg-3">
                <input class="bootstrap-switch" id="id_copy_testcases" name="copy_testcases"
                           type="checkbox" {% if form.copy_testcases.value %}checked{% endif %} data-on-text="{% trans 'Yes' %}" data-off-text="{% trans 'No' %}">
                <p class="help-block">{% trans "Clone or link existing TCs into new TP" %}</p>
            </div>


            <label class="col-md-1 col-lg-1" for="id_set_parent">{% trans "Parent TP" %}</label>
            <div class="col-md-3 col-lg-3">
                <input class="bootstrap-switch" id="id_set_parent" name="set_parent"
                       type="checkbox"  {% if form.set_parent.value %}checked{% endif %} data-on-text="{% trans 'Yes' %}" data-off-text="{% trans 'No' %}">
                <p class="help-block">{% trans "Set the source TP as parent of new TP" %}</p>
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-1 col-lg-1">
                <button type="submit" class="btn btn-default btn-lg">{% trans "Clone" %}</button>
            </div>
        </div>
    </form>

</div><!-- /container -->
{% endblock %}
