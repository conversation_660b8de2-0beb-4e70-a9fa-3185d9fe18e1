# Generated by Django 3.0.2 on 2020-01-20 19:58

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    atomic = False

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("testruns", "0008_test_execution_status_color_field"),
    ]

    operations = [
        migrations.RenameField(
            model_name="testexecution",
            old_name="case_run_id",
            new_name="id",
        ),
        migrations.RenameField(
            model_name="historicaltestexecution",
            old_name="case_run_id",
            new_name="id",
        ),
        migrations.AlterField(
            model_name="testexecution",
            name="id",
            field=models.AutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicaltestexecution",
            name="id",
            field=models.IntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name="testruncc",
            name="user",
            field=models.ForeignKey(
                on_delete=models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        # drop TestRun.run_id in favor of TestRun.id
        migrations.AlterUniqueTogether(
            name="testrun",
            unique_together=set(),
        ),
        migrations.RenameField(
            model_name="testrun",
            old_name="run_id",
            new_name="id",
        ),
        migrations.RenameField(
            model_name="historicaltestrun",
            old_name="run_id",
            new_name="id",
        ),
        migrations.AlterField(
            model_name="testrun",
            name="id",
            field=models.AutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="historicaltestrun",
            name="id",
            field=models.IntegerField(
                auto_created=True, blank=True, db_index=True, verbose_name="ID"
            ),
        ),
        migrations.AlterField(
            model_name="testexecutionstatus",
            name="id",
            field=models.AutoField(
                auto_created=True, primary_key=True, serialize=False, verbose_name="ID"
            ),
        ),
    ]
