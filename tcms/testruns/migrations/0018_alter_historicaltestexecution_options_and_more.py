# Generated by Django 4.1.3 on 2022-11-10 09:50

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("testruns", "0017_environment_testrun_property"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="historicaltestexecution",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical test execution",
                "verbose_name_plural": "historical test executions",
            },
        ),
        migrations.AlterModelOptions(
            name="historicaltestrun",
            options={
                "get_latest_by": ("history_date", "history_id"),
                "ordering": ("-history_date", "-history_id"),
                "verbose_name": "historical test run",
                "verbose_name_plural": "historical test runs",
            },
        ),
        migrations.AlterField(
            model_name="historicaltestexecution",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="historicaltestrun",
            name="history_date",
            field=models.DateTimeField(db_index=True),
        ),
    ]
