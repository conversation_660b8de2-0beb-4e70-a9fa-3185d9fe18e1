{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Testing Breakdown" %}{% endblock %}
{% block page_id %}page-telemetry-testing-breakdown{% endblock %}

{% block contents %}
    <div class="container-fluid container-cards-pf">
        {% include "telemetry/include/filters.html" %}

        <div class="col-md-3 kiwi-text-align-center">
            <div id="total-count"></div>
            <label> {% trans "Total" %} </label>

            <div class="progress automated-manual-bar">
                <div class="progress-bar automated-bar" role="progressbar"></div>
                <div class="progress-bar progress-bar-warning manual-bar" role="progressbar"></div>
            </div>

            <div class="bar-legend">
                <div style="align-items: center; display: flex;">
                    <div class="progress-bar"></div>
                    <div class="automated-legend-text">{% trans "Automated" %}</div>
                </div>
                <div style="align-items: center; display: flex;">
                    <div class="progress-bar-warning"></div>
                    <div class="manual-legend-text">{% trans "Manual" %}</div>
                </div>
            </div>
        </div>

        <div class="col-md-4 kiwi-text-align-center">
            <strong>{% trans "Priorities" %}</strong>
            <div id="priorities-chart"></div>
        </div>

        <div class="col-md-4 kiwi-text-align-center">
            <strong>{% trans "Categories" %}</strong>
            <div id="categories-chart"></div>
        </div>
    </div>

    <link rel="stylesheet" type="text/css" href="{% static 'telemetry/css/testing/breakdown.css' %}" />

    <script src="{% static 'c3/c3.min.js' %}"></script>
    <script src="{% static 'd3/d3.min.js' %}"></script>
{% endblock %}
