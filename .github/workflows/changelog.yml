name: changelog

on:
  pull_request:

permissions: read-all

jobs:
  acceptance:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: check-source-branch-and-modified-files
      env:
        HEAD_REF: ${{ github.head_ref }}
        DIFF_URL: ${{ github.event.pull_request.diff_url }}
      run: |
        # fail if curl fails
        set -e

        echo "INFO: source branch is: $HEAD_REF"
        echo "INFO: modified files"
        curl -o- -L "$DIFF_URL" 2>/dev/null | grep "^diff --git"

        set +e

        # if this PR modifies the CHANGELOG file it needs to come from a branch
        # that follows the pattern 'prepare/vX.Y' b/c we want to run some extra jobs for such branches!
        if curl -o- -L "$DIFF_URL" 2>/dev/null | grep "^diff --git" | grep "CHANGELOG"; then
            if [[ ! "$HEAD_REF" =~ ^prepare/v.+$ ]]; then
                echo "FAIL: Modifications to CHANGELOG are only accepted from 'prepate/vX.Y' branches!"
                echo "INFO: Otherwise aarch64 jobs in Circle CI will not be executed."
                exit 1
            fi
        fi
