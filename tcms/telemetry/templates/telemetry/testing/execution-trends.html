{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Execution Trends" %}{% endblock %}
{% block page_id %}page-telemetry-execution-trends{% endblock %}

{% block contents %}
    <div class="container-fluid container-cards-pf main" data-total-key="{% trans 'TOTAL' %}">
        {% include "telemetry/include/filters.html" %}

        <div class="passing-rate-summary">
            <div class="kiwi-font-weight-bold kiwi-text-align-center">{% trans "TOTAL" %}: <span class="total"></span></div>
            <div class="progress">
                <div class="progress-bar progress-bar-success" role="progressbar"></div>
                <div class="progress-bar progress-bar-remaining kiwi-color-black" role="progressbar"></div>
                <div class="progress-bar progress-bar-danger" role="progressbar"></div>
            </div>
            <div class="progress-description" style="display: flex; justify-content: space-between; max-width:100%">
                <div>
                    <span class="pficon pficon-ok"></span><strong>{% trans "Positive" %}: <span class="positive"></span></strong>
                </div>
                <div>
                    <span class="pficon pficon-unknown"></span><strong>{% trans "Neutral" %}: <span class="neutral"></span></strong>
                </div>
                <div>
                    <span class="pficon pficon-error-circle-o"></span><strong>{% trans "Negative" %}: <span class="negative"></span></strong>
                </div>
            </div>
        </div>
        <div id="chart"></div>
    </div>

    <script src="{% static 'c3/c3.min.js' %}"></script>
    <script src="{% static 'd3/d3.min.js' %}"></script>
{% endblock %}
