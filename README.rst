<PERSON><PERSON> TCMS - open source test management
=======================================

.. image:: https://img.shields.io/docker/pulls/kiwitcms/kiwi
    :target: https://kiwitcms.readthedocs.io/en/latest/installing_docker.html
    :alt: Downloads from Docker Hub

.. image:: https://readthedocs.org/projects/kiwitcms/badge/?version=latest
    :target: http://kiwitcms.readthedocs.io/en/latest/?badge=latest
    :alt: Documentation

.. image:: https://d322cqt584bo4o.cloudfront.net/kiwitcms/localized.svg
   :target: https://crowdin.com/project/kiwitcms
   :alt: Translate

.. image:: https://scan.coverity.com/projects/15921/badge.svg
    :target: https://scan.coverity.com/projects/kiwitcms-kiwi
    :alt: Coverity scan

.. image:: https://codecov.io/gh/kiwitcms/Kiwi/branch/master/graph/badge.svg
    :target: https://codecov.io/gh/kiwitcms/Kiwi
    :alt: Code coverage

.. image:: https://api.codeclimate.com/v1/badges/3f4e108ea369f625f112/maintainability
   :target: https://codeclimate.com/github/kiwitcms/Kiwi/maintainability
   :alt: Maintainability

.. image:: https://tidelift.com/badges/package/pypi/kiwitcms
    :target: https://tidelift.com/subscription/pkg/pypi-kiwitcms?utm_source=pypi-kiwitcms&utm_medium=github&utm_campaign=readme
    :alt: Tidelift

.. image:: https://opencollective.com/kiwitcms/tiers/sponsor/badge.svg?label=sponsors&color=brightgreen
   :target: https://opencollective.com/kiwitcms#contributors
   :alt: Become a sponsor

.. image:: https://img.shields.io/twitter/follow/KiwiTCMS.svg
    :target: https://twitter.com/KiwiTCMS
    :alt: Kiwi TCMS on Twitter


Introduction
------------

.. image:: https://raw.githubusercontent.com/kiwitcms/Kiwi/master/tcms/static/images/kiwi_h80.png
   :alt: "Kiwi TCMS Logo"

Kiwi TCMS is the leading open source test management system for both manual and
automated testing. It features bug tracker integration, search pages,
powerful access control, test automation framework plugins, visual reports and
rich API layer.

Our mission is to transform the testing process by making it more organized,
transparent & accountable for everyone on your team; to improve engineering
productivity and participation in testing.


Brief history
-------------

* Feb 2009 - Project created by Red Hat, Inc. under the name Nitrate
* Nov 2014 - Source code published on GitHub without previous history
* Mar 2016 - Mr. Senko starts contributing to upstream
* Jan 2017 - First private release on MrSenko.com including updates to
  Django 1.8.x and a working automated test suite
* May 2017 - Upstream appears to be unresponsive, so
  `fork <http://mrsenko.com/blog/mr-senko/2017/05/26/nitrate-is-now-kiwitestpad/>`_;
  first release which removes hard-coded bug-tracker specifications and
  makes it possible to integrate with external systems
* Aug 2017 - Support for Django 1.11.x; commit to keeping up to
  date with the latest versions of Django
* Sep 2017 - Project name changed to **Kiwi TCMS**; support for Python 3.5,
  started migrating to modern UI using Patternfly
* Oct 2017 - Launched http://kiwitcms.org and https://public.tenant.kiwitcms.org;
  first bug report from external contributor
* Nov 2017 - Pushed ``pub.kiwitcms.eu/kiwitcms/kiwi`` container;
  merged upstream API client sources and modified them to work with the current
  code base
* Jan 2018 - External contributions are now a fact: German translation by
  @xbln; new team member Anton Sankov
* Mar 2018 - First pull request from non-team member
* Apr 2018 - Enabled pylint and fixed 700 issues in the same release; commit to
  eradicate all of the remaining 3000+ issues and improve code quality
* May 2018 - First public appearance at OSCAL Tirana, DjangoCon Heidelberg and
  PyCon CZ Prague
* Nov 2018 - Project info booth at OpenFest Sofia
* Dec 2018 - GitLab integration support - first big code contribution by
  non-team member; more than 5 different external contributors in 2018 alone
* Feb 2019 - Celebrating 10th anniversary with Kiwi TCMS info booth at
  FOSDEM Brussels
* May 2019 - became the first open source TCMS system on `GitHub Marketplace <https://github.com/marketplace/kiwi-tcms/>`_
* Jun 2019 - `OpenAwards winner <http://kiwitcms.org/blog/atodorov/2019/06/24/kiwi-tcms-is-openawards-2019-best-tech-community-winner/>`_
  in 'Best Tech Community' category
* Feb 2020 - Start co-hosting the `Testing and Automation devroom at FOSDEM <https://fosdem-testingautomation.github.io/>`_
* Mar 2020 - `Mozilla Open Source Award winner <https://kiwitcms.org/blog/kiwi-tcms-team/2020/03/27/kiwi-tcms-is-open-source-seed-award-winner/>`_
* May 2020 - Reached 100K+ pulls on Docker Hub
* Mar 2021 - Became **Verified Publisher** on `GitHub Marketplace <https://github.com/marketplace/kiwi-tcms/>`_
* Jul 2021 - Reached 1M+ pulls on Docker Hub
* Nov 2023 - Reached 2M+ pulls on Docker Hub


Check it out
------------

https://public.tenant.kiwitcms.org


Documentation
-------------

http://kiwitcms.readthedocs.org/


Installation
------------

See
`Running Kiwi TCMS as a Docker container <http://kiwitcms.readthedocs.io/en/latest/installing_docker.html>`_.


Language support
----------------

- `Supported languages <https://crowdin.com/project/kiwitcms>`_
- `Request new language <https://github.com/kiwitcms/Kiwi/issues/new?title=Request+new+language:+...&body=Please+enable+...+language+in+Crowdin>`_
- `Translation guide <https://kiwitcms.readthedocs.io/en/latest/contribution.html#translation>`_


Help us improve Kiwi TCMS
-------------------------

- Click the `Star` button at https://github.com/kiwitcms/Kiwi/stargazers
- Click the star icon at https://hub.docker.com/r/kiwitcms/kiwi/
- Follow @KiwiTCMS at https://twitter.com/KiwiTCMS
- Subscribe to our
  `newsletter <https://kiwitcms.us17.list-manage.com/subscribe/post?u=9b57a21155a3b7c655ae8f922&id=c970a37581>`_
- Send us testimonials and feedback about how your team is using Kiwi TCMS
- Donate €5 or more via https://opencollective.com/kiwitcms#section-contribute
- Become a `contributor <http://kiwitcms.readthedocs.org/en/latest/contribution.html>`_


Support
-------

Commercial support for Kiwi TCMS is also available.
For more information see http://kiwitcms.org.
